"""
内容管理模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import <PERSON>olean, Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class ContentType(str, Enum):
    """内容类型枚举"""
    BROADCAST = "broadcast"      # 播报信息
    ANNOUNCEMENT = "announcement"  # 公告
    ABOUT = "about"             # 关于我们
    HELP = "help"               # 帮助文档
    PRIVACY = "privacy"         # 隐私政策
    TERMS = "terms"             # 服务条款


class ContentStatus(str, Enum):
    """内容状态枚举"""
    DRAFT = "draft"             # 草稿
    PUBLISHED = "published"     # 已发布
    ARCHIVED = "archived"       # 已归档


class Content(Base):
    """内容表"""
    __tablename__ = "contents"

    id = Column(Integer, primary_key=True, index=True)
    
    # 基础信息
    title = Column(String(500), nullable=False, comment="标题")
    content = Column(Text, nullable=False, comment="内容")
    content_type = Column(String(50), nullable=False, comment="内容类型")
    
    # 摘要和关键词
    summary = Column(Text, nullable=True, comment="摘要")
    keywords = Column(String(500), nullable=True, comment="关键词")
    
    # 状态信息
    status = Column(String(20), default=ContentStatus.DRAFT, nullable=False, comment="状态")
    is_featured = Column(Boolean, default=False, nullable=False, comment="是否推荐")
    is_top = Column(Boolean, default=False, nullable=False, comment="是否置顶")
    
    # 显示配置
    display_order = Column(Integer, default=0, nullable=False, comment="显示顺序")
    view_count = Column(Integer, default=0, nullable=False, comment="浏览次数")
    
    # 时间配置
    publish_time = Column(DateTime, nullable=True, comment="发布时间")
    expire_time = Column(DateTime, nullable=True, comment="过期时间")
    
    # 管理信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="更新人ID")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by], backref="created_contents")
    updater = relationship("User", foreign_keys=[updated_by], backref="updated_contents")
    
    def __repr__(self):
        return f"<Content(id={self.id}, title='{self.title}', type='{self.content_type}')>"


class BroadcastMessage(Base):
    """播报消息表"""
    __tablename__ = "broadcast_messages"

    id = Column(Integer, primary_key=True, index=True)
    
    # 消息内容
    message = Column(Text, nullable=False, comment="播报消息")
    message_type = Column(String(50), default="info", nullable=False, comment="消息类型")
    
    # 显示配置
    display_duration = Column(Integer, default=15, nullable=False, comment="显示时长(秒)")
    display_order = Column(Integer, default=0, nullable=False, comment="显示顺序")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 时间配置
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    
    # 管理信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # 关系
    creator = relationship("User", backref="broadcast_messages")
    
    def __repr__(self):
        return f"<BroadcastMessage(id={self.id}, message='{self.message[:50]}...', is_active={self.is_active})>"
