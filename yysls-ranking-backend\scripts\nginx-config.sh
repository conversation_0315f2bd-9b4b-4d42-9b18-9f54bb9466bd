#!/bin/bash

# 燕友圈榜单系统 - Nginx配置管理脚本

set -e

echo "🌐 Nginx 配置管理工具"
echo "===================="

# 检查配置文件
check_config() {
    echo "🔍 检查Nginx配置..."
    
    if [ ! -f "nginx/nginx.conf" ]; then
        echo "❌ nginx/nginx.conf 不存在"
        return 1
    fi
    
    if [ ! -d "nginx/conf.d" ]; then
        echo "❌ nginx/conf.d 目录不存在"
        return 1
    fi
    
    # 检查配置语法
    docker run --rm -v $(pwd)/nginx:/etc/nginx:ro nginx:alpine nginx -t
    
    echo "✅ Nginx配置检查通过"
    return 0
}

# 切换环境配置
switch_env() {
    echo ""
    echo "🔄 切换环境配置"
    echo "当前配置文件："
    ls -la nginx/conf.d/*.conf 2>/dev/null || echo "   无配置文件"
    
    echo ""
    echo "选择环境："
    echo "   1) 生产环境 (HTTPS + 限流)"
    echo "   2) 开发环境 (HTTP + CORS)"
    echo ""
    read -p "请选择 (1-2): " env_choice
    
    case $env_choice in
        1)
            echo "切换到生产环境配置..."
            rm -f nginx/conf.d/yysls-dev.conf
            if [ ! -f "nginx/conf.d/yysls.conf" ]; then
                echo "❌ 生产环境配置文件不存在"
                echo "请检查 nginx/conf.d/yysls.conf"
                return 1
            fi
            echo "✅ 已切换到生产环境配置"
            ;;
        2)
            echo "切换到开发环境配置..."
            rm -f nginx/conf.d/yysls.conf
            if [ -f "nginx/conf.d/yysls-dev.conf.example" ]; then
                cp nginx/conf.d/yysls-dev.conf.example nginx/conf.d/yysls-dev.conf
                echo "✅ 已切换到开发环境配置"
            else
                echo "❌ 开发环境配置模板不存在"
                return 1
            fi
            ;;
        *)
            echo "❌ 无效选择"
            return 1
            ;;
    esac
}

# 更新域名
update_domain() {
    echo ""
    read -p "请输入新域名: " new_domain
    
    if [ -z "$new_domain" ]; then
        echo "❌ 域名不能为空"
        return 1
    fi
    
    echo "更新配置文件中的域名..."
    
    # 更新所有配置文件
    find nginx/conf.d -name "*.conf" -exec sed -i "s/your-domain\.com/$new_domain/g" {} \;
    find nginx/conf.d -name "*.conf" -exec sed -i "s/www\.your-domain\.com/www.$new_domain/g" {} \;
    
    echo "✅ 域名已更新为: $new_domain"
}

# 重载配置
reload_nginx() {
    echo ""
    echo "🔄 重载Nginx配置..."
    
    # 检查容器是否运行
    if ! docker ps | grep -q yysls-ranking-nginx; then
        echo "❌ Nginx容器未运行"
        echo "请先启动服务: docker-compose -f docker-compose.prod.yml up -d"
        return 1
    fi
    
    # 测试配置
    docker exec yysls-ranking-nginx nginx -t
    
    # 重载配置
    docker exec yysls-ranking-nginx nginx -s reload
    
    echo "✅ Nginx配置已重载"
}

# 查看日志
view_logs() {
    echo ""
    echo "📋 Nginx日志"
    echo "选择日志类型："
    echo "   1) 访问日志"
    echo "   2) 错误日志"
    echo "   3) 实时日志"
    echo ""
    read -p "请选择 (1-3): " log_choice
    
    case $log_choice in
        1)
            docker exec yysls-ranking-nginx tail -n 50 /var/log/nginx/access.log
            ;;
        2)
            docker exec yysls-ranking-nginx tail -n 50 /var/log/nginx/error.log
            ;;
        3)
            echo "按 Ctrl+C 退出实时日志"
            docker logs -f yysls-ranking-nginx
            ;;
        *)
            echo "❌ 无效选择"
            ;;
    esac
}

# 主菜单
main_menu() {
    while true; do
        echo ""
        echo "请选择操作："
        echo "   1) 检查配置"
        echo "   2) 切换环境"
        echo "   3) 更新域名"
        echo "   4) 重载配置"
        echo "   5) 查看日志"
        echo "   6) 退出"
        echo ""
        read -p "请选择 (1-6): " choice
        
        case $choice in
            1) check_config ;;
            2) switch_env ;;
            3) update_domain ;;
            4) reload_nginx ;;
            5) view_logs ;;
            6) echo "👋 再见！"; exit 0 ;;
            *) echo "❌ 无效选择" ;;
        esac
    done
}

# 如果有参数，直接执行对应功能
case $1 in
    check) check_config ;;
    switch) switch_env ;;
    domain) update_domain ;;
    reload) reload_nginx ;;
    logs) view_logs ;;
    *) main_menu ;;
esac
