#!/usr/bin/env python3
"""
生成Excel模板文件的脚本
"""
import os
import sys
from datetime import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter
except ImportError:
    print("❌ 请先安装openpyxl: pip install openpyxl==3.1.2")
    sys.exit(1)

def create_ranking_template():
    """创建榜单明细Excel模板"""
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = "榜单明细模板"
    
    # 定义列结构
    columns = {
        'rank_start': {'name': '排名开始', 'width': 12},
        'rank_end': {'name': '排名结束', 'width': 12},
        'completion_time': {'name': '完成时间(MM:SS)', 'width': 18},
        'participant_count': {'name': '参与人数', 'width': 12},
        'team_name': {'name': '队伍名称', 'width': 20},
        'team_info': {'name': '队伍信息', 'width': 30}
    }
    
    # 设置样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style="thin"),
        right=Side(style="thin"),
        top=Side(style="thin"),
        bottom=Side(style="thin")
    )
    
    # 写入表头
    col_idx = 1
    for key, config in columns.items():
        cell = worksheet.cell(row=1, column=col_idx)
        cell.value = config['name']
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
        
        # 设置列宽
        worksheet.column_dimensions[get_column_letter(col_idx)].width = config['width']
        col_idx += 1
    
    # 添加示例数据
    example_data = [
        [1, 5, "05:30", 5, "燕友圈战队", "队伍A,队伍B,队伍C,队伍D,队伍E"],
        [6, 10, "06:15", 5, "竞速联盟", "队伍F,队伍G,队伍H,队伍I,队伍J"],
        [11, 15, "07:00", 5, "速度之星", "队伍K,队伍L,队伍M,队伍N,队伍O"],
        [16, 20, "07:45", 5, "闪电小队", "队伍P,队伍Q,队伍R,队伍S,队伍T"],
        [21, 25, "08:30", 5, "极速团队", "队伍U,队伍V,队伍W,队伍X,队伍Y"]
    ]
    
    for row_idx, row_data in enumerate(example_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = worksheet.cell(row=row_idx, column=col_idx)
            cell.value = value
            cell.border = border
            if col_idx <= 4:  # 数字列居中对齐
                cell.alignment = Alignment(horizontal="center")
    
    # 添加数据验证
    # 排名开始和结束必须为正整数
    rank_validation = DataValidation(
        type="whole",
        operator="greaterThan",
        formula1=0,
        showErrorMessage=True,
        errorTitle="输入错误",
        error="排名必须为大于0的整数"
    )
    worksheet.add_data_validation(rank_validation)
    rank_validation.add(f"A2:B1000")
    
    # 参与人数必须为正整数
    count_validation = DataValidation(
        type="whole",
        operator="greaterThan",
        formula1=0,
        showErrorMessage=True,
        errorTitle="输入错误",
        error="参与人数必须为大于0的整数"
    )
    worksheet.add_data_validation(count_validation)
    count_validation.add(f"D2:D1000")
    
    # 添加使用说明工作表
    info_sheet = workbook.create_sheet("使用说明")
    
    # 说明内容
    instructions = [
        ["燕友圈榜单明细Excel模板使用说明", ""],
        ["", ""],
        ["📋 模板说明", ""],
        ["本模板用于批量导入榜单明细数据到燕友圈榜单系统", ""],
        ["", ""],
        ["📊 字段说明", ""],
        ["排名开始", "排名区间的开始值，必须为正整数"],
        ["排名结束", "排名区间的结束值，必须大于等于开始值"],
        ["完成时间(MM:SS)", "完成时间，格式为MM:SS，如05:30表示5分30秒"],
        ["参与人数", "当前时间区间的参与人数，必须为正整数"],
        ["队伍名称", "队伍或团队的名称，如'燕友圈战队'（可选）"],
        ["队伍信息", "队伍详细信息，多个队伍用逗号分隔（可选）"],
        ["", ""],
        ["✅ 数据要求", ""],
        ["1. 排名开始不能大于排名结束", ""],
        ["2. 时间格式必须为MM:SS", ""],
        ["3. 数值字段不能为空", ""],
        ["4. 请勿修改表头名称", ""],
        ["5. 请勿删除示例数据行，可以直接修改", ""],
        ["", ""],
        ["📝 示例数据", ""],
        ["排名开始: 1, 排名结束: 5, 完成时间: 05:30", "表示第1-5名用时5分30秒"],
        ["参与人数: 5, 队伍名称: 燕友圈战队", "表示5个队伍参与，队伍名称为燕友圈战队"],
        ["队伍信息: 队伍A,队伍B,队伍C", "表示具体的队伍详细信息"],
        ["", ""],
        ["⚠️ 注意事项", ""],
        ["- 上传前请检查数据格式是否正确", ""],
        ["- 文件大小不能超过10MB", ""],
        ["- 仅支持.xlsx和.xls格式", ""],
        ["- 建议单次导入数据不超过1000条", ""],
        ["", ""],
        ["🔧 使用流程", ""],
        ["1. 下载本模板文件", ""],
        ["2. 按照格式填写榜单明细数据", ""],
        ["3. 保存文件", ""],
        ["4. 在系统中上传并导入数据", ""],
        ["", ""],
        ["📞 技术支持", ""],
        ["如有问题请联系系统管理员", ""]
    ]
    
    # 写入说明内容
    for row_idx, (col1, col2) in enumerate(instructions, 1):
        info_sheet.cell(row=row_idx, column=1, value=col1)
        info_sheet.cell(row=row_idx, column=2, value=col2)
    
    # 设置说明工作表样式
    info_sheet.column_dimensions['A'].width = 35
    info_sheet.column_dimensions['B'].width = 50
    
    for row in info_sheet.iter_rows():
        for cell in row:
            if cell.row == 1:
                cell.font = Font(bold=True, size=16, color="366092")
            elif cell.value and any(prefix in str(cell.value) for prefix in ["📋", "📊", "✅", "📝", "⚠️", "🔧", "📞"]):
                cell.font = Font(bold=True, size=12, color="366092")
            elif cell.value and cell.value.startswith("- "):
                cell.font = Font(color="666666")
    
    return workbook

def main():
    """主函数"""
    print("🚀 正在生成Excel模板文件...")
    
    try:
        # 创建模板
        workbook = create_ranking_template()
        
        # 保存文件
        template_path = "榜单明细模板.xlsx"
        workbook.save(template_path)
        workbook.close()
        
        print(f"✅ Excel模板文件已生成: {template_path}")
        print(f"📁 文件位置: {os.path.abspath(template_path)}")
        print(f"📊 文件大小: {os.path.getsize(template_path)} 字节")
        
        print("\n📋 模板包含:")
        print("   ✅ 榜单明细模板工作表（包含示例数据）")
        print("   ✅ 使用说明工作表")
        print("   ✅ 数据验证规则")
        print("   ✅ 格式化样式")
        
        print("\n🔧 使用方法:")
        print("   1. 打开生成的Excel文件")
        print("   2. 在'榜单明细模板'工作表中填写数据")
        print("   3. 查看'使用说明'工作表了解详细要求")
        print("   4. 保存文件后上传到系统")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成模板文件失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
