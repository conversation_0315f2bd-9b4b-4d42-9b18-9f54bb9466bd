#!/bin/bash

# 修复 MySQL lower_case_table_names 冲突问题
# 错误：Different lower_case_table_names settings for server ('1') and data dictionary ('0')

set -e

echo "🔧 修复 MySQL lower_case_table_names 冲突"
echo "========================================="
echo ""
echo "错误原因：MySQL 8.0 中 lower_case_table_names 只能在初始化时设置"
echo "解决方案：清除现有数据卷，让 MySQL 重新初始化"
echo ""

# 1. 停止所有服务
echo "1. 停止所有服务..."
docker-compose -f docker-compose.prod.yml down

# 2. 删除 MySQL 容器和数据卷
echo ""
echo "2. 清理 MySQL 数据..."
echo "⚠️  警告：这将删除所有数据库数据！"
read -p "确认继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 1
fi

# 删除容器
docker rm -f yysls-ranking-db 2>/dev/null || true

# 删除数据卷
docker volume rm yysls-ranking-backend_mysql_data 2>/dev/null || true

# 3. 检查并修复 MySQL 配置
echo ""
echo "3. 检查 MySQL 配置..."

# 确保配置文件正确
cat > mysql/conf.d/mysql.cnf << 'EOF'
[mysqld]
# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 表名大小写配置（必须在初始化时设置）
lower_case_table_names=1

# 连接配置
max_connections = 1000
max_connect_errors = 6000

# 时区配置
default-time_zone = '+8:00'

# 认证插件
default-authentication-plugin=mysql_native_password

# SQL模式
sql-mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION

# 性能优化
innodb_buffer_pool_size=128M
innodb_log_file_size=32M

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
EOF

echo "✅ MySQL 配置已更新"

# 4. 重新启动服务
echo ""
echo "4. 重新启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 5. 等待 MySQL 启动
echo ""
echo "5. 等待 MySQL 启动..."
echo "这可能需要几分钟时间..."

for i in {1..60}; do
    if docker-compose -f docker-compose.prod.yml exec -T db mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD} 2>/dev/null; then
        echo "✅ MySQL 启动成功！"
        break
    fi
    echo "等待中... ($i/60)"
    sleep 5
done

# 6. 验证配置
echo ""
echo "6. 验证配置..."
if docker-compose -f docker-compose.prod.yml exec -T db mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SHOW VARIABLES LIKE 'lower_case_table_names';" 2>/dev/null; then
    echo "✅ lower_case_table_names 配置验证成功"
else
    echo "❌ 配置验证失败，查看日志："
    docker-compose -f docker-compose.prod.yml logs db --tail=20
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "注意事项："
echo "1. 所有数据库数据已被清除"
echo "2. 需要重新运行数据库迁移"
echo "3. 需要重新创建管理员用户"
echo ""
echo "下一步操作："
echo "1. 运行数据库迁移: python -m alembic upgrade head"
echo "2. 创建管理员用户: python scripts/init_admin.py"
