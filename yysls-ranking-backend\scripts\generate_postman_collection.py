#!/usr/bin/env python3
"""
Postman集合生成脚本

从API文档生成Postman集合，方便API测试
"""
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_postman_collection() -> Dict[str, Any]:
    """创建Postman集合"""
    
    collection = {
        "info": {
            "name": "燕友圈榜单系统 API",
            "description": "燕友圈榜单系统后端API接口集合",
            "version": "1.0.0",
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "variable": [
            {
                "key": "base_url",
                "value": "http://localhost:8000",
                "type": "string"
            },
            {
                "key": "access_token",
                "value": "",
                "type": "string"
            }
        ],
        "item": []
    }
    
    # 认证模块
    auth_folder = {
        "name": "🔑 认证模块",
        "item": [
            {
                "name": "用户登录",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "username": "admin",
                            "password": "password123"
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{base_url}}/api/v1/auth/login",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "auth", "login"]
                    }
                },
                "event": [
                    {
                        "listen": "test",
                        "script": {
                            "exec": [
                                "if (pm.response.code === 200) {",
                                "    const response = pm.response.json();",
                                "    if (response.data && response.data.access_token) {",
                                "        pm.collectionVariables.set('access_token', response.data.access_token);",
                                "        console.log('Access token saved:', response.data.access_token);",
                                "    }",
                                "}"
                            ]
                        }
                    }
                ]
            },
            {
                "name": "微信登录",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "code": "wx_auth_code_from_frontend"
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{base_url}}/api/v1/auth/wechat-login",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "auth", "wechat-login"]
                    }
                }
            },
            {
                "name": "获取当前用户信息",
                "request": {
                    "method": "GET",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/auth/me",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "auth", "me"]
                    }
                }
            },
            {
                "name": "刷新Token",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/auth/refresh",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "auth", "refresh"]
                    }
                }
            },
            {
                "name": "用户登出",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/auth/logout",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "auth", "logout"]
                    }
                }
            }
        ]
    }
    
    # 榜单管理模块
    rankings_folder = {
        "name": "📊 榜单管理",
        "item": [
            {
                "name": "获取榜单列表",
                "request": {
                    "method": "GET",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/rankings?page=1&size=10",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "rankings"],
                        "query": [
                            {
                                "key": "page",
                                "value": "1"
                            },
                            {
                                "key": "size",
                                "value": "10"
                            },
                            {
                                "key": "status",
                                "value": "active",
                                "disabled": True
                            },
                            {
                                "key": "ranking_type",
                                "value": "five_person",
                                "disabled": True
                            }
                        ]
                    }
                }
            },
            {
                "name": "创建榜单",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        },
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "name": "第1期5人竞速榜",
                            "period": 1,
                            "ranking_type": "five_person",
                            "start_time": "2024-01-20T09:00:00Z",
                            "end_time": "2024-01-20T18:00:00Z",
                            "team_size_limit": 5
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{base_url}}/api/v1/rankings",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "rankings"]
                    }
                }
            },
            {
                "name": "获取榜单详情",
                "request": {
                    "method": "GET",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/rankings/1",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "rankings", "1"]
                    }
                }
            },
            {
                "name": "更新榜单",
                "request": {
                    "method": "PUT",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        },
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "name": "第1期5人竞速榜（更新）",
                            "status": "active"
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{base_url}}/api/v1/rankings/1",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "rankings", "1"]
                    }
                }
            },
            {
                "name": "删除榜单",
                "request": {
                    "method": "DELETE",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/rankings/1",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "rankings", "1"]
                    }
                }
            }
        ]
    }
    
    # 用户管理模块
    users_folder = {
        "name": "👥 用户管理",
        "item": [
            {
                "name": "获取用户列表",
                "request": {
                    "method": "GET",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Bearer {{access_token}}"
                        }
                    ],
                    "url": {
                        "raw": "{{base_url}}/api/v1/users?page=1&size=10",
                        "host": ["{{base_url}}"],
                        "path": ["api", "v1", "users"],
                        "query": [
                            {
                                "key": "page",
                                "value": "1"
                            },
                            {
                                "key": "size",
                                "value": "10"
                            }
                        ]
                    }
                }
            }
        ]
    }
    
    # 系统健康检查
    health_folder = {
        "name": "🔍 系统健康检查",
        "item": [
            {
                "name": "根路径检查",
                "request": {
                    "method": "GET",
                    "url": {
                        "raw": "{{base_url}}/",
                        "host": ["{{base_url}}"],
                        "path": [""]
                    }
                }
            },
            {
                "name": "健康检查",
                "request": {
                    "method": "GET",
                    "url": {
                        "raw": "{{base_url}}/health",
                        "host": ["{{base_url}}"],
                        "path": ["health"]
                    }
                }
            }
        ]
    }
    
    # 添加所有文件夹到集合
    collection["item"] = [
        auth_folder,
        rankings_folder,
        users_folder,
        health_folder
    ]
    
    return collection


def main():
    """主函数"""
    print("🚀 生成Postman集合...")
    
    # 创建集合
    collection = create_postman_collection()
    
    # 保存到文件
    docs_dir = project_root / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    output_file = docs_dir / "postman_collection.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(collection, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Postman集合已生成: {output_file}")
    print("\n使用方法:")
    print("1. 打开Postman")
    print("2. 点击 Import")
    print("3. 选择生成的 postman_collection.json 文件")
    print("4. 导入后可以直接使用集合中的请求进行API测试")
    print("\n注意:")
    print("- 请先运行登录接口获取access_token")
    print("- access_token会自动保存到集合变量中")
    print("- 可以修改base_url变量来切换不同环境")


if __name__ == "__main__":
    main()
