#!/usr/bin/env python3
"""
验证数据库结构是否与SQLAlchemy模型匹配
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, inspect
from app.config import get_settings
from app.models.ranking import RankingDetail

def verify_ranking_details_structure():
    """验证ranking_details表结构"""
    settings = get_settings()
    
    # 创建数据库连接
    engine = create_engine(settings.database_url)
    inspector = inspect(engine)
    
    # 获取表的列信息
    try:
        columns = inspector.get_columns('ranking_details')
        column_names = [col['name'] for col in columns]
        
        print("当前数据库中ranking_details表的字段:")
        for col in columns:
            nullable = "NULL" if col['nullable'] else "NOT NULL"
            print(f"  - {col['name']}: {col['type']} {nullable}")
        
        print("\nSQLAlchemy模型中定义的字段:")
        model_columns = [
            'id', 'ranking_id', 'rank_start', 'rank_end', 
            'completion_time', 'completion_seconds', 'participant_count', 
            'team_info', 'team_name', 'created_at', 'updated_at'
        ]
        
        for col in model_columns:
            print(f"  - {col}")
        
        # 检查缺失的字段
        missing_in_db = set(model_columns) - set(column_names)
        extra_in_db = set(column_names) - set(model_columns)
        
        if missing_in_db:
            print(f"\n❌ 数据库中缺失的字段: {missing_in_db}")
        
        if extra_in_db:
            print(f"\n⚠️  数据库中多余的字段: {extra_in_db}")
        
        if not missing_in_db and not extra_in_db:
            print("\n✅ 数据库结构与模型完全匹配!")
        
        # 特别检查team_name字段
        if 'team_name' in column_names:
            team_name_col = next(col for col in columns if col['name'] == 'team_name')
            print(f"\n✅ team_name字段已存在: {team_name_col['type']} {'NULL' if team_name_col['nullable'] else 'NOT NULL'}")
        else:
            print("\n❌ team_name字段不存在，需要添加")
            
    except Exception as e:
        print(f"❌ 检查数据库结构时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== 数据库结构验证 ===")
    verify_ranking_details_structure()
