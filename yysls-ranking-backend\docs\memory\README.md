# 项目记忆文档目录

本目录包含燕友圈榜单系统项目的完整记忆文档，用于记录项目的发展历程、技术决策和重要信息。

## 文档结构

### 📋 [project_memory.md](./project_memory.md)
**项目核心记忆文档**
- 项目概述和背景
- 已完成任务清单
- 项目架构说明
- 技术选型说明
- 核心业务逻辑
- 待完成任务列表
- 开发环境配置
- 下一步计划

### 🏗️ [architecture_decisions.md](./architecture_decisions.md)
**架构决策记录 (ADR)**
- ADR-001: 选择FastAPI作为Web框架
- ADR-002: 采用服务层模式架构
- ADR-003: 使用PostgreSQL作为主数据库
- ADR-004: 集成微信登录系统
- ADR-005: 使用SQLAlchemy 2.0异步ORM
- ADR-006: 实现配置管理系统
- ADR-007: 榜单系统设计
- 未来的架构决策计划

### 📝 [development_log.md](./development_log.md)
**开发日志**
- 项目启动阶段记录
- 数据库设计阶段记录
- 架构重构阶段记录
- 核心功能实现记录
- 开发经验总结
- 下一阶段计划
- 项目里程碑
- 团队协作规范

## 文档使用指南

### 🎯 快速了解项目
如果你是新加入的开发者或需要快速了解项目现状，建议按以下顺序阅读：
1. 先读 `project_memory.md` 了解项目整体情况
2. 再读 `architecture_decisions.md` 了解技术选型原因
3. 最后读 `development_log.md` 了解开发历程

### 🔍 查找特定信息
- **项目架构**: 查看 `project_memory.md` 的"项目架构"部分
- **技术选型原因**: 查看 `architecture_decisions.md` 的相关ADR
- **开发历程**: 查看 `development_log.md` 的时间线记录
- **待办事项**: 查看 `project_memory.md` 的"待完成的任务"部分

### 📚 文档维护
- **更新频率**: 每完成一个重要功能或做出重要决策时更新
- **维护责任**: 项目开发者共同维护
- **更新原则**: 保持信息准确、及时、完整

## 项目当前状态

### ✅ 已完成
- 项目架构设计与初始化
- 数据库设计与建模  
- 核心业务模型开发（服务层）

### 🔄 进行中
- API接口层开发

### ⏳ 待开始
- 用户认证与权限系统
- 系统配置管理功能
- 内容管理系统开发
- API文档生成
- 单元测试与集成测试
- Linux部署方案设计
- 部署文档与运行说明

## 重要提醒

### 🚨 关键信息
- **架构模式**: 采用服务层模式，不是简单的CRUD模式
- **数据库**: 使用PostgreSQL + SQLAlchemy 2.0异步
- **Web框架**: FastAPI
- **认证方式**: 支持传统登录 + 微信登录

### 💡 开发注意事项
- 所有业务逻辑应放在服务层，不要在API层写业务逻辑
- 使用异步编程模式，注意事务管理
- 遵循项目的代码规范和文档标准
- 重要决策需要记录到ADR文档中

### 🔧 环境要求
- Python 3.9+
- PostgreSQL 12+
- 详细依赖见 `requirements.txt`

## 联系信息

**项目团队**: 燕友圈工作室  
**技术栈**: Python + FastAPI + PostgreSQL  
**项目版本**: v1.0.0  
**文档版本**: v1.0  
**最后更新**: 2024年当前日期

---

*这些文档是项目的重要资产，请妥善维护和更新。*
