#!/usr/bin/env python3
"""
测试应用启动是否正常
"""
import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("测试应用启动...")

try:
    from app.main import create_application
    print("✓ 成功导入 create_application")
    
    app = create_application()
    print("✓ 成功创建 FastAPI 应用实例")
    
    print("✓ 应用启动测试通过！")
    
except Exception as e:
    print(f"✗ 应用启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n应用启动测试完成!")
