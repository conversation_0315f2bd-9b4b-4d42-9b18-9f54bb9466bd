/**
 * 微信小程序登录示例
 * 
 * 使用新的 jscode2session 接口进行登录
 */

// 配置
const API_BASE_URL = 'http://localhost:8000/api/v1';

/**
 * 微信小程序登录
 */
function loginWithMiniprogram() {
    console.log('开始微信小程序登录...');
    
    // 1. 调用微信登录接口获取code
    wx.login({
        success: function(loginRes) {
            if (loginRes.code) {
                console.log('获取到登录凭证:', loginRes.code);
                
                // 2. 将code发送到后端进行登录
                wx.request({
                    url: `${API_BASE_URL}/auth/wechat-miniprogram-login`,
                    method: 'POST',
                    data: {
                        code: loginRes.code
                    },
                    header: {
                        'Content-Type': 'application/json'
                    },
                    success: function(res) {
                        console.log('登录响应:', res.data);
                        
                        if (res.data.code === 200) {
                            // 登录成功
                            const tokenData = res.data.data;
                            const accessToken = tokenData.access_token;
                            const userInfo = tokenData.user;
                            
                            console.log('登录成功!');
                            console.log('访问令牌:', accessToken);
                            console.log('用户信息:', userInfo);
                            
                            // 保存token到本地存储
                            wx.setStorageSync('access_token', accessToken);
                            wx.setStorageSync('user_info', userInfo);
                            
                            // 显示成功消息
                            wx.showToast({
                                title: '登录成功',
                                icon: 'success',
                                duration: 2000
                            });
                            
                            // 可以在这里跳转到主页面
                            // wx.switchTab({
                            //     url: '/pages/index/index'
                            // });
                            
                        } else {
                            // 登录失败
                            console.error('登录失败:', res.data.message);
                            wx.showToast({
                                title: res.data.message || '登录失败',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    },
                    fail: function(err) {
                        console.error('请求失败:', err);
                        wx.showToast({
                            title: '网络请求失败',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                });
                
            } else {
                console.error('获取登录凭证失败:', loginRes.errMsg);
                wx.showToast({
                    title: '获取登录凭证失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        fail: function(err) {
            console.error('微信登录失败:', err);
            wx.showToast({
                title: '微信登录失败',
                icon: 'none',
                duration: 2000
            });
        }
    });
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
    const accessToken = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');
    
    if (accessToken && userInfo) {
        console.log('用户已登录');
        console.log('用户信息:', userInfo);
        return true;
    } else {
        console.log('用户未登录');
        return false;
    }
}

/**
 * 使用token调用API
 */
function callApiWithToken(url, data = {}, method = 'GET') {
    const accessToken = wx.getStorageSync('access_token');
    
    if (!accessToken) {
        console.error('未找到访问令牌，请先登录');
        wx.showToast({
            title: '请先登录',
            icon: 'none',
            duration: 2000
        });
        return;
    }
    
    wx.request({
        url: `${API_BASE_URL}${url}`,
        method: method,
        data: data,
        header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        },
        success: function(res) {
            console.log('API调用成功:', res.data);
            
            if (res.data.code === 401) {
                // token过期，需要重新登录
                console.log('访问令牌已过期，请重新登录');
                wx.removeStorageSync('access_token');
                wx.removeStorageSync('user_info');
                
                wx.showToast({
                    title: '登录已过期，请重新登录',
                    icon: 'none',
                    duration: 2000
                });
                
                // 可以在这里跳转到登录页面
                // wx.redirectTo({
                //     url: '/pages/login/login'
                // });
            }
        },
        fail: function(err) {
            console.error('API调用失败:', err);
        }
    });
}

/**
 * 退出登录
 */
function logout() {
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('user_info');
    
    console.log('已退出登录');
    wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 2000
    });
}

// 导出函数供其他页面使用
module.exports = {
    loginWithMiniprogram,
    checkLoginStatus,
    callApiWithToken,
    logout
};

// 使用示例：
// 
// 在页面的onLoad中检查登录状态：
// onLoad: function() {
//     if (!checkLoginStatus()) {
//         // 显示登录按钮或自动登录
//         this.setData({
//             showLoginButton: true
//         });
//     }
// }
//
// 在登录按钮的点击事件中：
// onLoginButtonTap: function() {
//     loginWithMiniprogram();
// }
//
// 调用需要认证的API：
// getUserRankings: function() {
//     callApiWithToken('/rankings/user', {}, 'GET');
// }
