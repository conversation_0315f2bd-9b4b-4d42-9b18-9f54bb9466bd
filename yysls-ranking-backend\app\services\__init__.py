"""
业务服务层

采用服务层模式（Service Layer Pattern），将业务逻辑从API层分离出来。
服务层负责：
1. 业务逻辑处理
2. 数据验证和转换
3. 事务管理
4. 跨模型的复杂操作
5. 外部服务集成

每个服务类都应该：
- 继承自BaseService基类
- 实现具体的业务逻辑方法
- 处理异常和错误
- 记录日志
"""

from app.services.base import BaseService
from app.services.user_service import UserService
from app.services.ranking_service import RankingService
from app.services.sponsor_service import SponsorService
from app.services.system_config_service import SystemConfigService
from app.services.content_service import ContentService

__all__ = [
    "BaseService",
    "UserService", 
    "RankingService",
    "SponsorService",
    "SystemConfigService",
    "ContentService",
]
