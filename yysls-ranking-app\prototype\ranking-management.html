<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 榜单管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 60px 20px 140px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 表单标签样式 */
        .form-label {
            color: #D4AF37;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }
        
        /* 表单输入框样式 */
        .form-input {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        .form-input::placeholder {
            color: rgba(245, 245, 220, 0.5);
        }
        
        /* 日期输入框样式 */
        .date-input {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .date-input:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 文件上传区域样式 */
        .upload-area {
            border: 2px dashed rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            background: rgba(74, 74, 74, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .upload-area:hover {
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.2);
        }
        
        .upload-area.dragover {
            border-color: rgba(212, 175, 55, 0.8);
            background: rgba(212, 175, 55, 0.1);
        }
        
        .upload-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            color: #D4AF37;
            stroke: currentColor;
        }
        
        .upload-text {
            color: #D4AF37;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            color: rgba(245, 245, 220, 0.6);
            font-size: 14px;
        }
        
        /* 文件信息显示 */
        .file-info {
            display: none;
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 12px;
        }
        
        .file-name {
            color: #D4AF37;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .file-size {
            color: rgba(245, 245, 220, 0.7);
            font-size: 14px;
        }
        
        /* 进度条样式 */
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(74, 74, 74, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #D4AF37 0%, #B8941F 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            transform: translateY(-2px);
        }
        
        .btn-primary:disabled {
            background: rgba(74, 74, 74, 0.5);
            color: rgba(245, 245, 220, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 响应式设计 */
        @media (max-width: 640px) {
            .main-content {
                padding: 60px 16px 140px 16px;
            }
            
            .upload-area {
                padding: 30px 16px;
            }
            
            .upload-icon {
                width: 40px;
                height: 40px;
            }
        }
        
        @media (max-width: 768px) {
            .date-range {
                grid-template-columns: 1fr !important;
                gap: 16px !important;
            }
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 榜单管理表单 -->
        <div class="ink-card p-6 scroll-animate">
            <div class="text-center mb-8">
                <img src="https://unpkg.com/lucide-static@latest/icons/trophy.svg" alt="榜单管理"
                     class="w-12 h-12 mx-auto mb-4" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h1 class="text-2xl font-bold gold-text mb-2">榜单管理</h1>
                <p class="text-gray-400 text-sm">创建和管理江湖排行榜</p>
            </div>
            
            <form id="rankingForm" class="space-y-6">
                <!-- 榜单标题 -->
                <div>
                    <label class="form-label" for="title">榜单标题 *</label>
                    <input type="text" id="title" name="title" class="form-input" 
                           placeholder="请输入榜单标题，如：2024年度武林高手排行榜" required>
                </div>
                
                <!-- 榜单副标题 -->
                <div>
                    <label class="form-label" for="subtitle">榜单副标题</label>
                    <input type="text" id="subtitle" name="subtitle" class="form-input"
                           placeholder="请输入榜单副标题（可选）">
                </div>

                <!-- 时间范围选择 -->
                <div>
                    <label class="form-label">榜单有效期 *</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 date-range">
                        <div class="date-input-group">
                            <label class="form-label text-sm" for="startDate">开始时间</label>
                            <input type="datetime-local" id="startDate" name="startDate" class="date-input" required>
                        </div>
                        <div class="date-input-group">
                            <label class="form-label text-sm" for="endDate">结束时间</label>
                            <input type="datetime-local" id="endDate" name="endDate" class="date-input" required>
                        </div>
                    </div>
                </div>

                <!-- Excel文件上传 -->
                <div>
                    <label class="form-label">榜单数据文件 *</label>
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                        <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <div class="upload-text">点击上传或拖拽文件到此处</div>
                        <div class="upload-hint">支持 .xlsx 和 .xls 格式，文件大小不超过 10MB</div>
                        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="handleFileSelect(event)">
                    </div>
                    <div class="file-info" id="fileInfo">
                        <div class="file-name" id="fileName"></div>
                        <div class="file-size" id="fileSize"></div>
                        <div class="progress-bar" id="progressBar" style="display: none;">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="pt-4">
                    <button type="submit" class="btn-primary" id="submitBtn">
                        <span id="submitText">创建榜单</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 榜单管理页面控制器
        class RankingManagement {
            constructor() {
                this.selectedFile = null;
                this.isSubmitting = false;
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();

                // 初始化标签栏
                this.initTabbar();

                // 初始化拖拽上传
                this.initDragAndDrop();

                // 初始化表单验证
                this.initFormValidation();

                // 设置默认时间
                this.setDefaultDates();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                // 观察所有需要动画的元素
                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 初始化拖拽上传
            initDragAndDrop() {
                const uploadArea = document.getElementById('uploadArea');

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleFile(files[0]);
                    }
                });
            }

            // 设置默认时间
            setDefaultDates() {
                const now = new Date();
                const startDate = new Date(now);
                const endDate = new Date(now);
                endDate.setDate(endDate.getDate() + 30); // 默认30天后结束

                // 格式化为 datetime-local 格式
                const formatDateTime = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    return `${year}-${month}-${day}T${hours}:${minutes}`;
                };

                document.getElementById('startDate').value = formatDateTime(startDate);
                document.getElementById('endDate').value = formatDateTime(endDate);
            }

            // 处理文件选择
            handleFile(file) {
                // 验证文件类型
                const allowedTypes = [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                    'application/vnd.ms-excel' // .xls
                ];

                if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                    this.showToast('请选择 Excel 文件（.xlsx 或 .xls 格式）', 'error');
                    return;
                }

                // 验证文件大小（10MB）
                const maxSize = 10 * 1024 * 1024;
                if (file.size > maxSize) {
                    this.showToast('文件大小不能超过 10MB', 'error');
                    return;
                }

                this.selectedFile = file;
                this.displayFileInfo(file);
                this.simulateUpload();
            }

            // 显示文件信息
            displayFileInfo(file) {
                const fileInfo = document.getElementById('fileInfo');
                const fileName = document.getElementById('fileName');
                const fileSize = document.getElementById('fileSize');

                fileName.textContent = file.name;
                fileSize.textContent = this.formatFileSize(file.size);
                fileInfo.style.display = 'block';
            }

            // 格式化文件大小
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 模拟文件上传进度
            simulateUpload() {
                const progressBar = document.getElementById('progressBar');
                const progressFill = document.getElementById('progressFill');

                progressBar.style.display = 'block';
                progressFill.style.width = '0%';

                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        setTimeout(() => {
                            progressBar.style.display = 'none';
                            this.showToast('文件上传成功', 'success');
                        }, 500);
                    }
                    progressFill.style.width = progress + '%';
                }, 200);
            }

            // 初始化表单验证
            initFormValidation() {
                const form = document.getElementById('rankingForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleSubmit();
                });

                // 实时验证结束时间
                const startDateInput = document.getElementById('startDate');
                const endDateInput = document.getElementById('endDate');

                const validateDates = () => {
                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);

                    if (startDate && endDate && endDate <= startDate) {
                        endDateInput.setCustomValidity('结束时间必须晚于开始时间');
                    } else {
                        endDateInput.setCustomValidity('');
                    }
                };

                startDateInput.addEventListener('change', validateDates);
                endDateInput.addEventListener('change', validateDates);
            }

            // 处理表单提交
            async handleSubmit() {
                if (this.isSubmitting) return;

                // 表单验证
                const title = document.getElementById('title').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                if (!title) {
                    this.showToast('请输入榜单标题', 'error');
                    return;
                }

                if (!startDate || !endDate) {
                    this.showToast('请选择榜单有效期', 'error');
                    return;
                }

                if (new Date(endDate) <= new Date(startDate)) {
                    this.showToast('结束时间必须晚于开始时间', 'error');
                    return;
                }

                if (!this.selectedFile) {
                    this.showToast('请上传榜单数据文件', 'error');
                    return;
                }

                this.isSubmitting = true;
                this.updateSubmitButton(true);

                try {
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 收集表单数据
                    const formData = {
                        title: title,
                        subtitle: document.getElementById('subtitle').value.trim(),
                        startDate: startDate,
                        endDate: endDate,
                        file: this.selectedFile
                    };

                    console.log('提交榜单数据:', formData);

                    this.showToast('榜单创建成功！', 'success');

                    // 重置表单
                    setTimeout(() => {
                        this.resetForm();
                    }, 1500);

                } catch (error) {
                    console.error('创建榜单失败:', error);
                    this.showToast('创建失败，请稍后重试', 'error');
                } finally {
                    this.isSubmitting = false;
                    this.updateSubmitButton(false);
                }
            }

            // 更新提交按钮状态
            updateSubmitButton(isLoading) {
                const submitBtn = document.getElementById('submitBtn');
                const submitText = document.getElementById('submitText');

                if (isLoading) {
                    submitBtn.disabled = true;
                    submitText.textContent = '创建中...';
                } else {
                    submitBtn.disabled = false;
                    submitText.textContent = '创建榜单';
                }
            }

            // 重置表单
            resetForm() {
                document.getElementById('rankingForm').reset();
                this.selectedFile = null;
                document.getElementById('fileInfo').style.display = 'none';
                this.setDefaultDates();
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                // 根据类型设置样式
                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');

                // 等待iframe加载完成
                tabbarIframe.addEventListener('load', () => {
                    // 向标签栏发送消息，设置当前活动标签为"我的"
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }
        }

        // 全局函数
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                rankingManagement.handleFile(file);
            }
        }

        function goBack() {
            window.history.back();
        }

        // 全局变量
        let rankingManagement;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            rankingManagement = new RankingManagement();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 允许来自同源或iframe的消息
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
