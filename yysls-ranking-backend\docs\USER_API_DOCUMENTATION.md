# 用户管理 API 接口文档

## 概述

用户管理模块提供用户注册、登录、信息管理等功能的 API 接口。

## 基础信息

- **基础路径**: `/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 数据模型

### 用户性别枚举 (UserGender)

| 枚举值 | 说明 |
|--------|------|
| `男` | 男性 |
| `女` | 女性 |
| `不愿意透露` | 不愿意透露 |

### 用户角色枚举 (UserRole)

| 枚举值 | 说明 |
|--------|------|
| `user` | 普通用户 |
| `admin` | 管理员 |
| `super_admin` | 超级管理员 |

### 用户基础信息模型

```json
{
  "id": 1,
  "username": "testuser",
  "nickname": "测试用户",
  "avatar_url": "https://example.com/avatar.jpg",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "role": "user",
  "is_active": true,
  "is_verified": true,
  "bio": "个人简介",
  "level": "江湖新人",
  "points": 100,
  "location": "北京市",
  "user_number": "USER001",
  "gender": "男",
  "age": 25,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "last_login_at": "2024-01-01T00:00:00"
}
```

## 认证相关接口

### 1. 用户注册

**接口地址**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户",
  "phone": "13800138000",
  "location": "上海市",
  "gender": "女",
  "age": 28
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "newuser",
      "nickname": "新用户",
      "email": "<EMAIL>",
      "role": "user",
      "gender": "女",
      "age": 28
    }
  },
  "timestamp": "2024-01-01T00:00:00"
}
```

### 2. 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "gender": "男",
      "role": "user"
    }
  },
  "timestamp": "2024-01-01T00:00:00"
}
```

### 3. 微信登录

**接口地址**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "wx_auth_code_from_frontend"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "微信登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "wx_user_123",
      "nickname": "微信用户",
      "avatar_url": "https://wx.qlogo.cn/...",
      "gender": "不愿意透露",
      "role": "user"
    }
  },
  "timestamp": "2024-01-01T00:00:00"
}
```

## 用户信息管理接口

### 4. 获取当前用户信息

**接口地址**: `GET /users/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "avatar_url": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "bio": "这是我的个人简介",
    "level": "江湖新人",
    "points": 150,
    "location": "北京市海淀区",
    "user_number": "USER001",
    "gender": "男",
    "age": 25,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "last_login_at": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5. 更新当前用户信息

**接口地址**: `PUT /users/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求参数**:
```json
{
  "nickname": "更新后的昵称",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "phone": "13900139000",
  "bio": "更新后的个人简介",
  "location": "上海市浦东新区",
  "gender": "女",
  "age": 30
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "更新后的昵称",
    "avatar_url": "https://example.com/new-avatar.jpg",
    "phone": "13900139000",
    "bio": "更新后的个人简介",
    "location": "上海市浦东新区",
    "gender": "女",
    "age": 30,
    "updated_at": "2024-01-01T13:00:00"
  },
  "timestamp": "2024-01-01T13:00:00"
}
```

## 用户管理接口（管理员权限）

### 6. 获取用户列表

**接口地址**: `GET /users`

**请求头**:
```
Authorization: Bearer <admin_access_token>
```

**查询参数**:
- `page`: 页码（默认：1）
- `size`: 每页大小（默认：10，最大：100）
- `role`: 用户角色筛选（可选）
- `is_active`: 激活状态筛选（可选）
- `search`: 搜索关键词（可选）

**请求示例**:
```
GET /users?page=1&size=10&role=user&search=测试
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "testuser1",
        "nickname": "测试用户1",
        "email": "<EMAIL>",
        "role": "user",
        "is_active": true,
        "gender": "男",
        "age": 25,
        "created_at": "2024-01-01T00:00:00"
      },
      {
        "id": 2,
        "username": "testuser2",
        "nickname": "测试用户2",
        "email": "<EMAIL>",
        "role": "user",
        "is_active": true,
        "gender": "女",
        "age": 28,
        "created_at": "2024-01-01T01:00:00"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 10,
    "pages": 5
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 7. 获取用户详情

**接口地址**: `GET /users/{user_id}`

**请求头**:
```
Authorization: Bearer <access_token>
```

**路径参数**:
- `user_id`: 用户ID

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "avatar_url": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "bio": "个人简介",
    "level": "江湖新人",
    "points": 100,
    "location": "北京市",
    "user_number": "USER001",
    "gender": "男",
    "age": 25,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00",
    "last_login_at": "2024-01-01T00:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 8. 更新用户信息（管理员）

**接口地址**: `PUT /users/{user_id}`

**请求头**:
```
Authorization: Bearer <admin_access_token>
```

**路径参数**:
- `user_id`: 用户ID

**请求参数**:
```json
{
  "nickname": "管理员更新的昵称",
  "role": "admin",
  "is_active": false,
  "gender": "不愿意透露",
  "age": 35
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "管理员更新的昵称",
    "role": "admin",
    "is_active": false,
    "gender": "不愿意透露",
    "age": 35,
    "updated_at": "2024-01-01T13:00:00"
  },
  "timestamp": "2024-01-01T13:00:00"
}
```

### 9. 创建用户（管理员）

**接口地址**: `POST /users`

**请求头**:
```
Authorization: Bearer <admin_access_token>
```

**请求参数**:
```json
{
  "username": "newadminuser",
  "email": "<EMAIL>",
  "password": "adminpassword123",
  "nickname": "管理员用户",
  "role": "admin",
  "phone": "13800138001",
  "location": "深圳市",
  "gender": "女",
  "age": 32
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建用户成功",
  "data": {
    "id": 10,
    "username": "newadminuser",
    "email": "<EMAIL>",
    "nickname": "管理员用户",
    "role": "admin",
    "is_active": true,
    "is_verified": true,
    "phone": "13800138001",
    "location": "深圳市",
    "gender": "女",
    "age": 32,
    "created_at": "2024-01-01T14:00:00"
  },
  "timestamp": "2024-01-01T14:00:00"
}
```

## 错误响应格式

### 通用错误响应

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权（token无效或过期） |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如用户名已存在） |
| 500 | 服务器内部错误 |

### 字段验证错误示例

```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "detail": [
      {
        "loc": ["gender"],
        "msg": "性别必须是以下值之一: 男, 女, 不愿意透露",
        "type": "value_error"
      },
      {
        "loc": ["age"],
        "msg": "年龄必须在0-150之间",
        "type": "value_error"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 注意事项

1. **性别字段更新**: 性别枚举值已从英文更新为中文，请使用新的枚举值：`男`、`女`、`不愿意透露`
2. **权限控制**: 普通用户只能查看和修改自己的信息，管理员可以管理所有用户
3. **Token有效期**: 访问令牌默认有效期为30分钟
4. **分页查询**: 支持分页查询，默认每页10条记录，最大100条
5. **搜索功能**: 支持按用户名、昵称进行模糊搜索
6. **数据验证**: 所有输入数据都会进行严格的格式和范围验证

## 更新日志

- **2024-08-01**: 更新性别字段枚举值为中文，移除 `other` 选项
- **2024-07-01**: 添加用户个人资料字段（位置、用户编号、性别、年龄）
- **2024-06-01**: 初始版本发布
