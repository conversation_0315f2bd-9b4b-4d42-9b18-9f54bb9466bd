<template>
	<view>
		<view class="top-navbar"
			:class="{ 'top-navbar--fixed': fixed }"
			:style="navbarStyle">

			<!-- 状态栏占位 -->
			<fui-status-bar v-if="statusBar"></fui-status-bar>

			<!-- 导航栏内容 -->
			<view class="navbar-content"
				:style="{
					height: navBarHeight + 'px'
				}">

				<!-- 主标题行 - 改为插槽 -->
				<view class="navbar-header" :style="{ height: navBarHeight + 'px' }">
					<slot name="content">
						
					</slot>
				</view>
			</view>

			
		</view>
		<!-- 占位块 - 确保页面内容不会向上移动 -->
		<view class="navbar-placeholder" v-if="fixed" :style="{ height: (statusBarHeight + navBarHeight) + 'px' }"></view>
	</view>
</template>

<script setup>
import { computed } from 'vue'
import { useSystemInfo } from '@/hooks/useSystemInfo.js'

// Props 定义
const props = defineProps({
	// 样式配置
	background: {
		type: String,
		default: 'transparent'
	},
	blur: {
		type: Boolean,
		default: false
	},
	shadow: {
		type: Boolean,
		default: false
	},

	// 系统适配
	statusBar: {
		type: Boolean,
		default: true
	},
	fixed: {
		type: Boolean,
		default: true
	},
	zIndex: {
		type: [Number, String],
		default: 999
	}
})

// 事件定义
const emit = defineEmits(['title-click'])

// 使用系统信息 hook
const { navBarHeight, statusBarHeight, getSystemInfo } = useSystemInfo()

// 初始化系统信息
getSystemInfo()

// 计算导航栏样式
const navbarStyle = computed(() => {
	const styles = {
		background: props.background,
		zIndex: props.zIndex
	}

	if (props.blur) {
		styles.backdropFilter = 'blur(20rpx)'
	}

	if (props.shadow) {
		styles.boxShadow = '0 2rpx 16rpx rgba(0, 0, 0, 0.1)'
	}

	return styles
})

// 事件处理
const handleTitleClick = () => {
	emit('title-click', props.title)
}
</script>

<style lang="scss" scoped>
.top-navbar {
	&--fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
	}
}

.navbar-content {
	.navbar-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 48rpx;
	}
}

.navbar-placeholder {
	/* 占位块样式 - 确保页面内容不会向上移动 */
	width: 100%;
}
</style>
