# 燕友圈榜单系统 - 数据库SQL文档

## 📋 文档概述

本文档提供燕友圈榜单系统的完整数据库SQL脚本，包含所有数据表的创建语句、索引、约束和初始化数据。

**数据库类型**: MySQL 8.0+
**字符集**: utf8mb4
**排序规则**: utf8mb4_unicode_ci
**时区**: Asia/Shanghai
**文档生成时间**: 2024年当前日期

## 🗄️ 数据库结构概览

| 表名 | 描述 | 主要字段 | 关联表 |
|------|------|----------|--------|
| users | 用户表 | id, username, email, wechat_openid | - |
| rankings | 榜单表 | id, title, ranking_type, status | ranking_details |
| ranking_details | 榜单明细表 | id, ranking_id, participant_name | rankings |
| sponsors | 赞助商表 | id, name, logo_url, sort_order | - |
| system_configs | 系统配置表 | id, config_key, config_value | - |
| contents | 内容表 | id, content_type, title, content | - |
| broadcast_messages | 播报消息表 | id, message, is_active | - |

## 📊 完整建表SQL脚本

### 1. 用户表 (users)

```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    nickname VARCHAR(100) NOT NULL DEFAULT '',
    avatar_url VARCHAR(500),
    role VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    wechat_openid VARCHAR(100) UNIQUE,
    wechat_unionid VARCHAR(100),

    -- 新增用户信息字段
    location VARCHAR(200),
    user_number VARCHAR(50) UNIQUE,
    gender VARCHAR(20) CHECK (gender IN ('男', '女', '不愿意透露')),
    age INTEGER CHECK (age >= 0 AND age <= 150),

    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_wechat_openid ON users(wechat_openid);
CREATE INDEX idx_users_user_number ON users(user_number);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 用户表注释
COMMENT ON TABLE users IS '用户表';
COMMENT ON COLUMN users.id IS '用户ID';
COMMENT ON COLUMN users.username IS '用户名';
COMMENT ON COLUMN users.email IS '邮箱';
COMMENT ON COLUMN users.password_hash IS '密码哈希';
COMMENT ON COLUMN users.nickname IS '昵称';
COMMENT ON COLUMN users.avatar_url IS '头像URL';
COMMENT ON COLUMN users.role IS '用户角色：user-普通用户，admin-管理员';
COMMENT ON COLUMN users.is_active IS '是否激活';
COMMENT ON COLUMN users.is_verified IS '是否已验证';
COMMENT ON COLUMN users.wechat_openid IS '微信OpenID';
COMMENT ON COLUMN users.wechat_unionid IS '微信UnionID';
COMMENT ON COLUMN users.location IS '所在地';
COMMENT ON COLUMN users.user_number IS '用户编号';
COMMENT ON COLUMN users.gender IS '性别：male-男性，female-女性，other-其他，prefer_not_to_say-不愿透露';
COMMENT ON COLUMN users.age IS '年龄';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';
```

### 2. 榜单表 (rankings)

```sql
-- 榜单表
CREATE TABLE rankings (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    ranking_type VARCHAR(20) NOT NULL CHECK (ranking_type IN ('five_person', 'ten_person')),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'finished')),
    max_participants INTEGER NOT NULL,
    current_participants INTEGER NOT NULL DEFAULT 0,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 榜单表索引
CREATE INDEX idx_rankings_ranking_type ON rankings(ranking_type);
CREATE INDEX idx_rankings_status ON rankings(status);
CREATE INDEX idx_rankings_created_at ON rankings(created_at);
CREATE INDEX idx_rankings_start_time ON rankings(start_time);

-- 榜单表注释
COMMENT ON TABLE rankings IS '榜单表';
COMMENT ON COLUMN rankings.id IS '榜单ID';
COMMENT ON COLUMN rankings.title IS '榜单标题';
COMMENT ON COLUMN rankings.description IS '榜单描述';
COMMENT ON COLUMN rankings.ranking_type IS '榜单类型：five_person-5人榜单，ten_person-10人榜单';
COMMENT ON COLUMN rankings.status IS '榜单状态：draft-草稿，in_progress-进行中，finished-已结束';
COMMENT ON COLUMN rankings.max_participants IS '最大参与人数';
COMMENT ON COLUMN rankings.current_participants IS '当前参与人数';
COMMENT ON COLUMN rankings.start_time IS '开始时间';
COMMENT ON COLUMN rankings.end_time IS '结束时间';
COMMENT ON COLUMN rankings.created_at IS '创建时间';
COMMENT ON COLUMN rankings.updated_at IS '更新时间';
```

### 3. 榜单明细表 (ranking_details)

```sql
-- 榜单明细表
CREATE TABLE ranking_details (
    id SERIAL PRIMARY KEY,
    ranking_id INTEGER NOT NULL REFERENCES rankings(id) ON DELETE CASCADE,
    participant_name VARCHAR(100) NOT NULL,
    completion_seconds INTEGER,
    rank_range VARCHAR(20),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 榜单明细表索引
CREATE INDEX idx_ranking_details_ranking_id ON ranking_details(ranking_id);
CREATE INDEX idx_ranking_details_completion_seconds ON ranking_details(completion_seconds);
CREATE INDEX idx_ranking_details_created_at ON ranking_details(created_at);

-- 榜单明细表注释
COMMENT ON TABLE ranking_details IS '榜单明细表';
COMMENT ON COLUMN ranking_details.id IS '明细ID';
COMMENT ON COLUMN ranking_details.ranking_id IS '榜单ID';
COMMENT ON COLUMN ranking_details.participant_name IS '参与者姓名';
COMMENT ON COLUMN ranking_details.completion_seconds IS '完成时间（秒）';
COMMENT ON COLUMN ranking_details.rank_range IS '排名范围';
COMMENT ON COLUMN ranking_details.notes IS '备注';
COMMENT ON COLUMN ranking_details.created_at IS '创建时间';
COMMENT ON COLUMN ranking_details.updated_at IS '更新时间';
```

### 4. 赞助商表 (sponsors)

```sql
-- 赞助商表
CREATE TABLE sponsors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    logo_url VARCHAR(500),
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 赞助商表索引
CREATE INDEX idx_sponsors_sort_order ON sponsors(sort_order);
CREATE INDEX idx_sponsors_is_active ON sponsors(is_active);
CREATE INDEX idx_sponsors_created_at ON sponsors(created_at);

-- 赞助商表注释
COMMENT ON TABLE sponsors IS '赞助商表';
COMMENT ON COLUMN sponsors.id IS '赞助商ID';
COMMENT ON COLUMN sponsors.name IS '赞助商名称';
COMMENT ON COLUMN sponsors.logo_url IS 'Logo URL（用作头像）';
COMMENT ON COLUMN sponsors.sort_order IS '排序顺序';
COMMENT ON COLUMN sponsors.is_active IS '是否启用';
COMMENT ON COLUMN sponsors.created_at IS '创建时间';
COMMENT ON COLUMN sponsors.updated_at IS '更新时间';
```

### 5. 系统配置表 (system_configs)

```sql
-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (config_type IN ('string', 'integer', 'boolean', 'json')),
    description VARCHAR(500),
    is_public BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表索引
CREATE INDEX idx_system_configs_config_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_config_type ON system_configs(config_type);
CREATE INDEX idx_system_configs_is_public ON system_configs(is_public);

-- 系统配置表注释
COMMENT ON TABLE system_configs IS '系统配置表';
COMMENT ON COLUMN system_configs.id IS '配置ID';
COMMENT ON COLUMN system_configs.config_key IS '配置键';
COMMENT ON COLUMN system_configs.config_value IS '配置值';
COMMENT ON COLUMN system_configs.config_type IS '配置类型：string-字符串，integer-整数，boolean-布尔值，json-JSON';
COMMENT ON COLUMN system_configs.description IS '配置描述';
COMMENT ON COLUMN system_configs.is_public IS '是否公开（前端可访问）';
COMMENT ON COLUMN system_configs.created_at IS '创建时间';
COMMENT ON COLUMN system_configs.updated_at IS '更新时间';
```

### 6. 内容表 (contents)

```sql
-- 内容表
CREATE TABLE contents (
    id SERIAL PRIMARY KEY,
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('announcement', 'about', 'privacy', 'terms')),
    title VARCHAR(200) NOT NULL,
    content TEXT,
    is_published BOOLEAN NOT NULL DEFAULT false,
    publish_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 内容表索引
CREATE INDEX idx_contents_content_type ON contents(content_type);
CREATE INDEX idx_contents_is_published ON contents(is_published);
CREATE INDEX idx_contents_publish_at ON contents(publish_at);
CREATE INDEX idx_contents_created_at ON contents(created_at);

-- 内容表注释
COMMENT ON TABLE contents IS '内容表';
COMMENT ON COLUMN contents.id IS '内容ID';
COMMENT ON COLUMN contents.content_type IS '内容类型：announcement-公告，about-关于我们，privacy-隐私政策，terms-服务条款';
COMMENT ON COLUMN contents.title IS '标题';
COMMENT ON COLUMN contents.content IS '内容';
COMMENT ON COLUMN contents.is_published IS '是否已发布';
COMMENT ON COLUMN contents.publish_at IS '发布时间';
COMMENT ON COLUMN contents.created_at IS '创建时间';
COMMENT ON COLUMN contents.updated_at IS '更新时间';
```

### 7. 播报消息表 (broadcast_messages)

```sql
-- 播报消息表
CREATE TABLE broadcast_messages (
    id SERIAL PRIMARY KEY,
    message TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'error')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 播报消息表索引
CREATE INDEX idx_broadcast_messages_is_active ON broadcast_messages(is_active);
CREATE INDEX idx_broadcast_messages_message_type ON broadcast_messages(message_type);
CREATE INDEX idx_broadcast_messages_start_time ON broadcast_messages(start_time);
CREATE INDEX idx_broadcast_messages_end_time ON broadcast_messages(end_time);
CREATE INDEX idx_broadcast_messages_created_at ON broadcast_messages(created_at);

-- 播报消息表注释
COMMENT ON TABLE broadcast_messages IS '播报消息表';
COMMENT ON COLUMN broadcast_messages.id IS '消息ID';
COMMENT ON COLUMN broadcast_messages.message IS '消息内容';
COMMENT ON COLUMN broadcast_messages.message_type IS '消息类型：info-信息，warning-警告，success-成功，error-错误';
COMMENT ON COLUMN broadcast_messages.is_active IS '是否激活';
COMMENT ON COLUMN broadcast_messages.start_time IS '开始时间';
COMMENT ON COLUMN broadcast_messages.end_time IS '结束时间';
COMMENT ON COLUMN broadcast_messages.created_at IS '创建时间';
COMMENT ON COLUMN broadcast_messages.updated_at IS '更新时间';
```

## 🔧 触发器和函数

### 更新时间自动更新触发器

```sql
-- 创建更新时间自动更新函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rankings_updated_at BEFORE UPDATE ON rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ranking_details_updated_at BEFORE UPDATE ON ranking_details
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sponsors_updated_at BEFORE UPDATE ON sponsors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contents_updated_at BEFORE UPDATE ON contents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_broadcast_messages_updated_at BEFORE UPDATE ON broadcast_messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 📝 初始化数据

### 1. 系统管理员用户

```sql
-- 插入系统管理员用户（密码：admin123）
INSERT INTO users (username, email, password_hash, nickname, role, is_active, is_verified) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '系统管理员', 'admin', true, true);
```

### 2. 系统配置初始化

```sql
-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
-- 基础配置
('site_name', '燕友圈榜单系统', 'string', '网站名称', true),
('site_description', '专业的竞速榜单管理系统', 'string', '网站描述', true),
('site_keywords', '榜单,竞速,排名,燕友圈', 'string', '网站关键词', true),

-- 功能开关
('enable_registration', 'true', 'boolean', '是否开放用户注册', false),
('enable_wechat_login', 'true', 'boolean', '是否启用微信登录', false),
('enable_broadcast', 'true', 'boolean', '是否启用播报功能', true),

-- 榜单配置
('max_five_person_rankings', '10', 'integer', '5人榜单最大数量', false),
('max_ten_person_rankings', '5', 'integer', '10人榜单最大数量', false),
('ranking_auto_finish_hours', '24', 'integer', '榜单自动结束时间（小时）', false),

-- 邮件配置
('smtp_host', 'smtp.gmail.com', 'string', 'SMTP服务器地址', false),
('smtp_port', '587', 'integer', 'SMTP端口', false),
('smtp_username', '', 'string', 'SMTP用户名', false),
('smtp_password', '', 'string', 'SMTP密码', false),
('from_email', '<EMAIL>', 'string', '发件人邮箱', false),
('from_name', '燕友圈榜单系统', 'string', '发件人名称', false),

-- 微信配置
('wechat_app_id', '', 'string', '微信应用ID', false),
('wechat_app_secret', '', 'string', '微信应用密钥', false),

-- 上传配置
('upload_max_size', '10485760', 'integer', '文件上传最大大小（字节）', false),
('upload_allowed_types', '["jpg", "jpeg", "png", "gif"]', 'json', '允许上传的文件类型', false),

-- 安全配置
('jwt_expire_minutes', '30', 'integer', 'JWT过期时间（分钟）', false),
('password_min_length', '6', 'integer', '密码最小长度', false),
('login_max_attempts', '5', 'integer', '登录最大尝试次数', false);
```

### 3. 示例内容数据

```sql
-- 插入示例内容
INSERT INTO contents (content_type, title, content, is_published, publish_at) VALUES
('announcement', '系统上线公告', '燕友圈榜单系统正式上线，欢迎大家使用！', true, CURRENT_TIMESTAMP),
('about', '关于我们', '燕友圈榜单系统是一个专业的竞速榜单管理平台，致力于为用户提供最优质的榜单服务。', true, CURRENT_TIMESTAMP),
('privacy', '隐私政策', '我们重视您的隐私保护...', true, CURRENT_TIMESTAMP),
('terms', '服务条款', '使用本系统即表示您同意以下条款...', true, CURRENT_TIMESTAMP);
```

### 4. 示例赞助商数据

```sql
-- 插入示例赞助商
INSERT INTO sponsors (name, logo_url, sort_order, is_active) VALUES
('赞助商A', 'https://example.com/logo-a.jpg', 1, true),
('赞助商B', 'https://example.com/logo-b.jpg', 2, true);
```

### 5. 示例播报消息

```sql
-- 插入示例播报消息
INSERT INTO broadcast_messages (message, message_type, is_active, start_time, end_time) VALUES
('欢迎使用燕友圈榜单系统！', 'info', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
('系统将于今晚22:00-23:00进行维护，请提前保存数据。', 'warning', false, NULL, NULL);
```

## 🔄 数据库迁移脚本

### 创建数据库和用户

```sql
-- 创建数据库
CREATE DATABASE yysls_ranking
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'zh_CN.UTF-8'
    LC_CTYPE = 'zh_CN.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 创建应用用户
CREATE USER yysls_user WITH PASSWORD 'your_password_here';

-- 授权
GRANT CONNECT ON DATABASE yysls_ranking TO yysls_user;
GRANT USAGE ON SCHEMA public TO yysls_user;
GRANT CREATE ON SCHEMA public TO yysls_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yysls_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yysls_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO yysls_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO yysls_user;
```

## 📋 完整部署脚本

### 一键部署脚本 (deploy_database.sql)

```sql
-- 燕友圈榜单系统数据库部署脚本
-- 执行前请确保已创建数据库和用户

\c yysls_ranking;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建更新时间函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 执行所有建表语句（此处省略，实际使用时需要包含上面的所有CREATE TABLE语句）

-- 执行所有触发器创建语句

-- 插入初始化数据

-- 完成提示
SELECT 'Database deployment completed successfully!' AS status;
```
