#!/usr/bin/env python3
"""
数据库SQL脚本生成器

从SQLAlchemy模型自动生成数据库建表SQL脚本
"""
import sys
from pathlib import Path
from sqlalchemy import create_engine, MetaData
from sqlalchemy.schema import CreateTable, CreateIndex
from sqlalchemy.dialects import postgresql

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import Base
from app.models import *  # 导入所有模型


def generate_create_tables_sql():
    """生成建表SQL脚本"""
    
    # 创建PostgreSQL引擎（仅用于SQL生成，不实际连接）
    engine = create_engine("postgresql://user:pass@localhost/db", echo=False)
    
    sql_statements = []
    
    # 添加文件头注释
    sql_statements.append("-- 燕友圈榜单系统数据库建表脚本")
    sql_statements.append("-- 生成时间: 2024年当前日期")
    sql_statements.append("-- 数据库类型: PostgreSQL 15+")
    sql_statements.append("-- 字符集: UTF-8")
    sql_statements.append("")
    sql_statements.append("-- 设置时区")
    sql_statements.append("SET timezone = 'Asia/Shanghai';")
    sql_statements.append("")
    
    # 生成建表语句
    for table in Base.metadata.sorted_tables:
        # 生成CREATE TABLE语句
        create_table_sql = str(CreateTable(table).compile(
            engine, 
            compile_kwargs={"literal_binds": True}
        )).strip()
        
        sql_statements.append(f"-- {table.comment or table.name}表")
        sql_statements.append(create_table_sql + ";")
        sql_statements.append("")
        
        # 生成索引语句
        for index in table.indexes:
            create_index_sql = str(CreateIndex(index).compile(
                engine,
                compile_kwargs={"literal_binds": True}
            )).strip()
            sql_statements.append(create_index_sql + ";")
        
        if table.indexes:
            sql_statements.append("")
    
    return "\n".join(sql_statements)


def generate_initial_data_sql():
    """生成初始化数据SQL脚本"""
    
    sql_statements = []
    
    # 添加文件头注释
    sql_statements.append("-- 燕友圈榜单系统初始化数据脚本")
    sql_statements.append("-- 生成时间: 2024年当前日期")
    sql_statements.append("")
    
    # 系统管理员用户
    sql_statements.append("-- 插入系统管理员用户（密码：admin123）")
    sql_statements.append("""INSERT INTO users (username, email, password_hash, nickname, role, is_active, is_verified) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '系统管理员', 'admin', true, true)
ON CONFLICT (username) DO NOTHING;""")
    sql_statements.append("")
    
    # 系统配置
    sql_statements.append("-- 插入系统配置")
    system_configs = [
        ("site_name", "燕友圈榜单系统", "string", "网站名称", "true"),
        ("site_description", "专业的竞速榜单管理系统", "string", "网站描述", "true"),
        ("enable_registration", "true", "boolean", "是否开放用户注册", "false"),
        ("enable_wechat_login", "true", "boolean", "是否启用微信登录", "false"),
        ("enable_broadcast", "true", "boolean", "是否启用播报功能", "true"),
        ("max_five_person_rankings", "10", "integer", "5人榜单最大数量", "false"),
        ("max_ten_person_rankings", "5", "integer", "10人榜单最大数量", "false"),
        ("jwt_expire_minutes", "30", "integer", "JWT过期时间（分钟）", "false"),
        ("password_min_length", "6", "integer", "密码最小长度", "false"),
        ("upload_max_size", "10485760", "integer", "文件上传最大大小（字节）", "false"),
    ]
    
    for config_key, config_value, config_type, description, is_public in system_configs:
        sql_statements.append(f"""INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('{config_key}', '{config_value}', '{config_type}', '{description}', {is_public})
ON CONFLICT (config_key) DO NOTHING;""")
    
    sql_statements.append("")
    
    # 示例内容
    sql_statements.append("-- 插入示例内容")
    contents = [
        ("announcement", "系统上线公告", "燕友圈榜单系统正式上线，欢迎大家使用！"),
        ("about", "关于我们", "燕友圈榜单系统是一个专业的竞速榜单管理平台。"),
        ("privacy", "隐私政策", "我们重视您的隐私保护..."),
        ("terms", "服务条款", "使用本系统即表示您同意以下条款..."),
    ]
    
    for content_type, title, content in contents:
        sql_statements.append(f"""INSERT INTO contents (content_type, title, content, is_published, publish_at) VALUES
('{content_type}', '{title}', '{content}', true, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;""")
    
    sql_statements.append("")
    
    # 示例赞助商
    sql_statements.append("-- 插入示例赞助商")
    sponsors = [
        ("赞助商A", "https://example.com/logo-a.jpg", 1, True),
        ("赞助商B", "https://example.com/logo-b.jpg", 2, True),
    ]

    for name, logo_url, sort_order, is_active in sponsors:
        sql_statements.append(f"""INSERT INTO sponsors (name, logo_url, sort_order, is_active) VALUES
('{name}', '{logo_url}', {sort_order}, {str(is_active).lower()});""")
    
    sql_statements.append("")
    
    # 示例播报消息
    sql_statements.append("-- 插入示例播报消息")
    sql_statements.append("""INSERT INTO broadcast_messages (message, message_type, is_active, start_time, end_time) VALUES
('欢迎使用燕友圈榜单系统！', 'info', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
('系统功能持续完善中，感谢您的支持！', 'success', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '7 days');""")
    
    return "\n".join(sql_statements)


def generate_triggers_sql():
    """生成触发器SQL脚本"""
    
    sql_statements = []
    
    # 添加文件头注释
    sql_statements.append("-- 燕友圈榜单系统触发器脚本")
    sql_statements.append("-- 生成时间: 2024年当前日期")
    sql_statements.append("")
    
    # 更新时间自动更新函数
    sql_statements.append("-- 创建更新时间自动更新函数")
    sql_statements.append("""CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';""")
    sql_statements.append("")
    
    # 为所有表添加更新时间触发器
    tables_with_updated_at = [
        'users', 'rankings', 'ranking_details', 'sponsors', 
        'system_configs', 'contents', 'broadcast_messages'
    ]
    
    sql_statements.append("-- 为所有表添加更新时间触发器")
    for table_name in tables_with_updated_at:
        trigger_name = f"update_{table_name}_updated_at"
        sql_statements.append(f"""CREATE TRIGGER {trigger_name} BEFORE UPDATE ON {table_name}
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();""")
    
    return "\n".join(sql_statements)


def generate_deployment_sql():
    """生成完整部署SQL脚本"""
    
    sql_statements = []
    
    # 添加文件头注释
    sql_statements.append("-- 燕友圈榜单系统完整部署脚本")
    sql_statements.append("-- 生成时间: 2024年当前日期")
    sql_statements.append("-- 使用方法: psql -U postgres -d yysls_ranking -f deploy_complete.sql")
    sql_statements.append("")
    
    # 设置
    sql_statements.append("-- 设置时区和编码")
    sql_statements.append("SET timezone = 'Asia/Shanghai';")
    sql_statements.append("SET client_encoding = 'UTF8';")
    sql_statements.append("")
    
    # 开始事务
    sql_statements.append("-- 开始事务")
    sql_statements.append("BEGIN;")
    sql_statements.append("")
    
    # 包含其他脚本内容
    sql_statements.append("-- 创建触发器函数")
    sql_statements.append(generate_triggers_sql().split('\n')[4:])  # 跳过头部注释
    sql_statements.append("")
    
    sql_statements.append("-- 创建数据表")
    sql_statements.append(generate_create_tables_sql().split('\n')[6:])  # 跳过头部注释
    sql_statements.append("")
    
    sql_statements.append("-- 插入初始化数据")
    sql_statements.append(generate_initial_data_sql().split('\n')[3:])  # 跳过头部注释
    sql_statements.append("")
    
    # 提交事务
    sql_statements.append("-- 提交事务")
    sql_statements.append("COMMIT;")
    sql_statements.append("")
    sql_statements.append("-- 部署完成")
    sql_statements.append("SELECT 'Database deployment completed successfully!' AS status;")
    
    # 扁平化列表
    flat_statements = []
    for item in sql_statements:
        if isinstance(item, list):
            flat_statements.extend(item)
        else:
            flat_statements.append(item)
    
    return "\n".join(flat_statements)


def main():
    """主函数"""
    print("🚀 开始生成数据库SQL脚本...")
    
    try:
        # 创建输出目录
        sql_dir = project_root / "sql"
        sql_dir.mkdir(exist_ok=True)
        
        # 生成建表脚本
        create_tables_sql = generate_create_tables_sql()
        with open(sql_dir / "01_create_tables.sql", "w", encoding="utf-8") as f:
            f.write(create_tables_sql)
        print("✅ 建表脚本已生成: sql/01_create_tables.sql")
        
        # 生成触发器脚本
        triggers_sql = generate_triggers_sql()
        with open(sql_dir / "02_create_triggers.sql", "w", encoding="utf-8") as f:
            f.write(triggers_sql)
        print("✅ 触发器脚本已生成: sql/02_create_triggers.sql")
        
        # 生成初始化数据脚本
        initial_data_sql = generate_initial_data_sql()
        with open(sql_dir / "03_initial_data.sql", "w", encoding="utf-8") as f:
            f.write(initial_data_sql)
        print("✅ 初始化数据脚本已生成: sql/03_initial_data.sql")
        
        # 生成完整部署脚本
        deployment_sql = generate_deployment_sql()
        with open(sql_dir / "deploy_complete.sql", "w", encoding="utf-8") as f:
            f.write(deployment_sql)
        print("✅ 完整部署脚本已生成: sql/deploy_complete.sql")
        
        print("\n🎉 数据库SQL脚本生成完成！")
        print("\n📁 生成的文件:")
        print("  - sql/01_create_tables.sql - 建表脚本")
        print("  - sql/02_create_triggers.sql - 触发器脚本")
        print("  - sql/03_initial_data.sql - 初始化数据脚本")
        print("  - sql/deploy_complete.sql - 完整部署脚本")
        print("\n💡 使用说明:")
        print("  - 分步执行: 按顺序执行 01、02、03 脚本")
        print("  - 一键部署: 执行 deploy_complete.sql 脚本")
        print("  - 命令示例: psql -U postgres -d yysls_ranking -f sql/deploy_complete.sql")
        
    except Exception as e:
        print(f"❌ 生成SQL脚本时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
