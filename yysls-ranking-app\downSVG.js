const fs = require('fs');
const https = require('https');
const path = require('path');

// 图标列表
const icons = [
    'arrow-left', 'sword', 'tag', 'sparkles', 'trophy', 'users', 'star', 
    'clock', 'heart', 'shield-check', 'file-text', 'chevron-right', 'lock', 
    'message-circle', 'mail', 'phone', 'megaphone', 'eye', 'user', 'volume-2', 
    'crown', 'calendar', 'handshake', 'camera', 'settings', 'info', 'search', 
    'coins', 'zap', 'menu', 'shield-user', 'history', 'log-in', 'log-out', 'cog',
    'refresh-cw', 'loader', 'share-2'
];

// 创建图标目录
const iconDir = './static/svg';
if (!fs.existsSync(iconDir)) {
    fs.mkdirSync(iconDir, { recursive: true });
}

// 下载函数 - 支持重定向处理
function downloadIcon(iconName) {
    // 使用具体版本号的URL，避免重定向问题
    const url = `https://unpkg.com/lucide-static@0.525.0/icons/${iconName}.svg`;
    const filePath = path.join(iconDir, `${iconName}.svg`);

    downloadWithRedirect(url, filePath, iconName);
}

// 处理重定向的下载函数
function downloadWithRedirect(url, filePath, iconName, maxRedirects = 5) {
    if (maxRedirects <= 0) {
        console.error(`✗ Too many redirects for ${iconName}`);
        return;
    }

    https.get(url, (response) => {
        // 处理重定向
        if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
            const redirectUrl = response.headers.location.startsWith('http')
                ? response.headers.location
                : `https://unpkg.com${response.headers.location}`;

            console.log(`→ Redirecting ${iconName} to: ${redirectUrl}`);
            downloadWithRedirect(redirectUrl, filePath, iconName, maxRedirects - 1);
            return;
        }

        // 检查响应状态
        if (response.statusCode !== 200) {
            console.error(`✗ HTTP ${response.statusCode} for ${iconName}`);
            return;
        }

        // 收集响应数据
        let data = '';
        response.on('data', (chunk) => {
            data += chunk;
        });

        response.on('end', () => {
            // 验证是否为有效的SVG内容
            if (data.includes('<svg') && data.includes('</svg>')) {
                fs.writeFileSync(filePath, data);
                console.log(`✓ Downloaded: ${iconName}.svg`);
            } else {
                console.error(`✗ Invalid SVG content for ${iconName}:`, data.substring(0, 100));
            }
        });

    }).on('error', (err) => {
        console.error(`✗ Error downloading ${iconName}:`, err.message);
    });
}

// 批量下载
console.log('开始下载图标...');
icons.forEach(icon => downloadIcon(icon));