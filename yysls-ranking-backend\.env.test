# 测试环境配置文件

# 数据库配置（测试环境使用SQLite）
DATABASE_URL=sqlite:///./test.db
DATABASE_URL_ASYNC=sqlite+aiosqlite:///./test.db

# JWT配置
JWT_SECRET_KEY=test-jwt-secret-key-for-testing-only
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 微信配置（测试用）
WECHAT_APP_ID=test_app_id
WECHAT_APP_SECRET=test_app_secret

# 邮件配置（测试用）
SMTP_SERVER=localhost
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=test_password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=测试系统

# 前端配置
FRONTEND_URL=http://localhost:3000

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/1

# 测试模式标识
TESTING=true
LOG_LEVEL=DEBUG
