"""
Excel处理功能测试
"""
import os
import tempfile
from io import BytesIO
from datetime import time

import pytest
from fastapi import UploadFile
from openpyxl import Workbook

from app.utils.excel_handler import ExcelHandler


class TestExcelHandler:
    """Excel处理工具类测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.excel_handler = ExcelHandler()
    
    def create_test_excel(self, data_rows=None):
        """创建测试用的Excel文件"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入测试数据
        if data_rows is None:
            data_rows = [
                [1, 5, "05:30", 5, "队伍A,队伍B,队伍C,队伍D,队伍E"],
                [6, 10, "06:15", 5, "队伍F,队伍G,队伍H,队伍I,队伍J"],
                [11, 15, "07:00", 5, "队伍K,队伍L,队伍M,队伍N,队伍O"]
            ]
        
        for row_idx, row_data in enumerate(data_rows, 2):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        # 保存到字节流
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        return excel_buffer
    
    def create_upload_file(self, excel_buffer, filename="test.xlsx"):
        """创建UploadFile对象"""
        return UploadFile(
            filename=filename,
            file=excel_buffer,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_success(self):
        """测试Excel文件验证成功"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer)
        
        result = await self.excel_handler.validate_excel_file(upload_file)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_invalid_extension(self):
        """测试无效文件扩展名"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer, "test.txt")
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.validate_excel_file(upload_file)
        
        assert "不支持的文件格式" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_too_large(self):
        """测试文件过大"""
        # 创建一个超大的Excel文件（模拟）
        large_content = b"x" * (11 * 1024 * 1024)  # 11MB
        upload_file = UploadFile(
            filename="large.xlsx",
            file=BytesIO(large_content),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.validate_excel_file(upload_file)
        
        assert "文件大小超过限制" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_success(self):
        """测试Excel解析成功"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer)
        
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 3
        
        # 检查第一条记录
        first_record = result[0]
        assert first_record['rank_start'] == 1
        assert first_record['rank_end'] == 5
        assert first_record['participant_count'] == 5
        assert first_record['completion_seconds'] == 5 * 60 + 30  # 5分30秒
        assert first_record['team_info'] == "队伍A,队伍B,队伍C,队伍D,队伍E"
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_missing_required_columns(self):
        """测试缺少必需列"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 只写入部分表头
        headers = ["排名开始", "参与人数"]  # 缺少其他必需列
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "缺少必需列" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_invalid_data_types(self):
        """测试无效数据类型"""
        data_rows = [
            ["abc", 5, "05:30", 5, "队伍A"],  # 排名开始应为数字
            [6, "def", "06:15", 5, "队伍B"],  # 排名结束应为数字
            [11, 15, "invalid", 5, "队伍C"],  # 时间格式错误
            [16, 20, "07:00", "xyz", "队伍D"]  # 参与人数应为数字
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "数据解析错误" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_invalid_rank_range(self):
        """测试无效排名范围"""
        data_rows = [
            [10, 5, "05:30", 5, "队伍A"]  # 排名开始大于排名结束
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "排名开始不能大于排名结束" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_empty_required_fields(self):
        """测试必需字段为空"""
        data_rows = [
            [None, 5, "05:30", 5, "队伍A"],  # 排名开始为空
            [6, 10, None, 5, "队伍B"],       # 完成时间为空
            [11, 15, "07:00", None, "队伍C"] # 参与人数为空
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "不能为空" in str(exc_info.value)
    
    def test_create_ranking_template_success(self):
        """测试创建Excel模板成功"""
        excel_buffer = self.excel_handler.create_ranking_template("5_person")
        
        assert excel_buffer is not None
        assert len(excel_buffer.getvalue()) > 0
        
        # 验证可以正常读取
        from openpyxl import load_workbook
        workbook = load_workbook(excel_buffer)
        
        # 检查工作表
        assert "榜单明细模板" in workbook.sheetnames
        assert "使用说明" in workbook.sheetnames
        
        # 检查表头
        worksheet = workbook["榜单明细模板"]
        expected_headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        
        for col_idx, expected_header in enumerate(expected_headers, 1):
            actual_header = worksheet.cell(row=1, column=col_idx).value
            assert expected_header in actual_header
        
        workbook.close()
    
    def test_create_ranking_template_different_types(self):
        """测试创建不同类型的榜单模板"""
        for ranking_type in ["5_person", "10_person"]:
            excel_buffer = self.excel_handler.create_ranking_template(ranking_type)
            assert excel_buffer is not None
            assert len(excel_buffer.getvalue()) > 0
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_with_time_objects(self):
        """测试解析包含时间对象的Excel"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入包含时间对象的数据
        worksheet.cell(row=2, column=1, value=1)
        worksheet.cell(row=2, column=2, value=5)
        worksheet.cell(row=2, column=3, value=time(minute=5, second=30))  # 时间对象
        worksheet.cell(row=2, column=4, value=5)
        worksheet.cell(row=2, column=5, value="队伍A")
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 1
        assert result[0]['completion_seconds'] == 5 * 60 + 30
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_skip_empty_rows(self):
        """测试跳过空行"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入数据，中间包含空行
        worksheet.cell(row=2, column=1, value=1)
        worksheet.cell(row=2, column=2, value=5)
        worksheet.cell(row=2, column=3, value="05:30")
        worksheet.cell(row=2, column=4, value=5)
        worksheet.cell(row=2, column=5, value="队伍A")
        
        # 第3行为空行
        
        worksheet.cell(row=4, column=1, value=6)
        worksheet.cell(row=4, column=2, value=10)
        worksheet.cell(row=4, column=3, value="06:15")
        worksheet.cell(row=4, column=4, value=5)
        worksheet.cell(row=4, column=5, value="队伍B")
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 2  # 应该跳过空行，只解析2条记录
