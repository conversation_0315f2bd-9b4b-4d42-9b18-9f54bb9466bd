# 燕友圈榜单系统 - API接口文档

## 📋 文档概述

本文档提供燕友圈榜单系统的完整API接口说明，包含所有7个核心模块的详细接口文档。

**API版本**: v1  
**基础URL**: `http://localhost:8000/api/v1`  
**文档生成时间**: 2024年当前日期  
**认证方式**: JWT Bearer Token

## 🔐 认证说明

### 认证方式
所有需要认证的接口都使用JWT Bearer Token认证：

```http
Authorization: Bearer <access_token>
```

### 获取Token
通过登录接口获取访问令牌：

```http
POST /api/v1/auth/login
```

## 📚 API模块概览

| 模块 | 描述 | 端点数量 | 认证要求 |
|------|------|----------|----------|
| [认证模块](#认证模块-auth) | 用户登录、微信登录、token管理 | 5个 | 部分需要 |
| [榜单模块](#榜单模块-rankings) | 榜单CRUD、状态管理、明细管理 | 8个 | 需要 |
| [用户模块](#用户模块-users) | 用户管理、权限控制 | 7个 | 需要 |
| [赞助商模块](#赞助商模块-sponsors) | 赞助商管理、排序、状态控制 | 8个 | 需要 |
| [系统配置模块](#系统配置模块-system_config) | 配置管理、批量更新 | 9个 | 需要 |
| [内容模块](#内容模块-content) | 公告、播报消息、静态内容 | 9个 | 需要 |
| [密码模块](#密码模块-password) | 密码重置、修改密码 | 3个 | 部分需要 |

## 🔑 认证模块 (auth)

### 1. 用户登录

**接口**: `POST /api/v1/auth/login`  
**描述**: 用户名密码登录  
**认证**: 不需要

#### 请求参数

```json
{
  "username": "string",
  "password": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名或邮箱 |
| password | string | 是 | 密码 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "role": "user",
      "is_active": true,
      "is_verified": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

**错误响应 (401)**:
```json
{
  "detail": "用户名或密码错误"
}
```

### 2. 微信登录

**接口**: `POST /api/v1/auth/wechat-login`  
**描述**: 微信授权码登录  
**认证**: 不需要

#### 请求参数

```json
{
  "code": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| code | string | 是 | 微信授权码 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "微信登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 2,
      "username": null,
      "email": null,
      "nickname": "微信用户",
      "wechat_openid": "o1234567890abcdef",
      "avatar_url": "https://wx.qlogo.cn/...",
      "role": "user",
      "is_active": true,
      "is_verified": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 3. 获取当前用户信息

**接口**: `GET /api/v1/auth/me`  
**描述**: 获取当前登录用户信息  
**认证**: 需要

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "avatar_url": "https://example.com/avatar.jpg",
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "last_login_at": "2024-01-01T12:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 刷新Token

**接口**: `POST /api/v1/auth/refresh`  
**描述**: 刷新访问令牌  
**认证**: 需要

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户"
    }
  }
}
```

### 5. 用户登出

**接口**: `POST /api/v1/auth/logout`  
**描述**: 用户登出，使token失效  
**认证**: 需要

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

## 📊 榜单模块 (rankings)

### 1. 获取榜单列表

**接口**: `GET /api/v1/rankings`  
**描述**: 分页获取榜单列表  
**认证**: 需要

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| skip | integer | 否 | 0 | 跳过的记录数 |
| limit | integer | 否 | 20 | 每页记录数 |
| ranking_type | string | 否 | - | 榜单类型 (five_person/ten_person) |
| status | string | 否 | - | 榜单状态 (draft/in_progress/finished) |
| search | string | 否 | - | 搜索关键词 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取榜单列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "title": "第一期5人竞速榜单",
        "description": "这是第一期的5人竞速榜单",
        "ranking_type": "five_person",
        "status": "in_progress",
        "max_participants": 5,
        "current_participants": 3,
        "start_time": "2024-01-01T10:00:00Z",
        "end_time": null,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "skip": 0,
    "limit": 20
  }
}
```

### 2. 创建榜单

**接口**: `POST /api/v1/rankings`  
**描述**: 创建新榜单  
**认证**: 需要（管理员权限）

#### 请求参数

```json
{
  "title": "string",
  "description": "string",
  "ranking_type": "five_person",
  "max_participants": 5
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| title | string | 是 | 榜单标题 |
| description | string | 否 | 榜单描述 |
| ranking_type | string | 是 | 榜单类型 (five_person/ten_person) |
| max_participants | integer | 是 | 最大参与人数 |

#### 响应示例

**成功响应 (201)**:
```json
{
  "code": 201,
  "message": "榜单创建成功",
  "data": {
    "id": 2,
    "title": "新榜单",
    "description": "新榜单描述",
    "ranking_type": "five_person",
    "status": "draft",
    "max_participants": 5,
    "current_participants": 0,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3. 获取榜单详情

**接口**: `GET /api/v1/rankings/{ranking_id}`
**描述**: 获取指定榜单的详细信息
**认证**: 需要

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取榜单详情成功",
  "data": {
    "id": 1,
    "title": "第一期5人竞速榜单",
    "description": "这是第一期的5人竞速榜单",
    "ranking_type": "five_person",
    "status": "in_progress",
    "max_participants": 5,
    "current_participants": 3,
    "start_time": "2024-01-01T10:00:00Z",
    "end_time": null,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z",
    "details": [
      {
        "id": 1,
        "participant_name": "参与者1",
        "completion_seconds": 120,
        "rank_range": "1-3",
        "notes": "表现优秀",
        "created_at": "2024-01-01T10:30:00Z"
      }
    ]
  }
}
```

### 4. 更新榜单状态

**接口**: `PUT /api/v1/rankings/{ranking_id}/status`
**描述**: 更新榜单状态
**认证**: 需要（管理员权限）

#### 请求参数

```json
{
  "status": "in_progress"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| status | string | 是 | 新状态 (draft/in_progress/finished) |

## 👥 用户模块 (users)

### 1. 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 分页获取用户列表（管理员权限）
**认证**: 需要（管理员权限）

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| skip | integer | 否 | 0 | 跳过的记录数 |
| limit | integer | 否 | 20 | 每页记录数 |
| search | string | 否 | - | 搜索关键词 |
| role | string | 否 | - | 用户角色 (user/admin) |
| is_active | boolean | 否 | - | 是否激活 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "role": "user",
        "is_active": true,
        "is_verified": true,
        "last_login_at": "2024-01-01T12:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "skip": 0,
    "limit": 20
  }
}
```

### 2. 更新当前用户信息

**接口**: `PUT /api/v1/users/me`
**描述**: 更新当前登录用户的信息
**认证**: 需要

#### 请求参数

```json
{
  "nickname": "string",
  "avatar_url": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| nickname | string | 否 | 用户昵称 |
| avatar_url | string | 否 | 头像URL |

## 🏢 赞助商模块 (sponsors)

### 1. 获取赞助商列表

**接口**: `GET /api/v1/sponsors`
**描述**: 获取赞助商列表
**认证**: 不需要（公开接口）

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| is_active | boolean | 否 | true | 是否只显示激活的赞助商 |
| skip | integer | 否 | 0 | 跳过的记录数 |
| limit | integer | 否 | 20 | 每页记录数 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取赞助商列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "赞助商A",
        "logo_url": "https://example.com/logo.jpg",
        "website_url": "https://sponsor-a.com",
        "contact_info": "<EMAIL>",
        "description": "优质赞助商",
        "sort_order": 1,
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "skip": 0,
    "limit": 20
  }
}
```
