# 数据库设计文档

## 概述

燕友圈榜单系统数据库设计，基于原型图分析的功能需求，设计了完整的数据库结构。

## 数据库ER图

```mermaid
erDiagram
    users ||--o{ rankings : creates
    users ||--o{ ranking_details : manages
    users ||--o{ contents : creates
    users ||--o{ broadcast_messages : creates
    
    rankings ||--o{ ranking_details : contains
    
    users {
        int id PK
        string username UK
        string nickname
        string avatar_url
        string email UK
        string phone UK
        string wechat_openid UK
        string wechat_unionid UK
        string role
        boolean is_active
        boolean is_verified
        text bio
        string level
        int points
        string location
        string user_number UK
        string gender
        int age
        datetime created_at
        datetime updated_at
        datetime last_login_at
    }
    
    rankings {
        int id PK
        string name
        int period
        string ranking_type
        datetime start_time
        datetime end_time
        int team_size_limit
        int total_participants
        string status
        int created_by FK
        int updated_by FK
        datetime created_at
        datetime updated_at
    }
    
    ranking_details {
        int id PK
        int ranking_id FK
        int rank_start
        int rank_end
        time completion_time
        int completion_seconds
        int participant_count
        text team_info
        datetime created_at
        datetime updated_at
    }
    
    sponsors {
        int id PK
        string name
        string logo_url
        int sort_order
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    system_configs {
        int id PK
        string config_key UK
        text config_value
        string config_type
        string name
        text description
        string value_type
        text default_value
        text validation_rule
        boolean is_active
        boolean is_public
        string group_name
        int sort_order
        datetime created_at
        datetime updated_at
    }
    
    contents {
        int id PK
        string title
        text content
        string content_type
        text summary
        string keywords
        string status
        boolean is_featured
        boolean is_top
        int display_order
        int view_count
        datetime publish_time
        datetime expire_time
        int created_by FK
        int updated_by FK
        datetime created_at
        datetime updated_at
    }
    
    broadcast_messages {
        int id PK
        text message
        string message_type
        int display_duration
        int display_order
        boolean is_active
        datetime start_time
        datetime end_time
        int created_by FK
        datetime created_at
        datetime updated_at
    }
```

## 数据表详细设计

### 1. 用户表 (users)

存储系统用户的基本信息和认证数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | UNIQUE, NOT NULL | 用户名 |
| nickname | VARCHAR(100) | NULL | 昵称 |
| avatar_url | VARCHAR(500) | NULL | 头像URL |
| email | VARCHAR(100) | UNIQUE, NULL | 邮箱 |
| phone | VARCHAR(20) | UNIQUE, NULL | 手机号 |
| wechat_openid | VARCHAR(100) | UNIQUE, NULL | 微信OpenID |
| wechat_unionid | VARCHAR(100) | UNIQUE, NULL | 微信UnionID |
| role | VARCHAR(20) | NOT NULL, DEFAULT 'user' | 用户角色 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| is_verified | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否验证 |
| bio | TEXT | NULL | 个人简介 |
| level | VARCHAR(50) | DEFAULT '江湖新人' | 用户等级 |
| points | INTEGER | NOT NULL, DEFAULT 0 | 积分 |
| location | VARCHAR(200) | NULL | 所在地 |
| user_number | VARCHAR(50) | UNIQUE, NULL | 用户编号 |
| gender | VARCHAR(20) | NULL | 性别 |
| age | INTEGER | NULL | 年龄 |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |
| last_login_at | DATETIME | NULL | 最后登录时间 |

**索引:**

- PRIMARY KEY (id)
- UNIQUE KEY (username)
- UNIQUE KEY (email)
- UNIQUE KEY (phone)
- UNIQUE KEY (wechat_openid)
- UNIQUE KEY (wechat_unionid)
- UNIQUE KEY (user_number)
- INDEX (role)

### 2. 榜单表 (rankings)

存储榜单的基础信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 榜单ID |
| name | VARCHAR(200) | NOT NULL | 榜单名称 |
| period | INTEGER | NOT NULL | 期数 |
| ranking_type | VARCHAR(20) | NOT NULL | 榜单类型 |
| start_time | DATETIME | NOT NULL | 开始时间 |
| end_time | DATETIME | NOT NULL | 结束时间 |
| team_size_limit | INTEGER | NOT NULL | 组队人数限制 |
| total_participants | INTEGER | NOT NULL, DEFAULT 0 | 总参与人数 |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'not_started' | 榜单状态 |
| created_by | INTEGER | NOT NULL, FK | 创建人ID |
| updated_by | INTEGER | NULL, FK | 更新人ID |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX (period)
- INDEX (ranking_type)
- INDEX (status)
- INDEX (created_by)
- FOREIGN KEY (created_by) REFERENCES users(id)
- FOREIGN KEY (updated_by) REFERENCES users(id)

### 3. 榜单明细表 (ranking_details)

存储具体的排名数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 明细ID |
| ranking_id | INTEGER | NOT NULL, FK | 榜单ID |
| rank_start | INTEGER | NOT NULL | 排名开始 |
| rank_end | INTEGER | NOT NULL | 排名结束 |
| completion_time | TIME | NOT NULL | 完成时间(分秒) |
| completion_seconds | INTEGER | NOT NULL | 完成时间(总秒数) |
| participant_count | INTEGER | NOT NULL | 参与人数 |
| team_info | TEXT | NULL | 队伍信息(JSON) |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX (ranking_id)
- INDEX (rank_start, rank_end)
- INDEX (completion_seconds)
- FOREIGN KEY (ranking_id) REFERENCES rankings(id) ON DELETE CASCADE

### 4. 赞助商表 (sponsors)

存储赞助商信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 赞助商ID |
| name | VARCHAR(200) | NOT NULL | 赞助商名称 |
| logo_url | VARCHAR(500) | NULL | Logo URL（用作头像） |
| sort_order | INTEGER | NOT NULL, DEFAULT 0 | 排序顺序 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否启用 |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**

- PRIMARY KEY (id)
- INDEX (sort_order)
- INDEX (is_active)
- INDEX (created_at)

### 5. 系统配置表 (system_configs)

存储系统配置参数。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 配置ID |
| config_key | VARCHAR(100) | UNIQUE, NOT NULL | 配置键 |
| config_value | TEXT | NULL | 配置值 |
| config_type | VARCHAR(50) | NOT NULL | 配置类型 |
| name | VARCHAR(200) | NOT NULL | 配置名称 |
| description | TEXT | NULL | 配置描述 |
| value_type | VARCHAR(20) | NOT NULL, DEFAULT 'string' | 值类型 |
| default_value | TEXT | NULL | 默认值 |
| validation_rule | TEXT | NULL | 验证规则 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否启用 |
| is_public | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否公开 |
| group_name | VARCHAR(100) | NULL | 分组名称 |
| sort_order | INTEGER | NOT NULL, DEFAULT 0 | 排序 |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- UNIQUE KEY (config_key)
- INDEX (config_type)
- INDEX (is_active)
- INDEX (group_name)

### 6. 内容表 (contents)

存储各类内容信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 内容ID |
| title | VARCHAR(500) | NOT NULL | 标题 |
| content | TEXT | NOT NULL | 内容 |
| content_type | VARCHAR(50) | NOT NULL | 内容类型 |
| summary | TEXT | NULL | 摘要 |
| keywords | VARCHAR(500) | NULL | 关键词 |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'draft' | 状态 |
| is_featured | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否推荐 |
| is_top | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否置顶 |
| display_order | INTEGER | NOT NULL, DEFAULT 0 | 显示顺序 |
| view_count | INTEGER | NOT NULL, DEFAULT 0 | 浏览次数 |
| publish_time | DATETIME | NULL | 发布时间 |
| expire_time | DATETIME | NULL | 过期时间 |
| created_by | INTEGER | NOT NULL, FK | 创建人ID |
| updated_by | INTEGER | NULL, FK | 更新人ID |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX (content_type)
- INDEX (status)
- INDEX (is_featured)
- INDEX (is_top)
- INDEX (publish_time)
- FOREIGN KEY (created_by) REFERENCES users(id)
- FOREIGN KEY (updated_by) REFERENCES users(id)

### 7. 播报消息表 (broadcast_messages)

存储播报消息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PK, AUTO_INCREMENT | 消息ID |
| message | TEXT | NOT NULL | 播报消息 |
| message_type | VARCHAR(50) | NOT NULL, DEFAULT 'info' | 消息类型 |
| display_duration | INTEGER | NOT NULL, DEFAULT 15 | 显示时长(秒) |
| display_order | INTEGER | NOT NULL, DEFAULT 0 | 显示顺序 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否启用 |
| start_time | DATETIME | NULL | 开始时间 |
| end_time | DATETIME | NULL | 结束时间 |
| created_by | INTEGER | NOT NULL, FK | 创建人ID |
| created_at | DATETIME | NOT NULL | 创建时间 |
| updated_at | DATETIME | NOT NULL | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX (is_active)
- INDEX (display_order)
- INDEX (start_time, end_time)
- FOREIGN KEY (created_by) REFERENCES users(id)

## 数据库初始化脚本

### 创建数据库

```sql
-- 创建数据库
CREATE DATABASE yysls_ranking CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'yysls_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON yysls_ranking.* TO 'yysls_user'@'localhost';
FLUSH PRIVILEGES;
```

### 使用Alembic进行迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head
```

## 性能优化建议

1. **索引优化**
   - 为经常查询的字段添加索引
   - 复合索引优化多字段查询
   - 定期分析索引使用情况

2. **分区策略**
   - 按时间分区榜单相关表
   - 按类型分区内容表

3. **缓存策略**
   - Redis缓存热点数据
   - 应用层缓存配置信息

4. **数据归档**
   - 定期归档历史榜单数据
   - 清理过期的播报消息
