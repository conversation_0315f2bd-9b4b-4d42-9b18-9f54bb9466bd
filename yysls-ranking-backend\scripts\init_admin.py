#!/usr/bin/env python3
"""
初始化管理员用户脚本

创建一个默认的管理员用户：
- 用户名: admin
- 密码: 123456
- 角色: admin
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User, UserRole
from app.utils.security import get_password_hash
from app.schemas.user import UserCreate


def create_admin_user():
    """创建管理员用户"""
    db = SessionLocal()
    
    try:
        # 检查是否已存在admin用户
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("❌ 管理员用户 'admin' 已存在")
            print(f"   用户ID: {existing_admin.id}")
            print(f"   角色: {existing_admin.role}")
            print(f"   创建时间: {existing_admin.created_at}")
            return False
        
        # 创建管理员用户数据
        admin_data = {
            "username": "admin",
            "password_hash": get_password_hash("123456"),
            "nickname": "系统管理员",
            "role": UserRole.ADMIN.value,
            "is_active": True,
            "is_verified": True,
            "level": "系统管理员",
            "points": 0,
            "bio": "系统默认管理员账户"
        }
        
        # 创建用户对象
        admin_user = User(**admin_data)
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ 管理员用户创建成功！")
        print(f"   用户名: admin")
        print(f"   密码: 123456")
        print(f"   用户ID: {admin_user.id}")
        print(f"   角色: {admin_user.role}")
        print(f"   创建时间: {admin_user.created_at}")
        print("\n⚠️  请在生产环境中及时修改默认密码！")
        
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建管理员用户失败: {str(e)}")
        return False
        
    finally:
        db.close()


def check_admin_users():
    """检查现有的管理员用户"""
    db = SessionLocal()
    
    try:
        admin_users = db.query(User).filter(User.role == UserRole.ADMIN.value).all()
        
        if not admin_users:
            print("📋 当前没有管理员用户")
            return []
        
        print(f"📋 当前管理员用户列表 (共{len(admin_users)}个):")
        for user in admin_users:
            status = "✅ 激活" if user.is_active else "❌ 禁用"
            verified = "✅ 已验证" if user.is_verified else "❌ 未验证"
            print(f"   - ID: {user.id}, 用户名: {user.username}, 昵称: {user.nickname}")
            print(f"     状态: {status}, 验证: {verified}")
            print(f"     创建时间: {user.created_at}")
            if user.last_login_at:
                print(f"     最后登录: {user.last_login_at}")
            print()
        
        return admin_users
        
    except Exception as e:
        print(f"❌ 查询管理员用户失败: {str(e)}")
        return []
        
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 燕友圈榜单系统 - 管理员用户初始化工具")
    print("=" * 50)
    
    # 检查现有管理员用户
    print("1. 检查现有管理员用户...")
    existing_admins = check_admin_users()
    
    if existing_admins:
        print("\n2. 是否要创建新的管理员用户？")
        choice = input("   输入 'y' 继续创建，其他键退出: ").lower().strip()
        if choice != 'y':
            print("👋 操作已取消")
            return
    
    print("\n2. 创建默认管理员用户...")
    success = create_admin_user()
    
    if success:
        print("\n🎉 初始化完成！")
        print("\n💡 使用说明:")
        print("   1. 启动服务器: uvicorn app.main:app --reload")
        print("   2. 访问 http://localhost:8000/docs 查看API文档")
        print("   3. 使用以下凭据登录:")
        print("      用户名: admin")
        print("      密码: 123456")
        print("\n⚠️  安全提醒:")
        print("   - 请在生产环境中立即修改默认密码")
        print("   - 建议启用双因素认证")
        print("   - 定期检查管理员账户的使用情况")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
