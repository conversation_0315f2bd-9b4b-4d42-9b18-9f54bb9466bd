#!/usr/bin/env python3
"""
API端点测试脚本

用于验证API文档中的端点是否正确工作
"""
import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

import httpx
from httpx import AsyncClient

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.client: Optional[AsyncClient] = None
    
    async def __aenter__(self):
        self.client = AsyncClient(base_url=self.base_url, timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_health_endpoints(self):
        """测试健康检查端点"""
        print("🔍 测试健康检查端点...")
        
        # 测试根路径
        try:
            response = await self.client.get("/")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ GET / - 状态码: {response.status_code}")
                print(f"   响应: {data.get('message', 'N/A')}")
            else:
                print(f"❌ GET / - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ GET / - 错误: {e}")
        
        # 测试健康检查
        try:
            response = await self.client.get("/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ GET /health - 状态码: {response.status_code}")
                print(f"   状态: {data.get('status', 'N/A')}")
            else:
                print(f"❌ GET /health - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ GET /health - 错误: {e}")
    
    async def test_auth_endpoints(self):
        """测试认证端点"""
        print("\n🔐 测试认证端点...")
        
        # 测试登录端点（不实际登录，只测试端点存在性）
        try:
            response = await self.client.post("/api/v1/auth/login", json={
                "username": "test_user",
                "password": "test_password"
            })
            print(f"✅ POST /api/v1/auth/login - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   预期的认证失败响应")
            elif response.status_code == 200:
                print("   登录成功（如果有测试用户）")
        except Exception as e:
            print(f"❌ POST /api/v1/auth/login - 错误: {e}")
        
        # 测试微信登录端点
        try:
            response = await self.client.post("/api/v1/auth/wechat-login", json={
                "code": "test_wechat_code"
            })
            print(f"✅ POST /api/v1/auth/wechat-login - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ POST /api/v1/auth/wechat-login - 错误: {e}")
        
        # 测试获取用户信息端点（无token）
        try:
            response = await self.client.get("/api/v1/auth/me")
            print(f"✅ GET /api/v1/auth/me - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   预期的未授权响应")
        except Exception as e:
            print(f"❌ GET /api/v1/auth/me - 错误: {e}")
    
    async def test_rankings_endpoints(self):
        """测试榜单端点"""
        print("\n📊 测试榜单端点...")
        
        # 测试获取榜单列表（无token）
        try:
            response = await self.client.get("/api/v1/rankings")
            print(f"✅ GET /api/v1/rankings - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   需要认证")
            elif response.status_code == 200:
                print("   成功获取榜单列表")
        except Exception as e:
            print(f"❌ GET /api/v1/rankings - 错误: {e}")
        
        # 测试创建榜单（无token）
        try:
            response = await self.client.post("/api/v1/rankings", json={
                "name": "测试榜单",
                "period": 1,
                "ranking_type": "five_person",
                "start_time": "2024-01-20T09:00:00Z",
                "end_time": "2024-01-20T18:00:00Z",
                "team_size_limit": 5
            })
            print(f"✅ POST /api/v1/rankings - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   需要认证")
        except Exception as e:
            print(f"❌ POST /api/v1/rankings - 错误: {e}")
    
    async def test_users_endpoints(self):
        """测试用户端点"""
        print("\n👥 测试用户端点...")
        
        # 测试获取用户列表（无token）
        try:
            response = await self.client.get("/api/v1/users")
            print(f"✅ GET /api/v1/users - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   需要认证")
        except Exception as e:
            print(f"❌ GET /api/v1/users - 错误: {e}")
    
    async def test_sponsors_endpoints(self):
        """测试赞助商端点"""
        print("\n🏢 测试赞助商端点...")
        
        # 测试获取赞助商列表（无token）
        try:
            response = await self.client.get("/api/v1/sponsors")
            print(f"✅ GET /api/v1/sponsors - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("   需要认证")
        except Exception as e:
            print(f"❌ GET /api/v1/sponsors - 错误: {e}")
    
    async def test_openapi_endpoint(self):
        """测试OpenAPI文档端点"""
        print("\n📚 测试OpenAPI文档端点...")
        
        try:
            response = await self.client.get("/openapi.json")
            if response.status_code == 200:
                openapi_data = response.json()
                print(f"✅ GET /openapi.json - 状态码: {response.status_code}")
                print(f"   API标题: {openapi_data.get('info', {}).get('title', 'N/A')}")
                print(f"   API版本: {openapi_data.get('info', {}).get('version', 'N/A')}")
                print(f"   端点数量: {len(openapi_data.get('paths', {}))}")
            else:
                print(f"❌ GET /openapi.json - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ GET /openapi.json - 错误: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API端点测试...")
        print(f"基础URL: {self.base_url}")
        print("=" * 50)
        
        await self.test_health_endpoints()
        await self.test_auth_endpoints()
        await self.test_rankings_endpoints()
        await self.test_users_endpoints()
        await self.test_sponsors_endpoints()
        await self.test_openapi_endpoint()
        
        print("\n" + "=" * 50)
        print("✅ API端点测试完成")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="API端点测试工具")
    parser.add_argument(
        "--base-url", 
        default="http://localhost:8000",
        help="API基础URL (默认: http://localhost:8000)"
    )
    
    args = parser.parse_args()
    
    async with APITester(args.base_url) as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        sys.exit(1)
