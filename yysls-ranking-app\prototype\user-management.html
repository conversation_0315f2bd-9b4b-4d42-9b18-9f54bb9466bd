<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 用户管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 60px 20px 140px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 搜索框样式 */
        .search-input {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 25px;
            padding: 10px 18px 10px 45px;
            color: #F5F5DC;
            width: 100%;
            font-size: 15px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        .search-input::placeholder {
            color: rgba(245, 245, 220, 0.5);
        }
        
        .search-container {
            position: relative;
        }
        
        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);
        }
        
        /* 用户卡片样式 */
        .user-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .user-card:hover {
            border-color: rgba(212, 175, 55, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .user-avatar {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            border: 2px solid rgba(212, 175, 55, 0.3);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
            flex-shrink: 0;
        }

        /* 用户信息区域 */
        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            flex-wrap: wrap;
            gap: 6px;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .user-detail-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #9CA3AF;
        }

        .user-detail-icon {
            width: 13px;
            height: 13px;
            margin-right: 5px;
            flex-shrink: 0;
        }

        /* 操作按钮区域 */
        .user-actions {
            flex-shrink: 0;
            margin-left: 12px;
        }
        
        /* 用户状态标签 */
        .user-status {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .user-status.admin {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #D4AF37;
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
        }
        
        .user-status.normal {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(245, 245, 220, 0.3);
            color: #F5F5DC;
        }
        
        /* 管理按钮样式 */
        .btn-admin {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transform: translateY(-1px);
        }
        
        .btn-admin:disabled {
            background: rgba(74, 74, 74, 0.5);
            color: rgba(245, 245, 220, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(245, 245, 220, 0.6);
        }
        
        .empty-state img {
            width: 80px;
            height: 80px;
            opacity: 0.3;
            margin: 0 auto 16px;
            filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 20px;
            color: rgba(245, 245, 220, 0.6);
        }
        
        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid rgba(212, 175, 55, 0.2);
            border-top: 2px solid #D4AF37;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 640px) {
            .main-content {
                padding: 60px 12px 140px 12px;
            }

            .user-card {
                padding: 12px;
                margin-bottom: 10px;
            }

            .user-avatar {
                width: 46px;
                height: 46px;
            }

            .user-name-row {
                margin-bottom: 4px;
                gap: 4px;
            }

            .user-details {
                gap: 2px;
            }

            .user-detail-item {
                font-size: 12px;
            }

            .user-detail-icon {
                width: 12px;
                height: 12px;
                margin-right: 4px;
            }

            .user-actions {
                margin-left: 10px;
            }

            .btn-admin {
                padding: 5px 10px;
                font-size: 12px;
            }

            .user-status {
                padding: 3px 8px;
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 60px 10px 140px 10px;
            }

            .user-card {
                padding: 10px;
                margin-bottom: 8px;
            }

            .user-card .flex {
                flex-direction: row;
                align-items: flex-start;
                gap: 8px;
            }

            .user-avatar {
                width: 42px;
                height: 42px;
            }

            .user-info {
                flex: 1;
                margin-left: 0 !important;
                min-width: 0;
            }

            .user-name-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 3px;
                margin-bottom: 3px;
            }

            .user-name-row h3 {
                font-size: 16px;
                line-height: 1.2;
            }

            .user-details {
                gap: 1px;
            }

            .user-detail-item {
                font-size: 11px;
            }

            .user-detail-icon {
                width: 11px;
                height: 11px;
                margin-right: 3px;
            }

            .user-actions {
                margin-left: 0;
                margin-top: 8px;
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }

            .btn-admin {
                padding: 4px 8px;
                font-size: 11px;
                min-width: 80px;
            }

            .user-status {
                padding: 2px 6px;
                font-size: 10px;
            }

            /* 紧凑布局：将操作按钮放在右上角 */
            .user-card-compact {
                position: relative;
            }

            .user-card-compact .user-actions {
                position: absolute;
                top: 8px;
                right: 8px;
                margin: 0;
                width: auto;
            }

            .user-card-compact .user-info {
                padding-right: 90px;
            }

            /* 进一步优化小屏幕显示 */
            .search-input {
                padding: 8px 16px 8px 40px;
                font-size: 14px;
            }

            .search-icon {
                left: 14px;
                width: 18px;
                height: 18px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 360px) {
            .main-content {
                padding: 60px 8px 140px 8px;
            }

            .user-card {
                padding: 8px;
                margin-bottom: 6px;
            }

            .user-avatar {
                width: 38px;
                height: 38px;
            }

            .user-name-row h3 {
                font-size: 15px;
            }

            .user-detail-item {
                font-size: 10px;
            }

            .user-detail-icon {
                width: 10px;
                height: 10px;
                margin-right: 2px;
            }

            .btn-admin {
                padding: 3px 6px;
                font-size: 10px;
                min-width: 70px;
            }

            .user-status {
                padding: 1px 4px;
                font-size: 9px;
            }

            .user-card-compact .user-info {
                padding-right: 75px;
            }
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 搜索区域 -->
        <div class="ink-card p-4 scroll-animate mb-4">
            <div class="search-container">
                <img src="https://unpkg.com/lucide-static@latest/icons/search.svg" alt="搜索" class="search-icon">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索用户昵称..." onkeyup="handleSearch()">
            </div>
        </div>
        
        <!-- 用户列表 -->
        <div id="userList" class="scroll-animate">
            <!-- 加载状态 -->
            <div id="loadingState" class="ink-card loading">
                <div class="loading-spinner"></div>
                <p>正在加载用户列表...</p>
            </div>
            
            <!-- 用户列表将在这里动态生成 -->
        </div>
    </div>
    
    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 用户管理页面控制器
        class UserManagement {
            constructor() {
                this.users = [];
                this.filteredUsers = [];
                this.isLoading = false;
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();
                
                // 初始化标签栏
                this.initTabbar();
                
                // 加载用户数据
                this.loadUsers();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                // 观察所有需要动画的元素
                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 模拟加载用户数据
            async loadUsers() {
                this.isLoading = true;
                
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // 模拟用户数据
                this.users = [
                    {
                        id: 1,
                        nickname: '剑客无名',
                        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
                        level: '武林高手',
                        registerDate: '2024-01-15',
                        isAdmin: false,
                        points: 1580
                    },
                    {
                        id: 2,
                        nickname: '江湖侠女',
                        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face',
                        level: '江湖新秀',
                        registerDate: '2024-02-20',
                        isAdmin: true,
                        points: 890
                    },
                    {
                        id: 3,
                        nickname: '独行剑客',
                        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face',
                        level: '武林宗师',
                        registerDate: '2023-12-08',
                        isAdmin: false,
                        points: 2340
                    },
                    {
                        id: 4,
                        nickname: '花间一壶酒',
                        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=120&h=120&fit=crop&crop=face',
                        level: '江湖新秀',
                        registerDate: '2024-03-10',
                        isAdmin: false,
                        points: 450
                    }
                ];
                
                this.filteredUsers = [...this.users];
                this.isLoading = false;
                this.renderUserList();
            }

            // 渲染用户列表
            renderUserList() {
                const userListContainer = document.getElementById('userList');
                const loadingState = document.getElementById('loadingState');
                
                if (this.isLoading) {
                    loadingState.style.display = 'block';
                    return;
                }
                
                loadingState.style.display = 'none';
                
                if (this.filteredUsers.length === 0) {
                    userListContainer.innerHTML = `
                        <div class="ink-card empty-state">
                            <img src="https://unpkg.com/lucide-static@latest/icons/users.svg" alt="无用户">
                            <p>暂无用户数据</p>
                        </div>
                    `;
                    return;
                }
                
                const userCards = this.filteredUsers.map(user => this.createUserCard(user)).join('');
                userListContainer.innerHTML = userCards;
            }

            // 创建用户卡片
            createUserCard(user) {
                const statusIcon = user.isAdmin ? 'crown' : 'user';
                const statusClass = user.isAdmin ? 'admin' : 'normal';
                const statusText = user.isAdmin ? '管理员' : '普通用户';
                const buttonText = user.isAdmin ? '已是管理员' : '设为管理员';
                const buttonDisabled = user.isAdmin ? 'disabled' : '';

                return `
                    <div class="user-card user-card-compact" data-user-id="${user.id}">
                        <div class="flex items-start">
                            <img src="${user.avatar}" alt="${user.nickname}" class="user-avatar">
                            <div class="user-info ml-3">
                                <div class="user-name-row">
                                    <h3 class="text-base font-semibold text-white">${user.nickname}</h3>
                                    <span class="user-status ${statusClass}">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/${statusIcon}.svg"
                                             alt="${statusText}" class="w-3 h-3 mr-1"
                                             style="filter: ${user.isAdmin ? 'brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%)' : 'brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%)'};">
                                        ${statusText}
                                    </span>
                                </div>
                                <div class="user-details">
                                    <div class="user-detail-item">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/star.svg" alt="等级"
                                             class="user-detail-icon" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                                        <span class="gold-text">${user.level}</span>
                                    </div>
                                    <div class="user-detail-item">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/calendar.svg" alt="注册时间"
                                             class="user-detail-icon" style="filter: brightness(0) saturate(100%) invert(60%) sepia(8%) saturate(564%) hue-rotate(314deg) brightness(95%) contrast(86%);">
                                        <span>注册于 ${user.registerDate}</span>
                                    </div>
                                    <div class="user-detail-item">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/coins.svg" alt="积分"
                                             class="user-detail-icon" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                                        <span class="gold-text">${user.points}分</span>
                                    </div>
                                </div>
                            </div>
                            <div class="user-actions">
                                <button class="btn-admin" ${buttonDisabled} onclick="userManagement.promoteToAdmin(${user.id})">
                                    ${buttonText}
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 搜索用户
            handleSearch(query) {
                if (!query || query.trim() === '') {
                    this.filteredUsers = [...this.users];
                } else {
                    this.filteredUsers = this.users.filter(user =>
                        user.nickname.toLowerCase().includes(query.toLowerCase())
                    );
                }
                this.renderUserList();
            }

            // 提升为管理员
            async promoteToAdmin(userId) {
                const user = this.users.find(u => u.id === userId);
                if (!user || user.isAdmin) {
                    return;
                }

                // 显示确认对话框
                if (!confirm(`确定要将"${user.nickname}"设为管理员吗？`)) {
                    return;
                }

                try {
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 800));

                    // 更新用户状态
                    user.isAdmin = true;

                    // 更新过滤后的用户列表
                    const filteredUser = this.filteredUsers.find(u => u.id === userId);
                    if (filteredUser) {
                        filteredUser.isAdmin = true;
                    }

                    // 重新渲染列表
                    this.renderUserList();

                    // 显示成功提示
                    this.showToast(`"${user.nickname}"已成功设为管理员`, 'success');

                } catch (error) {
                    console.error('设置管理员失败:', error);
                    this.showToast('操作失败，请稍后重试', 'error');
                }
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                // 根据类型设置样式
                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');

                // 等待iframe加载完成
                tabbarIframe.addEventListener('load', () => {
                    // 向标签栏发送消息，设置当前活动标签为"我的"
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }
        }

        // 全局函数
        function handleSearch() {
            const query = document.getElementById('searchInput').value;
            userManagement.handleSearch(query);
        }

        function goBack() {
            window.history.back();
        }

        // 全局变量
        let userManagement;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            userManagement = new UserManagement();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 允许来自同源或iframe的消息
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
