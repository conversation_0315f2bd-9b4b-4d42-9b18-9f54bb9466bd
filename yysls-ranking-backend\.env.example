# 数据库配置 (MySQL 8)
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/yysls_ranking?charset=utf8mb4
DATABASE_URL_ASYNC=mysql+aiomysql://root:password@localhost:3306/yysls_ranking?charset=utf8mb4

# 应用配置
APP_NAME=燕友圈榜单系统
APP_VERSION=1.0.0
DEBUG=True
SECRET_KEY=your-secret-key-here

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 微信登录配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# Redis配置（可选，用于缓存）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
