<template>
	<image :src="url" class="lui-image" :mode="mode" :style="customStyle"></image>
</template>

<script setup>
	import {
		ref,
		computed
	} from "vue";
	import {
		staticURL
	} from "@/config/dns.js";
	const props = defineProps({
		src: {
			type: String,
			default: ""
		},
		// 图片裁剪、缩放的模式
		mode: {
			type: String,
			default: 'scaleToFill'
		},
		customStyle: {
			type: [Object, String],
			default: () => ({})
		}
	});

	const url = computed(() => {
		if (props.src.indexOf('/style/image') !== -1||props.src.indexOf('//tmp') !== -1) return props.src;
		return staticURL + props.src
	})
</script>

<style lang="less" scoped>
	.lui-image {
		display: block;
	}
</style>