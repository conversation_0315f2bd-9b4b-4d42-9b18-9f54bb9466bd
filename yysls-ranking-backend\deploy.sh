#!/bin/bash

# 燕友圈榜单系统部署脚本
# 用于生产环境的自动化部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f ".env.prod" ]; then
        log_error "生产环境配置文件 .env.prod 不存在"
        log_info "请复制 .env.prod.example 为 .env.prod 并填入正确的配置"
        exit 1
    fi
    
    log_success "配置文件检查通过"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    # 创建备份目录
    mkdir -p backups
    
    # 生成备份文件名
    BACKUP_FILE="backups/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # 执行备份（如果数据库容器存在）
    if docker ps -a | grep -q "yysls-ranking-db"; then
        docker exec yysls-ranking-db pg_dump -U postgres yysls_ranking > "$BACKUP_FILE"
        log_success "数据库备份完成: $BACKUP_FILE"
    else
        log_warning "数据库容器不存在，跳过备份"
    fi
}

# 构建镜像
build_image() {
    log_info "构建Docker镜像..."
    
    docker build -t yysls-ranking:latest .
    
    log_success "镜像构建完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待数据库启动
    sleep 10
    
    # 运行迁移
    docker-compose -f docker-compose.prod.yml exec app alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 部署应用
deploy_app() {
    log_info "部署应用..."
    
    # 停止旧容器
    docker-compose -f docker-compose.prod.yml down
    
    # 启动新容器
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "应用部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待应用启动
    sleep 30
    
    # 检查应用健康状态
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        log_info "查看应用日志:"
        docker-compose -f docker-compose.prod.yml logs app
        exit 1
    fi
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    # 删除悬空镜像
    docker image prune -f
    
    log_success "清理完成"
}

# 显示部署信息
show_info() {
    log_success "部署完成！"
    echo ""
    echo "应用信息:"
    echo "  - 应用地址: http://localhost:8000"
    echo "  - API文档: http://localhost:8000/docs"
    echo "  - 健康检查: http://localhost:8000/health"
    echo ""
    echo "管理命令:"
    echo "  - 查看日志: docker-compose -f docker-compose.prod.yml logs -f"
    echo "  - 停止服务: docker-compose -f docker-compose.prod.yml down"
    echo "  - 重启服务: docker-compose -f docker-compose.prod.yml restart"
    echo ""
}

# 主函数
main() {
    echo "🚀 燕友圈榜单系统部署脚本"
    echo "================================"
    
    # 解析命令行参数
    SKIP_BACKUP=false
    SKIP_BUILD=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-backup  跳过数据库备份"
                echo "  --skip-build   跳过镜像构建"
                echo "  --help         显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_requirements
    check_config
    
    if [ "$SKIP_BACKUP" = false ]; then
        backup_database
    fi
    
    if [ "$SKIP_BUILD" = false ]; then
        build_image
    fi
    
    deploy_app
    run_migrations
    health_check
    cleanup
    show_info
}

# 错误处理
trap 'log_error "部署失败！"; exit 1' ERR

# 运行主函数
main "$@"
