#!/bin/bash

# MySQL 无限重启修复脚本

set -e

echo "🔧 MySQL 无限重启修复工具"
echo "=========================="

# 1. 停止数据库服务
echo "1. 停止数据库服务..."
docker-compose stop db 2>/dev/null || true
docker stop yysls-ranking-db 2>/dev/null || true
docker rm yysls-ranking-db 2>/dev/null || true

# 2. 检查系统资源
echo ""
echo "2. 检查系统资源..."
echo "内存使用情况："
free -h
echo ""
echo "磁盘使用情况："
df -h /var/lib/docker 2>/dev/null || df -h

# 3. 备份现有数据（如果存在）
echo ""
echo "3. 备份现有数据..."
if docker volume ls | grep -q mysql_data; then
    backup_name="mysql_backup_$(date +%Y%m%d_%H%M%S)"
    echo "创建数据备份: $backup_name"
    docker run --rm -v yysls-ranking-backend_mysql_data:/source -v $(pwd):/backup alpine tar czf /backup/$backup_name.tar.gz -C /source .
    echo "✅ 备份完成: $backup_name.tar.gz"
else
    echo "未找到现有数据卷"
fi

# 4. 清理数据卷
echo ""
echo "4. 清理数据卷..."
read -p "是否要删除现有数据卷？这将清除所有数据 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker volume rm yysls-ranking-backend_mysql_data 2>/dev/null || true
    docker volume rm mysql_data 2>/dev/null || true
    echo "✅ 数据卷已清理"
else
    echo "保留现有数据卷"
fi

# 5. 创建最小化配置
echo ""
echo "5. 创建最小化 MySQL 配置..."
cat > mysql/conf.d/mysql-minimal.cnf << 'EOF'
# MySQL 8.0 最小化配置
# 用于解决启动问题

[mysqld]
# 基础配置
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 最小内存配置
innodb_buffer_pool_size=64M
innodb_log_file_size=16M
innodb_log_buffer_size=4M
key_buffer_size=8M

# 连接配置
max_connections=50
wait_timeout=28800

# 日志配置
log_error=stderr
general_log=0
slow_query_log=0

# 禁用性能模式以节省内存
performance_schema=OFF

# InnoDB 配置
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=2

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
EOF

# 备份原配置
if [ -f "mysql/conf.d/mysql.cnf" ]; then
    mv mysql/conf.d/mysql.cnf mysql/conf.d/mysql.cnf.backup
    echo "✅ 原配置已备份为 mysql.cnf.backup"
fi

echo "✅ 最小化配置已创建"

# 6. 修改 Docker Compose 配置
echo ""
echo "6. 优化 Docker Compose 配置..."

# 创建临时的简化配置
cat > docker-compose-db-test.yml << 'EOF'
version: '3.8'

services:
  db:
    image: mysql:8.0
    container_name: yysls-ranking-db-test
    restart: "no"  # 禁用自动重启以便调试
    environment:
      - MYSQL_ROOT_PASSWORD=test123456
      - MYSQL_DATABASE=yysls_ranking
      - MYSQL_USER=yysls_user
      - MYSQL_PASSWORD=test123456
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=64M
      --performance-schema=OFF
      --skip-log-bin
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./mysql/conf.d/mysql-minimal.cnf:/etc/mysql/conf.d/mysql.cnf:ro
    ports:
      - "3306:3306"

volumes:
  mysql_test_data:
EOF

echo "✅ 测试配置已创建"

# 7. 启动测试容器
echo ""
echo "7. 启动测试数据库容器..."
docker-compose -f docker-compose-db-test.yml up -d db

echo "等待数据库启动..."
sleep 10

# 8. 检查启动状态
echo ""
echo "8. 检查启动状态..."
if docker ps | grep -q yysls-ranking-db-test; then
    echo "✅ 数据库容器启动成功！"
    
    # 等待 MySQL 完全启动
    echo "等待 MySQL 服务就绪..."
    for i in {1..30}; do
        if docker exec yysls-ranking-db-test mysqladmin ping -h localhost -u root -ptest123456 2>/dev/null; then
            echo "✅ MySQL 服务就绪！"
            break
        fi
        echo "等待中... ($i/30)"
        sleep 2
    done
    
    # 测试数据库连接
    echo ""
    echo "9. 测试数据库连接..."
    if docker exec yysls-ranking-db-test mysql -u root -ptest123456 -e "SHOW DATABASES;" 2>/dev/null; then
        echo "✅ 数据库连接测试成功！"
        
        # 显示数据库状态
        echo ""
        echo "数据库状态："
        docker exec yysls-ranking-db-test mysql -u root -ptest123456 -e "SELECT VERSION();" 2>/dev/null
        
        echo ""
        echo "🎉 数据库修复成功！"
        echo ""
        echo "下一步操作："
        echo "1. 停止测试容器: docker-compose -f docker-compose-db-test.yml down"
        echo "2. 使用最小化配置启动正式服务:"
        echo "   cp mysql/conf.d/mysql-minimal.cnf mysql/conf.d/mysql.cnf"
        echo "   docker-compose -f docker-compose.prod.yml up -d"
        echo "3. 如果需要恢复数据，使用备份文件"
        
    else
        echo "❌ 数据库连接失败"
        docker logs yysls-ranking-db-test --tail=20
    fi
    
else
    echo "❌ 数据库容器启动失败"
    echo ""
    echo "查看错误日志："
    docker logs yysls-ranking-db-test --tail=50
    
    echo ""
    echo "可能的解决方案："
    echo "1. 检查系统内存是否充足（建议至少1GB可用）"
    echo "2. 检查磁盘空间是否充足"
    echo "3. 尝试使用更早版本的 MySQL: mysql:5.7"
    echo "4. 检查 Docker 版本兼容性"
fi

# 清理函数
cleanup() {
    echo ""
    echo "清理测试资源..."
    docker-compose -f docker-compose-db-test.yml down -v 2>/dev/null || true
    rm -f docker-compose-db-test.yml
}

# 询问是否清理
echo ""
read -p "是否清理测试资源？(Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    cleanup
fi

echo ""
echo "✅ 修复脚本执行完成"
