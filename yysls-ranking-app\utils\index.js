//获取系统信息、判断ipX安全距离
export const getTabbarHeight = function() {
    var systemInfo = uni.getSystemInfoSync()
    var data = {
        ...systemInfo,
        tabbarH: 50,//tabbar高度--单位px
        tabbarPaddingB: 0,//tabbar底部安全距离高度--单位px
        device: systemInfo.system.indexOf('iOS') != -1?'iOS':'Android', //苹果或者安卓设备
    }
    let modelArr = ['10,3','10,6','X', 'XR', 'XS', '11', '12', '13', '14', '15', '16'];
    let model = systemInfo.model;
    model && modelArr.forEach(item => {
        //适配iphoneX以上的底部，给tabbar一定高度的padding-bottom
        if(model.indexOf(item) != -1 && model.indexOf('iPhone') != -1) {
            data.tabbarH = 70
            data.tabbarPaddingB = 20
        }
    })
    return data;
}

export const getWXStatusHeight = function () {
    // 获取距上
    const barTop = wx.getSystemInfoSync().statusBarHeight
    // 获取胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    // 获取导航栏高度
    const barHeight = menuButtonInfo.height + (menuButtonInfo.top - barTop) * 2
    let barWidth = menuButtonInfo.width
 
    return {
        barHeight,
        barTop,
        barWidth
    }
}

// 提示语
export const openToast = function (text) {
   uni.showToast({
		title: text,
		icon: 'none'
   })
}

//调用上传图片
export const uploadFile = function (data) {
	//上传的文件信息
	console.log('upload file', data)
	// 文件上传的函数，返回一个promise
	//上传成功后返回上传后的图片地址，上传失败则返回false即可
	return new Promise((resolve, reject) => {
		//调用api上传，所有需要参数自行补充
		uni.uploadFile({
			url: 'https://dnftestapi.qlxiaoyuan.com/' + data.upUrl,
			name: data.name,
			header: data.header,
			formData: data.formData,
			filePath: data.path,
			success: (res) => {
				//以下成功或失败逻辑根据接口自行处理
				const data = JSON.parse(res.data.replace(/\ufeff/g, "") || "{}")

				if (data.code === 0) {
					console.log(data);
					//返回上传成功后的图片
					resolve(data)
				} else {
					openToast(data.msg)
					//上传失败
					reject(false)
				}
			},
			fail: (err) => {
				//上传失败
				reject(false)
			}
		})
	})
}