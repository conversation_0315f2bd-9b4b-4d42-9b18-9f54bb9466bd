"""
pytest配置和fixture定义

提供测试所需的通用fixture和配置
"""
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import create_application
from app.core.database import Base, get_db
from app.config import settings
from app.models.user import User, UserRole
from app.models.ranking import Ranking, RankingType, RankingStatus
from app.models.sponsor import Sponsor
from app.services.user_service import UserService
from app.utils.security import get_password_hash

# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

# 创建测试引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,
    future=True
)

# 创建测试会话工厂
TestSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest_asyncio.fixture(scope="session")
async def setup_test_db():
    """设置测试数据库"""
    # 创建所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield
    
    # 清理测试数据库
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture
async def db_session(setup_test_db) -> AsyncGenerator[AsyncSession, None]:
    """提供数据库会话"""
    async with TestSessionLocal() as session:
        yield session
        await session.rollback()


@pytest.fixture
def app():
    """创建FastAPI应用实例"""
    app = create_application()
    return app


@pytest.fixture
def client(app) -> Generator[TestClient, None, None]:
    """创建测试客户端"""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client(app, db_session) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    
    # 覆盖数据库依赖
    async def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """创建测试用户"""
    user_service = UserService()
    
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "nickname": "测试用户",
        "role": UserRole.USER,
        "is_active": True,
        "is_verified": True
    }
    
    user = await user_service.create(db_session, obj_in=user_data)
    return user


@pytest_asyncio.fixture
async def test_admin(db_session: AsyncSession) -> User:
    """创建测试管理员"""
    user_service = UserService()
    
    admin_data = {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "adminpassword123",
        "nickname": "管理员",
        "role": UserRole.ADMIN,
        "is_active": True,
        "is_verified": True
    }
    
    admin = await user_service.create(db_session, obj_in=admin_data)
    return admin


@pytest_asyncio.fixture
async def test_ranking(db_session: AsyncSession) -> Ranking:
    """创建测试榜单"""
    ranking_data = {
        "title": "测试榜单",
        "description": "这是一个测试榜单",
        "ranking_type": RankingType.FIVE_PERSON,
        "status": RankingStatus.DRAFT,
        "max_participants": 5,
        "start_time": None,
        "end_time": None
    }
    
    ranking = Ranking(**ranking_data)
    db_session.add(ranking)
    await db_session.commit()
    await db_session.refresh(ranking)
    
    return ranking


@pytest_asyncio.fixture
async def test_sponsor(db_session: AsyncSession) -> Sponsor:
    """创建测试赞助商"""
    sponsor_data = {
        "name": "测试赞助商",
        "description": "这是一个测试赞助商",
        "logo_url": "https://example.com/logo.png",
        "website_url": "https://example.com",
        "contact_person": "张三",
        "contact_phone": "13800138000",
        "contact_email": "<EMAIL>",
        "cooperation_level": "金牌",
        "display_order": 1,
        "is_active": True
    }
    
    sponsor = Sponsor(**sponsor_data)
    db_session.add(sponsor)
    await db_session.commit()
    await db_session.refresh(sponsor)
    
    return sponsor


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """生成认证头"""
    from app.utils.security import create_access_token
    from datetime import timedelta
    
    access_token = create_access_token(
        data={"sub": str(test_user.id), "username": test_user.username},
        expires_delta=timedelta(minutes=30)
    )
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_headers(test_admin: User) -> dict:
    """生成管理员认证头"""
    from app.utils.security import create_access_token
    from datetime import timedelta
    
    access_token = create_access_token(
        data={"sub": str(test_admin.id), "username": test_admin.username},
        expires_delta=timedelta(minutes=30)
    )
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_wechat_api():
    """模拟微信API"""
    from unittest.mock import Mock
    
    mock_api = Mock()
    mock_api.login_with_code.return_value = {
        "access_token": "mock_access_token",
        "openid": "mock_openid_123",
        "nickname": "微信用户",
        "headimgurl": "https://example.com/avatar.jpg",
        "sex": 1,
        "country": "中国",
        "province": "北京",
        "city": "北京"
    }
    
    return mock_api


@pytest.fixture
def mock_email_sender():
    """模拟邮件发送器"""
    from unittest.mock import AsyncMock
    
    mock_sender = AsyncMock()
    mock_sender.send_email.return_value = None
    
    return mock_sender


# 测试数据工厂
class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def user_data(**kwargs):
        """用户测试数据"""
        default_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "nickname": "测试用户",
            "role": UserRole.USER,
            "is_active": True,
            "is_verified": True
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def ranking_data(**kwargs):
        """榜单测试数据"""
        default_data = {
            "title": "测试榜单",
            "description": "这是一个测试榜单",
            "ranking_type": RankingType.FIVE_PERSON,
            "status": RankingStatus.DRAFT,
            "max_participants": 5
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def sponsor_data(**kwargs):
        """赞助商测试数据"""
        default_data = {
            "name": "测试赞助商",
            "description": "这是一个测试赞助商",
            "logo_url": "https://example.com/logo.png",
            "website_url": "https://example.com",
            "contact_person": "张三",
            "contact_phone": "13800138000",
            "contact_email": "<EMAIL>",
            "cooperation_level": "金牌",
            "display_order": 1,
            "is_active": True
        }
        default_data.update(kwargs)
        return default_data


@pytest.fixture
def test_data_factory():
    """提供测试数据工厂"""
    return TestDataFactory
