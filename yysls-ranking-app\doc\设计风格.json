{"designSystem": {"name": "精致水墨风游戏界面设计系统", "description": "一个用于复现具有精致水墨风格和古典美学游戏界面的设计系统。强调优雅、沉浸感和视觉的层次感。", "styleGuide": {"overallTheme": "水墨山水与古典武侠融合", "colorPalette": {"primaryColors": [{"name": "深沉墨黑", "hex": "#1A1A1A", "description": "用于背景和主要文本，营造深邃感"}, {"name": "沉稳灰", "hex": "#4A4A4A", "description": "用于次要背景和装饰元素，增加层次"}, {"name": "点缀金/朱红", "hex": "#D4AF37", "description": "用于重要按钮、高亮和标题，提供视觉焦点和华丽感"}, {"name": "柔和米白", "hex": "#F5F5DC", "description": "用于正文和辅助信息，提供清晰的阅读体验"}], "accentColors": [{"name": "水墨渐变", "description": "背景元素和分割线常用，模拟水墨晕染效果"}, {"name": "淡雅青色", "description": "作为辅助色，用于点缀和背景细节"}]}, "typography": {"fontFamily": "字酷堂清楷体或其他具有古典韵味的中文字体", "headingSizes": {"h1": {"size": "大", "weight": "粗", "style": "艺术化处理，融入水墨或书法笔触"}, "h2": {"size": "中等", "weight": "半粗", "style": "标题常用，简洁有力"}, "h3": {"size": "小", "weight": "常规", "style": "副标题或段落标题"}}, "bodyText": {"size": "常规", "weight": "常规", "lineHeight": "适中，利于阅读"}, "specialCharacters": "少量具有东方美学或武侠感的符号"}, "imageryStyle": {"dominantStyle": "水墨山水画，意境深远", "elements": ["远山、近水、云雾缭绕", "古建筑剪影（亭台楼阁、宝塔）", "竹林、梅花、枯藤老树等自然元素", "动态元素模拟烟雾、水波或粒子效果"], "usage": "作为背景、装饰元素，或在视频预览框周围形成边框效果"}, "iconography": {"style": "简洁的线条勾勒，扁平化或微拟物化，融入中国传统纹样或元素", "colors": "与整体色调保持一致，常为金色、白色或与背景形成对比的深色"}}, "layoutAndStructure": {"gridSystem": "灵活的网格布局，内容区居中对齐，留有足够的呼吸空间", "spacing": "大间距，营造大气和空灵感，元素之间有清晰的视觉分隔", "sections": [{"name": "顶部导航/状态栏", "placement": "屏幕顶部，简洁，通常包含返回、设置等通用功能"}, {"name": "主视觉展示区", "placement": "页面中上部，用于展示核心内容，如游戏视频、主要宣传图"}, {"name": "内容说明区", "placement": "主视觉下方，用于详细描述游戏世界观、特色等，通常为多段落文本"}, {"name": "行动召唤区 (CTA)", "placement": "内容说明下方，包含主要下载按钮等，突出显示"}, {"name": "底部功能区", "placement": "屏幕底部，包含分享、进入官网等次要功能"}], "componentPlacement": {"buttons": "居中或左右对齐，大小适中，易于点击，通常带有渐变和描边效果", "textBlocks": "通常左对齐或居中对齐，段落之间有明显间距", "videoPlayer": "居中显示，带有播放按钮覆盖层，周围有装饰性边框"}}, "interactiveElements": {"buttons": {"style": "矩形或圆角矩形，带有渐变填充（如朱红到深红，或金到棕），边框清晰，文本居中", "hoverState": "背景颜色或边框颜色轻微变化，或有光效反馈", "activeState": "按下效果，颜色加深或轻微下陷"}, "navigation": {"style": "顶部或底部导航，图标或文字清晰，简洁", "feedback": "选中状态有颜色高亮或底部指示线"}}, "animations": {"transitions": "平滑的淡入淡出、滑动效果，模拟水墨晕染或笔触划过", "microinteractions": "按钮点击、加载时的微动效，增加精致感", "backgroundAnimations": "慢速移动的云雾、飘落的花瓣、涟漪水波纹等，增加沉浸感"}, "responsiveDesign": {"adaptability": "主要为移动端优化，但需考虑PC端显示时的适配，调整内容区宽度和元素排布", "scaling": "字体、图片和按钮大小按比例缩放，保持视觉平衡"}, "accessibility": {"contrast": "文本与背景之间保持足够的对比度以保证可读性", "touchTargets": "按钮和可点击区域足够大，便于移动设备操作"}}}