#!/usr/bin/env python3
"""
系统配置初始化脚本

用于创建系统的基础配置项
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.system_config import SystemConfig


def create_system_configs():
    """创建系统配置"""
    db = SessionLocal()

    # 系统配置数据
    configs = [
        # 基础配置
        {
            "config_key": "site_name",
            "config_value": "燕友圈榜单系统",
            "config_type": "string",
            "name": "网站名称",
            "description": "网站的显示名称",
            "is_public": True
        },
        {
            "config_key": "site_description", 
            "config_value": "专业的竞速榜单管理系统",
            "config_type": "basic",
            "name": "网站描述",
            "description": "网站的描述信息",
            "value_type": "string",
            "is_active": True,
            "is_public": True,
            "group_name": "基础设置",
            "sort_order": 2
        },
        {
            "config_key": "site_keywords",
            "config_value": "榜单,竞速,排名,燕友圈",
            "config_type": "basic", 
            "name": "网站关键词",
            "description": "网站SEO关键词",
            "value_type": "string",
            "is_active": True,
            "is_public": True,
            "group_name": "基础设置",
            "sort_order": 3
        },
        
        # 功能配置
        {
            "config_key": "enable_registration",
            "config_value": "true",
            "config_type": "feature",
            "name": "开放用户注册",
            "description": "是否允许新用户注册",
            "value_type": "boolean",
            "is_active": True,
            "is_public": False,
            "group_name": "功能设置",
            "sort_order": 10
        },
        {
            "config_key": "enable_wechat_login",
            "config_value": "true",
            "config_type": "feature",
            "name": "启用微信登录",
            "description": "是否启用微信登录功能",
            "value_type": "boolean",
            "is_active": True,
            "is_public": False,
            "group_name": "功能设置",
            "sort_order": 11
        },
        {
            "config_key": "enable_broadcast",
            "config_value": "true",
            "config_type": "feature",
            "name": "启用播报功能",
            "description": "是否启用系统播报功能",
            "value_type": "boolean",
            "is_active": True,
            "is_public": True,
            "group_name": "功能设置",
            "sort_order": 12
        },
        
        # 榜单配置
        {
            "config_key": "max_five_person_rankings",
            "config_value": "10",
            "config_type": "ranking",
            "name": "5人榜单最大数量",
            "description": "系统允许的5人榜单最大数量",
            "value_type": "integer",
            "is_active": True,
            "is_public": False,
            "group_name": "榜单设置",
            "sort_order": 20
        },
        {
            "config_key": "max_ten_person_rankings",
            "config_value": "5",
            "config_type": "ranking",
            "name": "10人榜单最大数量",
            "description": "系统允许的10人榜单最大数量",
            "value_type": "integer",
            "is_active": True,
            "is_public": False,
            "group_name": "榜单设置",
            "sort_order": 21
        },
        
        # 安全配置
        {
            "config_key": "jwt_expire_minutes",
            "config_value": "30",
            "config_type": "security",
            "name": "JWT过期时间",
            "description": "JWT令牌过期时间（分钟）",
            "value_type": "integer",
            "is_active": True,
            "is_public": False,
            "group_name": "安全设置",
            "sort_order": 30
        },
        {
            "config_key": "password_min_length",
            "config_value": "6",
            "config_type": "security",
            "name": "密码最小长度",
            "description": "用户密码的最小长度要求",
            "value_type": "integer",
            "is_active": True,
            "is_public": False,
            "group_name": "安全设置",
            "sort_order": 31
        },
        
        # 上传配置
        {
            "config_key": "upload_max_size",
            "config_value": "10485760",
            "config_type": "upload",
            "name": "文件上传最大大小",
            "description": "文件上传最大大小（字节）",
            "value_type": "integer",
            "is_active": True,
            "is_public": False,
            "group_name": "上传设置",
            "sort_order": 40
        },
        {
            "config_key": "allowed_file_types",
            "config_value": "jpg,jpeg,png,gif,xlsx,xls",
            "config_type": "upload",
            "name": "允许的文件类型",
            "description": "允许上传的文件扩展名",
            "value_type": "string",
            "is_active": True,
            "is_public": False,
            "group_name": "上传设置",
            "sort_order": 41
        }
    ]
    
    try:
        created_count = 0
        updated_count = 0
        
        for config_data in configs:
            # 检查配置是否已存在
            existing = db.query(SystemConfig).filter(
                SystemConfig.config_key == config_data['config_key']
            ).first()
            
            if existing:
                print(f"⚠️  配置已存在，跳过: {config_data['config_key']}")
                updated_count += 1
            else:
                # 创建新配置
                config = SystemConfig(**config_data)
                db.add(config)
                created_count += 1
                print(f"✅ 创建配置: {config_data['config_key']} = {config_data['config_value']}")
        
        db.commit()
        
        print(f"\n🎉 系统配置初始化完成！")
        print(f"   新创建: {created_count} 个配置")
        print(f"   已存在: {updated_count} 个配置")
        
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建系统配置失败: {str(e)}")
        return False
        
    finally:
        db.close()


def check_system_configs():
    """检查现有的系统配置"""
    db = SessionLocal()
    
    try:
        configs = db.query(SystemConfig).order_by(
            SystemConfig.group_name, SystemConfig.sort_order
        ).all()
        
        if not configs:
            print("📋 当前没有系统配置")
            return []
        
        print(f"📋 当前系统配置列表 (共{len(configs)}个):")
        
        current_group = None
        for config in configs:
            if config.group_name != current_group:
                current_group = config.group_name
                print(f"\n📁 {current_group}:")
            
            status = "✅ 启用" if config.is_active else "❌ 禁用"
            public = "🌐 公开" if config.is_public else "🔒 私有"
            print(f"   - {config.config_key}: {config.config_value}")
            print(f"     名称: {config.name}")
            print(f"     状态: {status} | {public} | 类型: {config.value_type}")
        
        return configs
        
    except Exception as e:
        print(f"❌ 查询系统配置失败: {str(e)}")
        return []
        
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 燕友圈榜单系统 - 系统配置初始化工具")
    print("=" * 50)
    
    # 检查现有配置
    print("1. 检查现有系统配置...")
    existing_configs = check_system_configs()
    
    if existing_configs:
        print("\n2. 是否要创建缺失的配置？")
        choice = input("   输入 'y' 继续创建，其他键退出: ").lower().strip()
        if choice != 'y':
            print("👋 操作已取消")
            return
    
    print("\n2. 创建系统配置...")
    success = create_system_configs()
    
    if success:
        print("\n🎉 初始化完成！")
        print("\n💡 使用说明:")
        print("   1. 配置已创建到数据库中")
        print("   2. 可通过管理后台修改配置值")
        print("   3. 部分配置对前端公开，可直接访问")
        print("\n📝 配置分组:")
        print("   - 基础设置: 网站基本信息")
        print("   - 功能设置: 功能开关")
        print("   - 榜单设置: 榜单相关限制")
        print("   - 安全设置: 安全相关配置")
        print("   - 上传设置: 文件上传配置")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
