import { getTabbarHeight } from '@/utils/index.js';

let baseUrl = "";
if (process.env.NODE_ENV === 'development') {
	// 开发环境
	baseUrl = 'https://dnftestapi.qlxiaoyuan.com/'
} else if (process.env.NODE_ENV === 'production') {
	// 生产环境
	baseUrl = 'https://dnfapi.qlxiaoyuan.com'
}

let systemInfo = {
    ...getTabbarHeight(),
    // #ifdef MP-ALIPAY
    navBarH: uni.getSystemInfoSync().statusBarHeight + uni.getSystemInfoSync().titleBarHeight, //菜单栏总高度--单位px
    titleBarHeight: uni.getSystemInfoSync().titleBarHeight, //标题栏高度--单位px
    // #endif
    // #ifndef MP-ALIPAY
    navBarH: uni.getSystemInfoSync().statusBarHeight + 44, //菜单栏总高度--单位px
    titleBarHeight: 44, //标题栏高度--单位px
    // #endif
};

console.log(systemInfo,'systemInfo')
const courtConfig = {
	baseUrl: baseUrl,//域名
	imgUrl: '',
    systemInfo: systemInfo,//系统信息
};
export default Object.assign({}, courtConfig);
