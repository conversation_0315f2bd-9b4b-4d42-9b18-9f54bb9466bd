#!/usr/bin/env python3
"""
系统配置初始化脚本（简化版）
根据实际数据库DDL结构创建配置
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import SessionLocal
from app.models.system_config import SystemConfig


def create_system_configs():
    """创建系统配置"""
    db = SessionLocal()
    
    # 系统配置数据（根据实际DDL结构）
    configs = [
        # 基础配置
        {
            "config_key": "site_name",
            "config_value": "燕友圈榜单系统",
            "config_type": "string",
            "name": "网站名称",
            "description": "网站的显示名称",
            "is_public": True
        },
        {
            "config_key": "site_description",
            "config_value": "专业的竞速榜单管理系统",
            "config_type": "string",
            "name": "网站描述",
            "description": "网站的描述信息",
            "is_public": True
        },
        {
            "config_key": "site_keywords",
            "config_value": "榜单,竞速,排名,燕友圈",
            "config_type": "string",
            "name": "SEO关键词",
            "description": "网站SEO关键词",
            "is_public": True
        },
        
        # 功能配置
        {
            "config_key": "enable_registration",
            "config_value": "true",
            "config_type": "boolean",
            "name": "开放用户注册",
            "description": "是否允许新用户注册",
            "is_public": False
        },
        {
            "config_key": "enable_wechat_login",
            "config_value": "true",
            "config_type": "boolean",
            "name": "启用微信登录",
            "description": "是否启用微信登录功能",
            "is_public": False
        },
        {
            "config_key": "enable_broadcast",
            "config_value": "true",
            "config_type": "boolean",
            "name": "启用播报功能",
            "description": "是否启用榜单播报功能",
            "is_public": True
        },
        
        # 榜单配置
        {
            "config_key": "max_five_person_rankings",
            "config_value": "10",
            "config_type": "integer",
            "name": "5人榜单最大数量",
            "description": "系统允许的5人榜单最大数量",
            "is_public": False
        },
        {
            "config_key": "max_ten_person_rankings",
            "config_value": "5",
            "config_type": "integer",
            "name": "10人榜单最大数量",
            "description": "系统允许的10人榜单最大数量",
            "is_public": False
        },
        
        # 安全配置
        {
            "config_key": "jwt_expire_minutes",
            "config_value": "30",
            "config_type": "integer",
            "name": "JWT过期时间",
            "description": "JWT令牌过期时间（分钟）",
            "is_public": False
        },
        {
            "config_key": "password_min_length",
            "config_value": "6",
            "config_type": "integer",
            "name": "密码最小长度",
            "description": "用户密码最小长度要求",
            "is_public": False
        },
        
        # 上传配置
        {
            "config_key": "upload_max_size",
            "config_value": "10485760",
            "config_type": "integer",
            "name": "文件上传最大大小",
            "description": "文件上传最大大小（字节）",
            "is_public": False
        },
        {
            "config_key": "allowed_file_types",
            "config_value": "jpg,jpeg,png,gif,xlsx,xls",
            "config_type": "string",
            "name": "允许的文件类型",
            "description": "允许上传的文件类型",
            "is_public": False
        }
    ]
    
    try:
        created_count = 0
        updated_count = 0
        
        for config_data in configs:
            # 检查配置是否已存在
            existing_config = db.query(SystemConfig).filter(
                SystemConfig.config_key == config_data["config_key"]
            ).first()
            
            if existing_config:
                # 更新现有配置
                for key, value in config_data.items():
                    if key != "config_key":  # 不更新主键
                        setattr(existing_config, key, value)
                updated_count += 1
                print(f"✅ 更新配置: {config_data['config_key']}")
            else:
                # 创建新配置
                config = SystemConfig(**config_data)
                db.add(config)
                created_count += 1
                print(f"🆕 创建配置: {config_data['config_key']}")
        
        # 提交事务
        db.commit()
        
        print(f"\n🎉 系统配置初始化完成!")
        print(f"   创建: {created_count} 个配置")
        print(f"   更新: {updated_count} 个配置")
        print(f"   总计: {len(configs)} 个配置")
        
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 燕友圈榜单系统 - 系统配置初始化工具")
    print("=" * 50)
    
    success = create_system_configs()
    
    if success:
        print("\n💡 接下来可以:")
        print("   1. 启动API服务器")
        print("   2. 访问 /api/v1/system-config/public 查看公开配置")
        print("   3. 通过管理后台管理配置")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
