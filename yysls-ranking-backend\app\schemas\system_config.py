"""
系统配置相关的Pydantic模型
"""
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, Field, ConfigDict


class SystemConfigBase(BaseModel):
    """系统配置基础模型"""
    config_key: str = Field(..., description="配置键", max_length=100)
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: str = Field("string", description="配置类型", max_length=20)
    name: Optional[str] = Field(None, description="配置名称", max_length=30)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    is_public: bool = Field(False, description="是否公开（前端可访问）")


class SystemConfigCreate(SystemConfigBase):
    """创建系统配置的请求模型"""
    pass


class SystemConfigUpdate(BaseModel):
    """更新系统配置的请求模型"""
    id:Optional[int] = Field(None, description="配置ID")
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: Optional[str] = Field(None, description="配置类型", max_length=20)
    name: Optional[str] = Field(None, description="配置名称", max_length=30)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    is_public: Optional[bool] = Field(None, description="是否公开（前端可访问）")


class SystemConfigResponse(SystemConfigBase):
    """系统配置响应模型"""
    id: int = Field(..., description="配置ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class SystemConfigPublic(BaseModel):
    """公开系统配置模型（不需要认证）"""
    config_key: str = Field(..., description="配置键")
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: str = Field(..., description="配置类型")
    name: Optional[str] = Field(None, description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")

    model_config = ConfigDict(from_attributes=True)


class ConfigBatchUpdate(BaseModel):
    """批量更新配置的请求模型"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
