<template>
	<view class="ret-btn l-flex-center" @click="handleBackClick">
		<image src="/static/image/home.png" mode=""></image>
		<view class="text">首页</view>
	</view>
</template>

<script setup>
	const handleBackClick = () => {
		uni.navigateTo({
			url: '/pages/index/index'
		})
	}
</script>

<style scoped lang="scss">
	.ret-btn {
		position: absolute;
		bottom: 308rpx;
		right: 30rpx;
		width: 88rpx;
		height: 88rpx;
		background: #27A2FF;
		border-radius: 100%;
		flex-direction: column;

		image {
			width: 36rpx;
			height: 36rpx;
		}

		.text {
			font-size: 20rpx;
			color: #FFFFFF;
		}
	}
</style>