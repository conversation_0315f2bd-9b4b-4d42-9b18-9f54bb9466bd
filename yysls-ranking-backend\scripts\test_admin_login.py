#!/usr/bin/env python3
"""
测试管理员用户登录功能

验证创建的admin用户是否可以正常登录
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User, UserRole
from app.utils.security import verify_password, create_access_token
from datetime import timedelta


def test_admin_login():
    """测试管理员登录"""
    db = SessionLocal()
    
    try:
        print("🔍 测试管理员用户登录功能...")
        print("=" * 40)
        
        # 1. 查找admin用户
        print("1. 查找admin用户...")
        admin_user = db.query(User).filter(User.username == "admin").first()
        
        if not admin_user:
            print("❌ 未找到admin用户，请先运行 init_admin.py")
            return False
        
        print(f"✅ 找到admin用户 (ID: {admin_user.id})")
        print(f"   用户名: {admin_user.username}")
        print(f"   昵称: {admin_user.nickname}")
        print(f"   角色: {admin_user.role}")
        print(f"   状态: {'激活' if admin_user.is_active else '禁用'}")
        
        # 2. 验证密码
        print("\n2. 验证密码...")
        test_password = "123456"
        
        if not admin_user.password_hash:
            print("❌ 用户没有设置密码哈希")
            return False
        
        password_valid = verify_password(test_password, admin_user.password_hash)
        
        if password_valid:
            print("✅ 密码验证成功")
        else:
            print("❌ 密码验证失败")
            return False
        
        # 3. 生成访问令牌
        print("\n3. 生成访问令牌...")
        try:
            token_data = {
                "sub": str(admin_user.id),
                "username": admin_user.username,
                "role": admin_user.role
            }
            
            access_token = create_access_token(
                data=token_data,
                expires_delta=timedelta(minutes=30)
            )
            
            print("✅ 访问令牌生成成功")
            print(f"   令牌长度: {len(access_token)} 字符")
            print(f"   令牌前缀: {access_token[:50]}...")
            
        except Exception as e:
            print(f"❌ 生成访问令牌失败: {str(e)}")
            return False
        
        # 4. 检查权限
        print("\n4. 检查管理员权限...")
        if admin_user.role == UserRole.ADMIN.value:
            print("✅ 用户具有管理员权限")
        else:
            print(f"❌ 用户权限不足，当前角色: {admin_user.role}")
            return False
        
        print("\n🎉 管理员用户登录测试全部通过！")
        
        # 5. 显示登录信息
        print("\n📋 登录信息:")
        print(f"   API端点: POST /api/v1/auth/login")
        print(f"   用户名: admin")
        print(f"   密码: 123456")
        print(f"   返回令牌: Bearer {access_token[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False
        
    finally:
        db.close()


def show_curl_example():
    """显示curl测试示例"""
    print("\n💡 API测试示例:")
    print("=" * 40)
    
    print("1. 启动服务器:")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n2. 测试登录API (使用curl):")
    curl_cmd = '''curl -X POST "http://localhost:8000/api/v1/auth/login" \\
     -H "Content-Type: application/json" \\
     -d '{
       "username": "admin",
       "password": "123456"
     }' '''
    print(curl_cmd)
    
    print("\n3. 使用返回的token访问受保护的API:")
    protected_curl = '''curl -X GET "http://localhost:8000/api/v1/rankings" \\
     -H "Authorization: Bearer YOUR_TOKEN_HERE"'''
    print(protected_curl)
    
    print("\n4. 访问Swagger文档:")
    print("   http://localhost:8000/docs")


def main():
    """主函数"""
    print("🧪 燕友圈榜单系统 - 管理员登录测试工具")
    print("=" * 50)
    
    # 测试登录功能
    success = test_admin_login()
    
    if success:
        # 显示使用示例
        show_curl_example()
        
        print("\n✅ 测试完成！管理员用户可以正常使用。")
    else:
        print("\n❌ 测试失败！请检查错误信息并重新初始化。")
        sys.exit(1)


if __name__ == "__main__":
    main()
