"""
通用响应模型
"""
from typing import Any, Generic, List, Optional, TypeVar, TYPE_CHECKING
from pydantic import BaseModel, Field

if TYPE_CHECKING:
    from app.schemas.user import UserResponse

# 泛型类型变量
T = TypeVar('T')


class ResponseModel(BaseModel, Generic[T]):
    """通用响应模型"""
    code: int = Field(default=200, description="响应码，200表示成功")
    message: str = Field(default="success", description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")

    class Config:
        json_encoders = {
            # 可以在这里添加自定义的JSON编码器
        }


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T] = Field(description="数据列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    pages: int = Field(description="总页数")

    class Config:
        json_encoders = {
            # 可以在这里添加自定义的JSON编码器
        }


class TokenResponse(BaseModel):
    """Token响应模型"""
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="过期时间(秒)")
    user: Optional[Any] = Field(default=None, description="用户信息")
    refresh_token: Optional[str] = Field(default=None, description="刷新令牌")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    code: int = Field(description="错误码")
    message: str = Field(description="错误消息")
    detail: Optional[str] = Field(default=None, description="错误详情")
    errors: Optional[List[dict]] = Field(default=None, description="字段错误列表")


class SuccessResponse(BaseModel):
    """成功响应模型"""
    code: int = Field(default=0, description="响应码")
    message: str = Field(default="操作成功", description="响应消息")
    data: Optional[Any] = Field(default=None, description="响应数据")
