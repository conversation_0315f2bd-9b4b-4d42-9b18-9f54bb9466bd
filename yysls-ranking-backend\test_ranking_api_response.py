#!/usr/bin/env python3
"""
测试榜单API响应中的中文名称字段
"""
import json
from datetime import datetime

# 模拟API响应数据结构
def simulate_api_response():
    """模拟API响应数据"""
    
    # 模拟数据库中的榜单数据
    mock_rankings = [
        {
            "id": 1,
            "name": "第1期5人竞速榜单",
            "period": 1,
            "ranking_type": "5_person",
            "start_time": "2024-01-01T10:00:00",
            "end_time": "2024-01-01T18:00:00",
            "team_size_limit": 5,
            "total_participants": 25,
            "status": "in_progress",
            "created_at": "2024-01-01T09:00:00",
            "updated_at": "2024-01-01T09:00:00"
        },
        {
            "id": 2,
            "name": "第1期10人竞速榜单",
            "period": 1,
            "ranking_type": "10_person",
            "start_time": "2024-01-02T10:00:00",
            "end_time": "2024-01-02T18:00:00",
            "team_size_limit": 10,
            "total_participants": 50,
            "status": "finished",
            "created_at": "2024-01-02T09:00:00",
            "updated_at": "2024-01-02T19:00:00"
        }
    ]
    
    # 模拟添加中文名称字段的逻辑
    type_mapping = {
        "5_person": "5人榜单",
        "10_person": "10人榜单"
    }
    
    # 为每个榜单添加中文名称字段
    for ranking in mock_rankings:
        ranking["ranking_type_name"] = type_mapping.get(ranking["ranking_type"], ranking["ranking_type"])
    
    # 构建完整的API响应
    api_response = {
        "code": 200,
        "message": "获取榜单列表成功",
        "data": {
            "items": mock_rankings,
            "total": len(mock_rankings),
            "page": 1,
            "size": 10,
            "pages": 1
        }
    }
    
    return api_response


def test_api_response_structure():
    """测试API响应结构"""
    print("测试API响应结构...")
    
    response = simulate_api_response()
    
    # 验证响应结构
    assert "code" in response, "响应中缺少 code 字段"
    assert "message" in response, "响应中缺少 message 字段"
    assert "data" in response, "响应中缺少 data 字段"
    
    data = response["data"]
    assert "items" in data, "响应数据中缺少 items 字段"
    assert "total" in data, "响应数据中缺少 total 字段"
    assert "page" in data, "响应数据中缺少 page 字段"
    assert "size" in data, "响应数据中缺少 size 字段"
    assert "pages" in data, "响应数据中缺少 pages 字段"
    
    print("✅ API响应结构验证通过")


def test_ranking_type_name_field():
    """测试榜单类型中文名称字段"""
    print("\n测试榜单类型中文名称字段...")
    
    response = simulate_api_response()
    rankings = response["data"]["items"]
    
    for ranking in rankings:
        print(f"\n榜单 {ranking['id']}:")
        print(f"  名称: {ranking['name']}")
        print(f"  类型: {ranking['ranking_type']}")
        print(f"  类型中文名称: {ranking['ranking_type_name']}")
        print(f"  状态: {ranking['status']}")
        print(f"  参与人数: {ranking['total_participants']}")
        
        # 验证必要字段存在
        assert "ranking_type_name" in ranking, f"榜单 {ranking['id']} 缺少 ranking_type_name 字段"
        
        # 验证中文名称映射正确
        if ranking["ranking_type"] == "5_person":
            assert ranking["ranking_type_name"] == "5人榜单", f"5人榜单的中文名称应为 '5人榜单'，实际为 '{ranking['ranking_type_name']}'"
        elif ranking["ranking_type"] == "10_person":
            assert ranking["ranking_type_name"] == "10人榜单", f"10人榜单的中文名称应为 '10人榜单'，实际为 '{ranking['ranking_type_name']}'"
    
    print("\n✅ 榜单类型中文名称字段验证通过")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n测试向后兼容性...")
    
    response = simulate_api_response()
    rankings = response["data"]["items"]
    
    # 验证原有字段仍然存在
    required_fields = [
        "id", "name", "period", "ranking_type", "start_time", "end_time",
        "team_size_limit", "total_participants", "status", "created_at", "updated_at"
    ]
    
    for ranking in rankings:
        for field in required_fields:
            assert field in ranking, f"榜单 {ranking['id']} 缺少必要字段 '{field}'"
    
    print("✅ 向后兼容性验证通过")


def display_sample_response():
    """显示示例响应"""
    print("\n示例API响应:")
    print("=" * 50)
    
    response = simulate_api_response()
    print(json.dumps(response, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    print("开始测试榜单API响应中的中文名称字段...\n")
    
    try:
        test_api_response_structure()
        test_ranking_type_name_field()
        test_backward_compatibility()
        display_sample_response()
        
        print("\n🎉 所有测试通过！API响应格式正确，包含榜单类型中文名称字段。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
