[global]
# 主要源：清华大学 PyPI 镜像
index-url = https://pypi.tuna.tsinghua.edu.cn/simple

# 备用源：阿里云、豆瓣、中科大
extra-index-url = https://mirrors.aliyun.com/pypi/simple/
                  https://pypi.douban.com/simple/
                  https://pypi.mirrors.ustc.edu.cn/simple/

# 信任的主机
trusted-host = pypi.tuna.tsinghua.edu.cn
               mirrors.aliyun.com
               pypi.douban.com
               pypi.mirrors.ustc.edu.cn

# 超时设置
timeout = 120

# 重试次数
retries = 3

[install]
# 不使用缓存目录（适合Docker环境）
no-cache-dir = true

# 不检查证书（如果遇到SSL问题）
# trusted-host = pypi.org
