"""
API接口测试脚本

用于验证API接口是否正常工作
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import create_application

def test_api_endpoints():
    """测试API端点是否正常"""
    
    # 创建测试客户端
    app = create_application()
    client = TestClient(app)
    
    print("🚀 开始测试API接口...")
    
    # 测试根路径
    print("\n1. 测试根路径...")
    response = client.get("/")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"   响应: {response.json()}")
        print("   ✅ 根路径测试通过")
    else:
        print("   ❌ 根路径测试失败")
    
    # 测试健康检查
    print("\n2. 测试健康检查...")
    response = client.get("/health")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"   响应: {response.json()}")
        print("   ✅ 健康检查测试通过")
    else:
        print("   ❌ 健康检查测试失败")
    
    # 测试API文档
    print("\n3. 测试API文档...")
    response = client.get("/docs")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ API文档可访问")
    else:
        print("   ❌ API文档访问失败")
    
    # 测试OpenAPI规范
    print("\n4. 测试OpenAPI规范...")
    response = client.get("/openapi.json")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        openapi_data = response.json()
        print(f"   API标题: {openapi_data.get('info', {}).get('title', 'N/A')}")
        print(f"   API版本: {openapi_data.get('info', {}).get('version', 'N/A')}")
        print(f"   路径数量: {len(openapi_data.get('paths', {}))}")
        print("   ✅ OpenAPI规范测试通过")
    else:
        print("   ❌ OpenAPI规范测试失败")
    
    # 测试系统配置公开接口（不需要认证）
    print("\n5. 测试系统配置公开接口...")
    response = client.get("/api/v1/system/public")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 系统配置公开接口测试通过")
    elif response.status_code == 500:
        print("   ⚠️  系统配置接口返回500（可能是数据库未连接）")
    else:
        print(f"   ❌ 系统配置公开接口测试失败: {response.status_code}")
    
    # 测试公告接口（不需要认证）
    print("\n6. 测试公告接口...")
    response = client.get("/api/v1/content/public/announcements")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 公告接口测试通过")
    elif response.status_code == 500:
        print("   ⚠️  公告接口返回500（可能是数据库未连接）")
    else:
        print(f"   ❌ 公告接口测试失败: {response.status_code}")
    
    # 测试需要认证的接口（应该返回401）
    print("\n7. 测试需要认证的接口...")
    response = client.get("/api/v1/users/me")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 401:
        print("   ✅ 认证保护正常工作")
    elif response.status_code == 422:
        print("   ✅ 参数验证正常工作")
    else:
        print(f"   ⚠️  认证接口返回意外状态码: {response.status_code}")
    
    print("\n🎉 API接口测试完成！")
    print("\n📝 测试总结:")
    print("   - 基础路由正常工作")
    print("   - API文档可以访问")
    print("   - OpenAPI规范正确生成")
    print("   - 认证保护机制正常")
    print("   - 公开接口可以访问（如果数据库连接正常）")
    
    return True

def test_api_structure():
    """测试API结构"""
    print("\n🔍 检查API结构...")
    
    app = create_application()
    client = TestClient(app)
    
    # 获取OpenAPI规范
    response = client.get("/openapi.json")
    if response.status_code != 200:
        print("   ❌ 无法获取OpenAPI规范")
        return False
    
    openapi_data = response.json()
    paths = openapi_data.get('paths', {})
    
    # 检查主要API路径
    expected_paths = [
        '/api/v1/auth/login',
        '/api/v1/auth/wechat-login',
        '/api/v1/auth/refresh',
        '/api/v1/auth/me',
        '/api/v1/rankings',
        '/api/v1/users',
        '/api/v1/users/me',
        '/api/v1/sponsors',
        '/api/v1/system/public',
        '/api/v1/content/public/announcements',
    ]
    
    print(f"   总路径数: {len(paths)}")
    print("   检查关键路径:")
    
    for path in expected_paths:
        if path in paths:
            methods = list(paths[path].keys())
            print(f"   ✅ {path} ({', '.join(methods)})")
        else:
            print(f"   ❌ {path} (缺失)")
    
    # 检查标签分组
    tags = set()
    for path_data in paths.values():
        for method_data in path_data.values():
            if 'tags' in method_data:
                tags.update(method_data['tags'])
    
    print(f"\n   API标签: {', '.join(sorted(tags))}")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 燕友圈榜单系统 API 测试")
    print("=" * 60)
    
    try:
        # 测试API端点
        test_api_endpoints()
        
        # 测试API结构
        test_api_structure()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！API接口层开发成功！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
