"""
赞助商相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class SponsorBase(BaseModel):
    """赞助商基础模型"""
    name: str = Field(..., min_length=1, max_length=200, description="赞助商名称")
    logo_url: Optional[str] = Field(None, max_length=500, description="Logo URL（用作头像）")
    sort_order: int = Field(default=0, description="排序顺序")
    is_active: bool = Field(default=True, description="是否启用")


class SponsorCreate(SponsorBase):
    """创建赞助商模型"""
    pass


class SponsorUpdate(BaseModel):
    """更新赞助商模型"""
    id: int = Field(..., description="赞助商ID")
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="赞助商名称")
    logo_url: Optional[str] = Field(None, max_length=500, description="Logo URL（用作头像）")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")


class SponsorResponse(SponsorBase):
    """赞助商响应模型"""
    id: int = Field(description="赞助商ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

    class Config:
        from_attributes = True


class SponsorListRequest(BaseModel):
    """赞助商列表请求模型"""
    is_active: Optional[bool] = Field(None, description="是否启用")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class SponsorSearchRequest(BaseModel):
    """赞助商搜索请求模型"""
    keyword: str = Field(..., min_length=1, description="搜索关键词")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")
