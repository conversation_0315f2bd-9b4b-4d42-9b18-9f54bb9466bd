#!/usr/bin/env python3
"""
测试微信小程序登录功能
"""
import asyncio
import httpx
import json
from app.utils.wechat import wechat_api, WeChatAPIError


async def test_jscode2session():
    """测试jscode2session接口"""
    print("=== 测试微信小程序jscode2session接口 ===")
    
    # 模拟小程序code（实际使用时需要从小程序前端获取）
    test_code = "test_js_code_from_miniprogram"
    
    try:
        result = await wechat_api.jscode2session(test_code)
        print(f"✅ jscode2session调用成功:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except WeChatAPIError as e:
        print(f"❌ jscode2session调用失败: {e.message} (错误码: {e.error_code})")
        print("这是预期的，因为使用的是测试code")
        
    except Exception as e:
        print(f"❌ 意外错误: {str(e)}")


async def test_miniprogram_login():
    """测试小程序登录流程"""
    print("\n=== 测试微信小程序登录流程 ===")
    
    test_code = "test_js_code_from_miniprogram"
    
    try:
        result = await wechat_api.miniprogram_login(test_code)
        print(f"✅ 小程序登录成功:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except WeChatAPIError as e:
        print(f"❌ 小程序登录失败: {e.message} (错误码: {e.error_code})")
        print("这是预期的，因为使用的是测试code")
        
    except Exception as e:
        print(f"❌ 意外错误: {str(e)}")


async def test_api_endpoint():
    """测试API端点"""
    print("\n=== 测试小程序登录API端点 ===")
    
    # 测试数据
    login_data = {
        "code": "test_js_code_from_miniprogram"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/auth/wechat-miniprogram-login",
                json=login_data,
                timeout=10.0
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容:")
            
            try:
                result = response.json()
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except:
                print(response.text)
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")


def print_api_info():
    """打印API信息"""
    print("\n=== 微信小程序登录API信息 ===")
    print("新增接口:")
    print("  POST /api/v1/auth/wechat-miniprogram-login")
    print("  请求体: {\"code\": \"小程序wx.login()获得的code\"}")
    print()
    print("原有接口:")
    print("  POST /api/v1/auth/wechat-login (用于公众号OAuth2登录)")
    print()
    print("主要变化:")
    print("  1. 新增 jscode2session 接口支持")
    print("  2. 使用 https://api.weixin.qq.com/sns/jscode2session")
    print("  3. 参数: appid, secret, js_code, grant_type=authorization_code")
    print("  4. 返回: openid, session_key, unionid(可选)")
    print()
    print("错误处理:")
    print("  - 40029: code无效")
    print("  - 45011: API调用太频繁")
    print("  - 40013: 不合法的AppID")


async def main():
    """主函数"""
    print("微信小程序登录功能测试")
    print("=" * 50)
    
    # 打印API信息
    print_api_info()
    
    # 测试jscode2session接口
    await test_jscode2session()
    
    # 测试小程序登录流程
    await test_miniprogram_login()
    
    # 测试API端点
    await test_api_endpoint()
    
    print("\n=== 测试完成 ===")
    print("注意: 由于使用的是测试code，API调用会失败，这是正常的")
    print("在实际使用中，需要从小程序前端调用wx.login()获取真实的code")


if __name__ == "__main__":
    asyncio.run(main())
