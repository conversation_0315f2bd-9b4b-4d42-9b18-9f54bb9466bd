#!/usr/bin/env python3
"""
测试修复后的微信小程序登录
"""
import asyncio
import httpx
import json


async def test_miniprogram_login():
    """测试小程序登录接口"""
    print("=== 测试修复后的小程序登录接口 ===")
    
    # 测试数据
    login_data = {
        "code": "test_js_code_from_miniprogram"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/auth/wechat-login",
                json=login_data,
                timeout=10.0
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容:")
            
            try:
                result = response.json()
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 检查是否还有OAuth错误
                if "invalid oauth code" in str(result):
                    print("❌ 仍然存在OAuth错误，需要进一步检查")
                else:
                    print("✅ 没有OAuth错误，接口已正确使用jscode2session")
                    
            except:
                print(response.text)
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")


def print_changes():
    """打印修改内容"""
    print("\n=== 修改内容总结 ===")
    print("1. 修改了 /api/v1/auth/wechat-login 接口:")
    print("   - 现在只支持小程序登录（使用jscode2session）")
    print("   - 移除了OAuth2公众号登录支持")
    print("   - 使用 await wechat_api.miniprogram_login(code)")
    print()
    print("2. 修复了异步调用问题:")
    print("   - 添加了 await user_service.wechat_login()")
    print()
    print("3. 接口现在的流程:")
    print("   - 接收小程序code")
    print("   - 调用微信jscode2session API")
    print("   - 获取openid和session_key")
    print("   - 创建或更新用户")
    print("   - 返回JWT token")
    print()
    print("4. 错误处理:")
    print("   - 专门处理小程序登录错误")
    print("   - 提供清晰的错误信息")


async def main():
    """主函数"""
    print("微信小程序登录修复测试")
    print("=" * 50)
    
    # 打印修改内容
    print_changes()
    
    # 测试接口
    await test_miniprogram_login()
    
    print("\n=== 测试完成 ===")
    print("注意: 由于使用测试code，微信API会返回错误，但不应该再有OAuth相关错误")


if __name__ == "__main__":
    asyncio.run(main())
