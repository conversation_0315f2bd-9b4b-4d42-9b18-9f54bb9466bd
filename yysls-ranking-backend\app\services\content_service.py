"""
内容管理业务服务

处理内容管理相关的业务逻辑，包括：
- 内容发布和管理
- 播报消息管理
- 内容状态管理
- 内容统计
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from datetime import datetime

from app.services.base import BaseService
from app.models.content import Content, BroadcastMessage, ContentType, ContentStatus


class ContentService(BaseService):
    """内容管理服务类"""
    
    def __init__(self):
        super().__init__(Content)
    
    async def get_by_type(
        self, 
        db: AsyncSession, 
        content_type: ContentType,
        status: ContentStatus = ContentStatus.PUBLISHED,
        skip: int = 0,
        limit: int = 100
    ) -> List[Content]:
        """根据类型获取内容列表"""
        try:
            query = select(Content).where(
                and_(
                    Content.content_type == content_type,
                    Content.status == status
                )
            )
            
            # 按置顶、推荐、显示顺序、发布时间排序
            query = query.order_by(
                Content.is_top.desc(),
                Content.is_featured.desc(),
                Content.display_order,
                Content.publish_time.desc()
            ).offset(skip).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"根据类型获取内容失败 type={content_type}: {str(e)}")
            raise
    
    async def create_content(
        self, 
        db: AsyncSession, 
        content_data: Dict[str, Any], 
        created_by: int
    ) -> Content:
        """创建内容"""
        try:
            content_data['created_by'] = created_by
            
            # 如果是发布状态且没有设置发布时间，设置为当前时间
            if content_data.get('status') == ContentStatus.PUBLISHED and not content_data.get('publish_time'):
                content_data['publish_time'] = datetime.utcnow()
            
            db_content = Content(**content_data)
            db.add(db_content)
            await db.commit()
            await db.refresh(db_content)
            
            self.logger.info(f"创建内容成功 ID={db_content.id}, title={db_content.title}")
            return db_content
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建内容失败: {str(e)}")
            raise
    
    async def publish_content(
        self, 
        db: AsyncSession, 
        content_id: int, 
        updated_by: int
    ) -> Content:
        """发布内容"""
        try:
            content = await self.get(db, content_id)
            if not content:
                raise ValueError(f"内容不存在 ID={content_id}")
            
            content.status = ContentStatus.PUBLISHED
            content.publish_time = datetime.utcnow()
            content.updated_by = updated_by
            
            await db.commit()
            await db.refresh(content)
            
            self.logger.info(f"发布内容成功 ID={content_id}")
            return content
        except Exception as e:
            await db.rollback()
            self.logger.error(f"发布内容失败 ID={content_id}: {str(e)}")
            raise
    
    async def get_about_content(self, db: AsyncSession) -> Optional[Content]:
        """获取关于我们内容"""
        try:
            result = await db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ABOUT,
                        Content.status == ContentStatus.PUBLISHED
                    )
                ).order_by(Content.updated_at.desc())
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"获取关于我们内容失败: {str(e)}")
            raise
    
    async def increment_view_count(self, db: AsyncSession, content_id: int) -> bool:
        """增加浏览次数"""
        try:
            content = await self.get(db, content_id)
            if not content:
                return False
            
            content.view_count += 1
            await db.commit()
            
            return True
        except Exception as e:
            await db.rollback()
            self.logger.error(f"增加浏览次数失败 ID={content_id}: {str(e)}")
            return False

    async def search_contents(
        self,
        db: AsyncSession,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        **filters
    ) -> tuple[List[Content], int]:
        """搜索内容（返回结果和总数）"""
        try:
            # 构建搜索条件
            search_conditions = or_(
                Content.title.ilike(f"%{search_term}%"),
                Content.content.ilike(f"%{search_term}%"),
                Content.summary.ilike(f"%{search_term}%")
            )

            # 构建过滤条件
            filter_conditions = self._build_filters(**filters)

            # 组合所有条件
            all_conditions = [search_conditions]
            if filter_conditions:
                all_conditions.extend(filter_conditions)

            # 构建查询
            query = select(Content).where(and_(*all_conditions))
            count_query = select(func.count(Content.id)).where(and_(*all_conditions))

            # 获取总数
            total_result = await db.execute(count_query)
            total = total_result.scalar()

            # 获取数据
            query = query.order_by(
                Content.is_top.desc(),
                Content.is_featured.desc(),
                Content.publish_time.desc()
            ).offset(skip).limit(limit)
            result = await db.execute(query)
            contents = result.scalars().all()

            return contents, total
        except Exception as e:
            self.logger.error(f"搜索内容失败 search_term={search_term}: {str(e)}")
            raise

    async def get_published_announcements(self, db: AsyncSession, limit: int = 10) -> List[Content]:
        """获取已发布的公告列表"""
        try:
            result = await db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ANNOUNCEMENT,
                        Content.status == ContentStatus.PUBLISHED
                    )
                ).order_by(
                    Content.is_top.desc(),
                    Content.publish_time.desc()
                ).limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取公告列表失败: {str(e)}")
            raise

    async def get_broadcast_messages(
        self,
        db: AsyncSession,
        limit: int = 10,
        **filters
    ) -> List[BroadcastMessage]:
        """获取播报消息列表"""
        try:
            query = select(BroadcastMessage)

            # 应用过滤条件
            filter_conditions = []
            for key, value in filters.items():
                if hasattr(BroadcastMessage, key) and value is not None:
                    filter_conditions.append(getattr(BroadcastMessage, key) == value)

            if filter_conditions:
                query = query.where(and_(*filter_conditions))

            query = query.order_by(BroadcastMessage.display_order).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取播报消息列表失败: {str(e)}")
            raise

    async def create_broadcast_message(
        self,
        db: AsyncSession,
        obj_in: Dict[str, Any]
    ) -> BroadcastMessage:
        """创建播报消息"""
        try:
            db_message = BroadcastMessage(**obj_in)
            db.add(db_message)
            await db.commit()
            await db.refresh(db_message)

            self.logger.info(f"创建播报消息成功 ID={db_message.id}")
            return db_message
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建播报消息失败: {str(e)}")
            raise


class BroadcastService(BaseService):
    """播报消息服务类"""
    
    def __init__(self):
        super().__init__(BroadcastMessage)
    
    async def get_active_messages(self, db: AsyncSession) -> List[BroadcastMessage]:
        """获取当前活跃的播报消息"""
        try:
            now = datetime.utcnow()
            result = await db.execute(
                select(BroadcastMessage).where(
                    and_(
                        BroadcastMessage.is_active == True,
                        or_(
                            BroadcastMessage.start_time.is_(None),
                            BroadcastMessage.start_time <= now
                        ),
                        or_(
                            BroadcastMessage.end_time.is_(None),
                            BroadcastMessage.end_time >= now
                        )
                    )
                ).order_by(BroadcastMessage.display_order)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取活跃播报消息失败: {str(e)}")
            raise
    
    async def create_broadcast(
        self, 
        db: AsyncSession, 
        message_data: Dict[str, Any], 
        created_by: int
    ) -> BroadcastMessage:
        """创建播报消息"""
        try:
            message_data['created_by'] = created_by
            
            db_message = BroadcastMessage(**message_data)
            db.add(db_message)
            await db.commit()
            await db.refresh(db_message)
            
            self.logger.info(f"创建播报消息成功 ID={db_message.id}")
            return db_message
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建播报消息失败: {str(e)}")
            raise


# 创建全局服务实例
content_service = ContentService()
broadcast_service = BroadcastService()
