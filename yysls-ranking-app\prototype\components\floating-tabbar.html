<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮式底部导航栏组件</title>
    <style>
        /* 重置body样式，确保在iframe中正常显示 */
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            overflow: hidden;
        }

        /* 悬浮式 Tabbar 样式 */
        .floating-tabbar-container {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            z-index: 9999;
            transform: translateY(200px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .floating-tabbar-container.show {
            transform: translateY(0);
            opacity: 1;
        }

        .floating-tabbar {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            border-radius: 30px;
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(74, 74, 74, 0.85) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 4px 16px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(212, 175, 55, 0.1);
            position: relative;
            overflow: hidden;
            animation: float-glow 4s ease-in-out infinite;
            max-width: 280px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 背景装饰效果 */
        .floating-tabbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                135deg, 
                rgba(212, 175, 55, 0.05) 0%, 
                transparent 50%, 
                rgba(212, 175, 55, 0.03) 100%
            );
            pointer-events: none;
        }

        /* Tab项目样式 */
        .floating-tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            height: 100%;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            user-select: none;
            margin: 0 15px;
        }

        /* 分割线样式 */
        .tab-divider {
            width: 2px;
            height: 35px;
            background: linear-gradient(
                to bottom,
                transparent 0%,
                rgba(212, 175, 55, 0.2) 15%,
                rgba(212, 175, 55, 0.5) 30%,
                rgba(212, 175, 55, 0.8) 50%,
                rgba(212, 175, 55, 0.5) 70%,
                rgba(212, 175, 55, 0.2) 85%,
                transparent 100%
            );
            position: relative;
            flex-shrink: 0;
            border-radius: 1px;
            box-shadow:
                0 0 8px rgba(212, 175, 55, 0.3),
                inset 0 0 2px rgba(212, 175, 55, 0.4);
        }

        .tab-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, rgba(212, 175, 55, 1) 0%, rgba(212, 175, 55, 0.6) 70%, transparent 100%);
            border-radius: 50%;
            box-shadow:
                0 0 8px rgba(212, 175, 55, 0.6),
                0 0 4px rgba(212, 175, 55, 0.8);
        }

        .tab-divider::after {
            content: '';
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            width: 1px;
            height: 60%;
            background: linear-gradient(
                to bottom,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 100%
            );
        }

        .floating-tab-item:active {
            transform: scale(0.95);
        }

        /* 图标容器 */
        .floating-tab-icon-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            margin-bottom: 4px;
        }

        /* 选中状态指示器 */
        .floating-tab-indicator {
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            transform: scale(0);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .floating-tab-item.active .floating-tab-indicator {
            transform: scale(1);
            box-shadow: 
                0 0 10px rgba(212, 175, 55, 0.4),
                0 0 20px rgba(212, 175, 55, 0.2);
        }

        /* 图标样式 */
        .floating-tab-icon {
            width: 24px;
            height: 24px;
            filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform: scale(1);
        }

        .floating-tab-item.active .floating-tab-icon {
            filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);
            transform: scale(1.1);
            drop-shadow: 0 0 4px rgba(212, 175, 55, 0.6);
        }

        /* 徽章样式 */
        .floating-tab-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            min-width: 16px;
            height: 16px;
            border-radius: 8px;
            background: linear-gradient(135deg, #FF4444 0%, #FF6666 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            box-shadow: 0 2px 6px rgba(255, 68, 68, 0.4);
        }

        .floating-tab-badge-text {
            color: #FFFFFF;
            font-size: 10px;
            font-weight: 600;
            line-height: 1;
        }

        /* 文字样式 */
        .floating-tab-text {
            font-size: 10px;
            font-weight: 500;
            color: #9CA3AF;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            text-shadow: none;
        }

        .floating-tab-item.active .floating-tab-text {
            color: #D4AF37;
            font-weight: 600;
            text-shadow: 0 0 4px rgba(212, 175, 55, 0.4);
            transform: scale(1.05);
        }

        /* 悬浮动画 */
        @keyframes float-glow {
            0%, 100% {
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.4),
                    0 4px 16px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(212, 175, 55, 0.1);
            }
            50% {
                box-shadow: 
                    0 10px 40px rgba(0, 0, 0, 0.5),
                    0 6px 20px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(212, 175, 55, 0.15),
                    0 0 20px rgba(212, 175, 55, 0.1);
            }
        }

        /* 点击波纹效果 */
        .floating-tab-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(212, 175, 55, 0.2);
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .floating-tab-item.ripple::before {
            width: 60px;
            height: 60px;
            opacity: 0;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .floating-tabbar-container {
                left: 10px;
                right: 10px;
            }
            
            .floating-tab-text {
                font-size: 9px;
            }
            
            .floating-tab-icon {
                width: 20px;
                height: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 悬浮式底部导航栏 -->
    <div class="floating-tabbar-container" id="floatingTabbar">
        <div class="floating-tabbar">
            <div class="floating-tab-item" data-tab="home">
                <div class="floating-tab-icon-container">
                    <div class="floating-tab-indicator"></div>
                    <img src="https://unpkg.com/lucide-static@latest/icons/trophy.svg" alt="榜单" class="floating-tab-icon">
                </div>
                <span class="floating-tab-text">榜单</span>
            </div>

            <!-- 分割线 -->
            <div class="tab-divider"></div>

            <div class="floating-tab-item" data-tab="user">
                <div class="floating-tab-icon-container">
                    <div class="floating-tab-indicator"></div>
                    <img src="https://unpkg.com/lucide-static@latest/icons/user.svg" alt="我的" class="floating-tab-icon">
                </div>
                <span class="floating-tab-text">我的</span>
            </div>
        </div>
    </div>

    <script>
        // 悬浮式 Tabbar 控制器
        class FloatingTabbar {
            constructor(containerId) {
                this.container = document.getElementById(containerId);
                this.tabItems = this.container.querySelectorAll('.floating-tab-item');
                this.currentIndex = -1; // 初始化为-1，表示没有活动标签

                this.init();
            }
            
            init() {
                // 显示 tabbar
                setTimeout(() => {
                    this.container.classList.add('show');
                }, 100);
                
                // 绑定点击事件
                this.tabItems.forEach((item, index) => {
                    item.addEventListener('click', (e) => {
                        this.handleTabClick(index, e);
                    });
                });
            }
            
            handleTabClick(index, event) {
                // 添加波纹效果
                this.addRippleEffect(event.currentTarget);

                // 更新选中状态
                this.setActiveTab(index);

                // 触发自定义事件
                const tabData = {
                    index: index,
                    tab: this.tabItems[index].dataset.tab
                };

                this.container.dispatchEvent(new CustomEvent('tabchange', {
                    detail: tabData
                }));

                // 向父页面发送消息（如果在iframe中）
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'tabchange',
                        ...tabData
                    }, '*');
                }

                console.log('Tab切换:', tabData);
            }
            
            setActiveTab(index) {
                // 移除所有活动状态
                this.tabItems.forEach(item => {
                    item.classList.remove('active');
                });

                // 设置新的活动状态
                this.tabItems[index].classList.add('active');
                this.currentIndex = index;
            }

            // 根据标签名设置活动状态
            setActiveTabByName(tabName) {
                this.tabItems.forEach((item, index) => {
                    if (item.dataset.tab === tabName) {
                        this.setActiveTab(index);
                    }
                });
            }
            
            addRippleEffect(element) {
                element.classList.add('ripple');
                setTimeout(() => {
                    element.classList.remove('ripple');
                }, 300);
            }
            
            // 显示/隐藏 tabbar
            show() {
                this.container.classList.add('show');
            }
            
            hide() {
                this.container.classList.remove('show');
            }
            
            // 设置徽章
            setBadge(tabIndex, count) {
                const tabItem = this.tabItems[tabIndex];
                const iconContainer = tabItem.querySelector('.floating-tab-icon-container');
                let badge = iconContainer.querySelector('.floating-tab-badge');
                
                if (count > 0) {
                    if (!badge) {
                        badge = document.createElement('div');
                        badge.className = 'floating-tab-badge';
                        badge.innerHTML = '<span class="floating-tab-badge-text"></span>';
                        iconContainer.appendChild(badge);
                    }
                    badge.querySelector('.floating-tab-badge-text').textContent = count > 99 ? '99+' : count;
                } else if (badge) {
                    badge.remove();
                }
            }
        }
        
        // 初始化悬浮式 Tabbar
        const floatingTabbar = new FloatingTabbar('floatingTabbar');

        // 监听 tab 切换事件
        document.getElementById('floatingTabbar').addEventListener('tabchange', (e) => {
            console.log('Tab changed to:', e.detail);
            // 这里可以添加页面切换逻辑
        });

        // 监听来自父页面的消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'setActiveTab') {
                console.log('收到设置活动标签消息:', event.data.tab);
                floatingTabbar.setActiveTabByName(event.data.tab);
            }
        });
        
        // 示例：动态设置徽章
        setTimeout(() => {
            floatingTabbar.setBadge(2, 5); // 给排行tab设置徽章数量为5
        }, 2000);
        
        // 示例：隐藏和显示 tabbar
        // setTimeout(() => {
        //     floatingTabbar.hide();
        // }, 5000);
        
        // setTimeout(() => {
        //     floatingTabbar.show();
        // }, 7000);
    </script>
</body>
</html>
