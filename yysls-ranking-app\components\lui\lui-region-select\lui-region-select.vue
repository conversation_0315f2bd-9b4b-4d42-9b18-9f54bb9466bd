<template>
	<fui-bottom-popup :show="show" @close="handleClose">
		<view class="fui-popup">
			<view class="popup-title">
				<view class="title">{{title}}</view>
				<view class="close" @tap="handleClose">
					<fui-icon name="close" :size="48"></fui-icon>
				</view>
			</view>
			<fui-cascader stepLoading ref="cascaderRef" :options="options" @change="change" @complete="complete">
			</fui-cascader>
		</view>
	</fui-bottom-popup>
</template>

<script setup>
	import {
		ref,
		onMounted,
		watch
	} from "vue";
	import {
		getRegion as getRegionApi
	} from "@/apis/common.js";

	const props = defineProps({
		title: {
			type: String,
			default: "请选择区域"
		},
		value: {
			type: Boolean,
			default: () => false
		}
	});

	const emits = defineEmits(['update:value', 'complete'])

	const show = ref(props.value);
	const options = ref([]);
	const cascaderRef = ref();

	watch(() => props.value, (val) => {
		show.value = val;
	})

	onMounted(async () => {
		options.value = await getData()
	});

	const getData = (pid = 0) => {
		return getRegionApi({
			pid
		}, {
			showLoading: false
		}).then(res => {
			return res.map(v => {
				return {
					id: v.id,
					text: v.name,
					value: v.code
				}
			});
		})
	}

	const handleClose = () => {
		emits('update:value', false)
	}

	const change = async (e) => {
		if (e.layer == 2) {
			cascaderRef.value.end(e.layer);
			handleClose();
			return
		}
		const data = await getData(e.id)
		cascaderRef.value.setRequestData(data, e.layer)
	}

	const complete = (e) => {
		emits('complete', {
			value: e.value,
			text: e.text.join(' ')
		})

	}
</script>

<style>
</style>