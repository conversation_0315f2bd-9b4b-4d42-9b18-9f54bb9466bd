"""
赞助商模型
"""
from datetime import datetime, timezone

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String

from app.core.database import Base


class Sponsor(Base):
    """赞助商表"""
    __tablename__ = "sponsors"

    id = Column(Integer, primary_key=True, index=True)

    # 基础信息
    name = Column(String(200), nullable=False, comment="赞助商名称")
    logo_url = Column(String(500), nullable=True, comment="Logo URL（用作头像）")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序顺序")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False, comment="更新时间")

    def __repr__(self):
        return f"<Sponsor(id={self.id}, name='{self.name}')>"
