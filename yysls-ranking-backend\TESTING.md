# 燕友圈榜单系统 - 测试指南

## 🧪 测试体系概述

本项目采用pytest作为测试框架，建立了完整的测试体系，包括单元测试、集成测试和API测试。

## 📁 测试结构

```
tests/
├── __init__.py                 # 测试包初始化
├── conftest.py                # pytest配置和fixture
├── test_services/             # 服务层单元测试
│   ├── test_user_service.py   # 用户服务测试
│   └── test_ranking_service.py # 榜单服务测试
└── test_api/                  # API集成测试
    └── test_auth.py           # 认证API测试
```

## 🛠️ 测试配置

### pytest配置 (pytest.ini)
- 测试发现和执行配置
- 覆盖率报告配置
- 测试标记定义
- 异步测试支持

### 测试环境配置 (.env.test)
- SQLite测试数据库
- 测试用的JWT密钥
- 模拟的微信和邮件配置

### 测试fixture (conftest.py)
- 测试数据库设置
- 异步客户端配置
- 测试用户和数据创建
- 认证头生成

## 🏃‍♂️ 运行测试

### 使用测试运行器
```bash
# 检查测试环境
python run_tests.py check

# 运行所有测试
python run_tests.py all

# 运行单元测试
python run_tests.py unit

# 运行集成测试
python run_tests.py integration

# 运行认证相关测试
python run_tests.py auth

# 运行测试并生成覆盖率报告
python run_tests.py coverage

# 运行快速测试（排除慢速测试）
python run_tests.py fast

# 运行指定测试文件
python run_tests.py --file tests/test_services/test_user_service.py

# 运行匹配的测试函数
python run_tests.py --function test_login
```

### 直接使用pytest
```bash
# 运行所有测试
pytest

# 运行指定目录的测试
pytest tests/test_services/

# 运行指定文件的测试
pytest tests/test_services/test_user_service.py

# 运行指定测试函数
pytest tests/test_services/test_user_service.py::TestUserService::test_create_user

# 运行带标记的测试
pytest -m unit          # 单元测试
pytest -m integration   # 集成测试
pytest -m auth          # 认证测试
pytest -m wechat        # 微信相关测试

# 生成覆盖率报告
pytest --cov=app --cov-report=html --cov-report=term-missing

# 详细输出
pytest -v --tb=short
```

## 📊 测试标记

项目定义了以下测试标记：

- `unit`: 单元测试
- `integration`: 集成测试
- `api`: API测试
- `auth`: 认证相关测试
- `database`: 数据库相关测试
- `slow`: 慢速测试
- `wechat`: 微信相关测试
- `email`: 邮件相关测试

## 🧪 测试类型

### 1. 单元测试 (Unit Tests)
测试单个服务类的功能，位于 `tests/test_services/`

**已实现的测试**:
- ✅ `test_user_service.py` - 用户服务测试
- ✅ `test_ranking_service.py` - 榜单服务测试

**待实现的测试**:
- ⏳ `test_sponsor_service.py` - 赞助商服务测试
- ⏳ `test_system_config_service.py` - 系统配置服务测试
- ⏳ `test_content_service.py` - 内容服务测试

### 2. 集成测试 (Integration Tests)
测试API端点的完整流程，位于 `tests/test_api/`

**已实现的测试**:
- ✅ `test_auth.py` - 认证API测试

**待实现的测试**:
- ⏳ `test_rankings.py` - 榜单API测试
- ⏳ `test_users.py` - 用户API测试
- ⏳ `test_sponsors.py` - 赞助商API测试
- ⏳ `test_system_config.py` - 系统配置API测试
- ⏳ `test_content.py` - 内容API测试

### 3. 数据库测试
测试数据模型和数据库操作

### 4. 认证测试
测试JWT认证、权限控制、微信登录等

## 📈 测试覆盖率

### 当前状态
- **目标覆盖率**: 80%+
- **当前覆盖率**: 待测量
- **覆盖率报告**: `htmlcov/index.html`

### 覆盖率要求
- 服务层代码覆盖率 > 90%
- API层代码覆盖率 > 85%
- 工具函数覆盖率 > 80%
- 总体覆盖率 > 80%

## 🔧 测试最佳实践

### 1. 测试命名
- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试函数: `test_*`
- 描述性命名: `test_create_user_success`

### 2. 测试结构
```python
@pytest.mark.unit
class TestUserService:
    @pytest.mark.asyncio
    async def test_create_user(self, db_session, user_service, test_data_factory):
        # Arrange - 准备测试数据
        user_data = test_data_factory.user_data()
        
        # Act - 执行被测试的操作
        user = await user_service.create(db_session, obj_in=user_data)
        
        # Assert - 验证结果
        assert user.id is not None
        assert user.username == user_data["username"]
```

### 3. 使用fixture
- 使用 `conftest.py` 中定义的fixture
- 创建可重用的测试数据
- 确保测试隔离

### 4. 异步测试
```python
@pytest.mark.asyncio
async def test_async_function(self, async_client):
    response = await async_client.get("/api/endpoint")
    assert response.status_code == 200
```

### 5. Mock和Patch
```python
@patch('app.utils.wechat.wechat_api.login_with_code')
async def test_wechat_login(self, mock_wechat, async_client):
    mock_wechat.return_value = {"openid": "test_openid"}
    # 测试代码
```

## 🚨 故障排除

### 常见问题

1. **数据库连接错误**
   - 检查 `.env.test` 配置
   - 确保测试数据库可访问

2. **导入错误**
   - 检查 `PYTHONPATH` 设置
   - 确保在项目根目录运行测试

3. **异步测试失败**
   - 确保使用 `@pytest.mark.asyncio`
   - 检查 `pytest-asyncio` 是否安装

4. **Fixture未找到**
   - 检查 `conftest.py` 是否正确配置
   - 确保fixture名称正确

### 调试技巧

1. **使用 `-v` 参数查看详细输出**
2. **使用 `--tb=short` 简化错误信息**
3. **使用 `-s` 参数查看print输出**
4. **使用 `--pdb` 进入调试器**

## 📝 测试报告

### 生成报告
```bash
# HTML覆盖率报告
pytest --cov=app --cov-report=html

# 终端覆盖率报告
pytest --cov=app --cov-report=term-missing

# JUnit XML报告
pytest --junitxml=test-results.xml
```

### 查看报告
- HTML报告: 打开 `htmlcov/index.html`
- 终端报告: 直接在命令行查看
- CI/CD集成: 使用XML报告

## 🎯 下一步计划

### 短期目标 (1-2周)
1. ✅ 完成用户服务单元测试
2. ✅ 完成榜单服务单元测试
3. ✅ 完成认证API集成测试
4. ⏳ 完成其他服务层单元测试
5. ⏳ 完成主要API集成测试

### 中期目标 (2-4周)
1. ⏳ 达到80%以上测试覆盖率
2. ⏳ 完成所有API端点测试
3. ⏳ 添加性能测试
4. ⏳ 集成CI/CD测试流水线

### 长期目标 (1-2月)
1. ⏳ 端到端测试
2. ⏳ 压力测试
3. ⏳ 安全测试
4. ⏳ 自动化测试报告

---

**测试状态**: 🟡 进行中 | **覆盖率**: 🟡 待提升 | **质量**: 🟢 良好
