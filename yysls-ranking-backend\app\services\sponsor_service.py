"""
赞助商业务服务

处理赞助商相关的业务逻辑，包括：
- 赞助商信息管理
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import select, or_, and_

from app.services.base import BaseService
from app.models.sponsor import Sponsor


class SponsorService(BaseService):
    """赞助商服务类"""

    def __init__(self):
        super().__init__(Sponsor)

    def get_all(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> List[Sponsor]:
        """获取所有赞助商列表（按排序顺序排序）"""
        try:
            query = select(Sponsor)

            # 添加激活状态筛选
            if is_active is not None:
                query = query.where(Sponsor.is_active == is_active)

            result = db.execute(
                query.order_by(Sponsor.sort_order.asc(), Sponsor.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
        except Exception as e:
            self.logger.error(f"获取赞助商列表失败: {str(e)}")
            raise

    def get_active_sponsors(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100
    ) -> List[Sponsor]:
        """获取活跃的赞助商列表（按排序顺序排序）"""
        return self.get_all(db, skip=skip, limit=limit, is_active=True)

    def search_sponsors(
        self,
        db: Session,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> tuple[List[Sponsor], int]:
        """搜索赞助商（返回结果和总数）"""
        try:
            # 构建搜索条件
            search_conditions = or_(
                Sponsor.name.ilike(f"%{search_term}%")
            )

            # 构建完整的查询条件
            conditions = [search_conditions]
            if is_active is not None:
                conditions.append(Sponsor.is_active == is_active)

            # 组合所有条件
            where_clause = and_(*conditions) if len(conditions) > 1 else conditions[0]

            # 构建查询
            query = select(Sponsor).where(where_clause)
            count_query = select(Sponsor.id).where(where_clause)

            # 获取总数
            total_result = db.execute(count_query)
            total = len(list(total_result.scalars().all()))

            # 获取分页结果
            result = db.execute(
                query.order_by(Sponsor.sort_order.asc(), Sponsor.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            sponsors = list(result.scalars().all())

            return sponsors, total
        except Exception as e:
            self.logger.error(f"搜索赞助商失败 search_term={search_term}: {str(e)}")
            raise

    def update_sort_order(
        self,
        db: Session,
        sponsor_id: int,
        new_order: int
    ) -> Sponsor:
        """更新赞助商排序顺序"""
        try:
            sponsor = self.get(db, sponsor_id)
            if not sponsor:
                raise ValueError(f"赞助商不存在 ID={sponsor_id}")

            old_order = sponsor.sort_order
            sponsor.sort_order = new_order

            db.commit()
            db.refresh(sponsor)

            self.logger.info(f"更新赞助商排序顺序成功 ID={sponsor_id}, {old_order}->{new_order}")
            return sponsor
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新赞助商排序顺序失败 ID={sponsor_id}: {str(e)}")
            raise

    def toggle_active_status(
        self,
        db: Session,
        sponsor_id: int
    ) -> Sponsor:
        """切换赞助商激活状态"""
        try:
            sponsor = self.get(db, sponsor_id)
            if not sponsor:
                raise ValueError(f"赞助商不存在 ID={sponsor_id}")

            old_status = sponsor.is_active
            sponsor.is_active = not sponsor.is_active

            db.commit()
            db.refresh(sponsor)

            self.logger.info(f"切换赞助商激活状态成功 ID={sponsor_id}, {old_status}->{sponsor.is_active}")
            return sponsor
        except Exception as e:
            db.rollback()
            self.logger.error(f"切换赞助商激活状态失败 ID={sponsor_id}: {str(e)}")
            raise


# 创建全局赞助商服务实例
sponsor_service = SponsorService()
