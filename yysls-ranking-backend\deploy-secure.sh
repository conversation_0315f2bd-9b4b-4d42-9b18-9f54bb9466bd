#!/bin/bash

# 燕友圈榜单系统 - 安全生产环境部署脚本

set -e

echo "🔐 燕友圈榜单系统 - 安全生产环境部署"
echo "======================================="

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  不建议使用root用户运行此脚本"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 1. 环境检查
echo "1. 环境安全检查..."

# 检查环境配置文件
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod 文件不存在"
    echo "请先创建生产环境配置文件"
    exit 1
fi

# 检查环境配置文件权限
env_perms=$(stat -c "%a" .env.prod)
if [ "$env_perms" != "600" ]; then
    echo "⚠️  .env.prod 文件权限不安全，正在修复..."
    chmod 600 .env.prod
fi

echo "✅ 环境配置检查通过"

# 2. SSL证书管理
echo ""
echo "2. SSL证书安全检查..."

ssl_method=""
echo "选择SSL证书管理方式："
echo "   1) 使用本地证书文件（开发/测试）"
echo "   2) 使用外部卷管理（生产推荐）"
echo "   3) 跳过SSL配置（仅HTTP）"
read -p "请选择 (1-3): " ssl_choice

case $ssl_choice in
    1)
        ssl_method="local"
        if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
            echo "❌ SSL证书文件不存在"
            echo "请运行: ./scripts/generate-ssl.sh"
            exit 1
        fi
        
        # 检查证书权限
        chmod 600 ssl/key.pem 2>/dev/null || true
        chmod 644 ssl/cert.pem 2>/dev/null || true
        echo "✅ 使用本地SSL证书"
        ;;
    2)
        ssl_method="external"
        echo "✅ 将使用外部卷管理SSL证书"
        echo "请确保已创建外部卷: docker volume create yysls_ssl_certs"
        ;;
    3)
        ssl_method="none"
        echo "⚠️  跳过SSL配置，仅使用HTTP"
        ;;
esac

# 3. 创建外部卷（如果需要）
if [ "$ssl_method" = "external" ]; then
    echo ""
    echo "3. 创建外部卷..."
    
    # 检查卷是否存在
    if ! docker volume ls | grep -q yysls_ssl_certs; then
        docker volume create yysls_ssl_certs
        echo "✅ 已创建SSL证书外部卷"
        
        echo "请将SSL证书复制到外部卷："
        echo "   docker run --rm -v yysls_ssl_certs:/ssl -v \$(pwd)/ssl:/host alpine cp /host/cert.pem /ssl/"
        echo "   docker run --rm -v yysls_ssl_certs:/ssl -v \$(pwd)/ssl:/host alpine cp /host/key.pem /ssl/"
        
        read -p "证书已复制完成？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "请先复制证书文件"
            exit 1
        fi
    else
        echo "✅ SSL证书外部卷已存在"
    fi
fi

# 4. 选择部署配置
echo ""
echo "4. 选择部署配置..."

compose_file="docker-compose.prod.yml"
if [ "$ssl_method" = "external" ]; then
    compose_file="docker-compose.prod-secure.yml"
    echo "✅ 使用安全配置文件: $compose_file"
else
    echo "✅ 使用标准配置文件: $compose_file"
fi

# 5. 停止现有服务
echo ""
echo "5. 停止现有服务..."
docker-compose -f $compose_file down 2>/dev/null || true

# 6. 备份数据（如果存在）
echo ""
echo "6. 数据备份..."
if docker volume ls | grep -q yysls-ranking-backend_mysql_data; then
    backup_file="backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    echo "正在备份数据库数据到: $backup_file"
    docker run --rm -v yysls-ranking-backend_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/$backup_file -C /data .
    echo "✅ 数据备份完成"
fi

# 7. 构建和启动服务
echo ""
echo "7. 构建和启动服务..."
docker-compose -f $compose_file up --build -d

# 8. 等待服务启动
echo ""
echo "8. 等待服务启动..."
sleep 15

# 9. 健康检查
echo ""
echo "9. 服务健康检查..."
docker-compose -f $compose_file ps

# 检查应用健康状态
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过"
else
    echo "⚠️  应用健康检查失败，查看日志："
    docker-compose -f $compose_file logs app --tail=10
fi

# 10. 安全配置建议
echo ""
echo "🎉 部署完成！"
echo ""
echo "🔒 安全配置建议："
echo "   1. 定期更新SSL证书"
echo "   2. 监控系统日志"
echo "   3. 定期备份数据"
echo "   4. 使用防火墙限制访问"
echo "   5. 启用日志轮转"
echo ""
echo "📋 服务信息："
if [ "$ssl_method" != "none" ]; then
    echo "   - HTTPS地址: https://your-domain.com"
fi
echo "   - HTTP地址: http://your-server-ip"
echo "   - API文档: http://your-server-ip/docs"
echo ""
echo "🔧 管理命令："
echo "   - 查看日志: docker-compose -f $compose_file logs -f"
echo "   - 重启服务: docker-compose -f $compose_file restart"
echo "   - 停止服务: docker-compose -f $compose_file down"
echo "   - 更新证书: ./scripts/nginx-config.sh"
