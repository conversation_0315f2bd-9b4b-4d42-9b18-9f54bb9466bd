<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 合作伙伴管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 60px 20px 140px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 表单标签样式 */
        .form-label {
            color: #D4AF37;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }
        
        /* 表单输入框样式 */
        .form-input {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        .form-input::placeholder {
            color: rgba(245, 245, 220, 0.5);
        }
        
        /* 图片上传区域样式 */
        .upload-area {
            border: 2px dashed rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            padding: 30px 20px;
            text-align: center;
            background: rgba(74, 74, 74, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .upload-area:hover {
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.2);
        }
        
        .upload-area.dragover {
            border-color: rgba(212, 175, 55, 0.8);
            background: rgba(212, 175, 55, 0.1);
        }
        
        .upload-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 12px;
            color: #D4AF37;
            stroke: currentColor;
        }
        
        .upload-text {
            color: #D4AF37;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 6px;
        }
        
        .upload-hint {
            color: rgba(245, 245, 220, 0.6);
            font-size: 12px;
        }
        
        /* 图片预览 */
        .image-preview {
            display: none;
            max-width: 200px;
            max-height: 120px;
            border-radius: 8px;
            margin: 12px auto 0;
            border: 2px solid rgba(212, 175, 55, 0.3);
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(74, 74, 74, 0.5);
            border: 1px solid rgba(245, 245, 220, 0.3);
            color: #F5F5DC;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 12px;
        }
        
        .btn-secondary:hover {
            background: rgba(74, 74, 74, 0.7);
            border-color: rgba(245, 245, 220, 0.5);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            border: 1px solid rgba(239, 68, 68, 0.5);
            color: #FFFFFF;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 12px;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #F87171 0%, #EF4444 100%);
            transform: translateY(-1px);
        }
        
        /* 合作伙伴列表 */
        .partner-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .partner-item {
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .partner-item:hover {
            border-color: rgba(212, 175, 55, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }
        
        .partner-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 12px;
            border: 1px solid rgba(212, 175, 55, 0.2);
        }
        
        .partner-name {
            color: #F5F5DC;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            text-align: center;
        }

        .partner-order {
            color: #D4AF37;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 12px;
        }
        
        .partner-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(245, 245, 220, 0.6);
        }
        
        .empty-state img {
            width: 80px;
            height: 80px;
            opacity: 0.3;
            margin: 0 auto 16px;
            filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 响应式设计 */
        @media (max-width: 640px) {
            .main-content {
                padding: 60px 16px 140px 16px;
            }
            
            .partner-list {
                grid-template-columns: 1fr;
            }
            
            .upload-area {
                padding: 20px 16px;
            }
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 添加新合作伙伴表单 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="text-center mb-6">
                <img src="https://unpkg.com/lucide-static@latest/icons/handshake.svg" alt="合作伙伴管理"
                     class="w-12 h-12 mx-auto mb-4" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h1 class="text-2xl font-bold gold-text mb-2">合作伙伴管理</h1>
                <p class="text-gray-400 text-sm">管理江湖合作伙伴信息</p>
            </div>
            
            <form id="partnerForm" class="space-y-4">
                <!-- 图片上传 -->
                <div>
                    <label class="form-label">合作伙伴图片 *</label>
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('imageInput').click()">
                        <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <div class="upload-text">点击上传或拖拽图片到此处</div>
                        <div class="upload-hint">支持 JPG、PNG、WebP 格式，文件大小不超过 5MB</div>
                        <input type="file" id="imageInput" accept=".jpg,.jpeg,.png,.webp" style="display: none;" onchange="handleImageSelect(event)">
                        <img id="imagePreview" class="image-preview" alt="图片预览">
                    </div>
                </div>
                
                <!-- 合作伙伴名称和序号 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="form-label" for="partnerName">合作伙伴名称 *</label>
                        <input type="text" id="partnerName" name="partnerName" class="form-input"
                               placeholder="请输入合作伙伴名称" required>
                    </div>
                    <div>
                        <label class="form-label" for="partnerOrder">显示序号 *</label>
                        <input type="number" id="partnerOrder" name="partnerOrder" class="form-input"
                               placeholder="序号" min="1" max="999" required>
                    </div>
                </div>
                
                <!-- 添加按钮 -->
                <div class="pt-2">
                    <button type="submit" class="btn-primary w-full" id="addBtn">
                        <span id="addText">添加合作伙伴</span>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 合作伙伴列表 -->
        <div class="ink-card p-6 scroll-animate">
            <h2 class="text-xl font-semibold gold-text mb-4">已添加的合作伙伴</h2>
            <div id="partnerList" class="partner-list">
                <!-- 合作伙伴列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 合作伙伴管理页面控制器
        class PartnerManagement {
            constructor() {
                this.partners = [];
                this.selectedImage = null;
                this.isAdding = false;
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();

                // 初始化标签栏
                this.initTabbar();

                // 初始化拖拽上传
                this.initDragAndDrop();

                // 初始化表单验证
                this.initFormValidation();

                // 加载示例数据
                this.loadSampleData();

                // 设置默认序号
                this.setNextAvailableOrder();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 初始化拖拽上传
            initDragAndDrop() {
                const uploadArea = document.getElementById('uploadArea');

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleImage(files[0]);
                    }
                });
            }

            // 初始化表单验证
            initFormValidation() {
                const form = document.getElementById('partnerForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleAddPartner();
                });
            }

            // 处理图片选择
            handleImage(file) {
                // 验证文件类型
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

                if (!allowedTypes.includes(file.type)) {
                    this.showToast('请选择 JPG、PNG 或 WebP 格式的图片', 'error');
                    return;
                }

                // 验证文件大小（5MB）
                const maxSize = 5 * 1024 * 1024;
                if (file.size > maxSize) {
                    this.showToast('图片大小不能超过 5MB', 'error');
                    return;
                }

                this.selectedImage = file;
                this.displayImagePreview(file);
            }

            // 显示图片预览
            displayImagePreview(file) {
                const reader = new FileReader();
                const preview = document.getElementById('imagePreview');

                reader.onload = (e) => {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };

                reader.readAsDataURL(file);
            }

            // 加载示例数据
            loadSampleData() {
                this.partners = [
                    {
                        id: 1,
                        name: '武林盟主府',
                        image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
                        order: 1
                    },
                    {
                        id: 2,
                        name: '江南书院',
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
                        order: 2
                    },
                    {
                        id: 3,
                        name: '天下第一剑',
                        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
                        order: 3
                    }
                ];
                this.sortPartnersByOrder();
                this.renderPartnerList();
            }

            // 渲染合作伙伴列表
            renderPartnerList() {
                const partnerListContainer = document.getElementById('partnerList');

                if (this.partners.length === 0) {
                    partnerListContainer.innerHTML = `
                        <div class="empty-state col-span-full">
                            <img src="https://unpkg.com/lucide-static@latest/icons/users.svg" alt="无合作伙伴">
                            <p>暂无合作伙伴</p>
                        </div>
                    `;
                    return;
                }

                const partnerCards = this.partners.map(partner => this.createPartnerCard(partner)).join('');
                partnerListContainer.innerHTML = partnerCards;
            }

            // 创建合作伙伴卡片
            createPartnerCard(partner) {
                return `
                    <div class="partner-item" data-partner-id="${partner.id}">
                        <img src="${partner.image}" alt="${partner.name}" class="partner-image">
                        <div class="partner-name">${partner.name}</div>
                        <div class="partner-order">序号: ${partner.order}</div>
                        <div class="partner-actions">
                            <button class="btn-secondary" onclick="partnerManagement.editPartner(${partner.id})">
                                编辑
                            </button>
                            <button class="btn-danger" onclick="partnerManagement.deletePartner(${partner.id})">
                                删除
                            </button>
                        </div>
                    </div>
                `;
            }

            // 处理添加合作伙伴
            async handleAddPartner() {
                if (this.isAdding) return;

                const partnerName = document.getElementById('partnerName').value.trim();
                const partnerOrder = parseInt(document.getElementById('partnerOrder').value);

                if (!partnerName) {
                    this.showToast('请输入合作伙伴名称', 'error');
                    return;
                }

                if (!partnerOrder || partnerOrder < 1 || partnerOrder > 999) {
                    this.showToast('请输入有效的显示序号（1-999）', 'error');
                    return;
                }

                // 检查序号是否已存在
                if (this.partners.some(p => p.order === partnerOrder)) {
                    this.showToast('该序号已被使用，请选择其他序号', 'error');
                    return;
                }

                if (!this.selectedImage) {
                    this.showToast('请选择合作伙伴图片', 'error');
                    return;
                }

                this.isAdding = true;
                this.updateAddButton(true);

                try {
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // 创建新合作伙伴
                    const newPartner = {
                        id: Date.now(),
                        name: partnerName,
                        image: URL.createObjectURL(this.selectedImage),
                        order: partnerOrder
                    };

                    this.partners.push(newPartner);
                    this.sortPartnersByOrder();
                    this.renderPartnerList();

                    this.showToast('合作伙伴添加成功！', 'success');
                    this.resetForm();

                } catch (error) {
                    console.error('添加合作伙伴失败:', error);
                    this.showToast('添加失败，请稍后重试', 'error');
                } finally {
                    this.isAdding = false;
                    this.updateAddButton(false);
                }
            }

            // 编辑合作伙伴
            editPartner(partnerId) {
                const partner = this.partners.find(p => p.id === partnerId);
                if (!partner) return;

                const newName = prompt('请输入新的合作伙伴名称:', partner.name);
                if (newName && newName.trim() !== partner.name) {
                    partner.name = newName.trim();

                    const newOrderStr = prompt('请输入新的显示序号 (1-999):', partner.order);
                    if (newOrderStr) {
                        const newOrder = parseInt(newOrderStr);
                        if (newOrder >= 1 && newOrder <= 999) {
                            // 检查序号是否已被其他合作伙伴使用
                            if (this.partners.some(p => p.id !== partnerId && p.order === newOrder)) {
                                this.showToast('该序号已被使用，序号未更改', 'error');
                            } else {
                                partner.order = newOrder;
                                this.sortPartnersByOrder();
                            }
                        } else {
                            this.showToast('序号必须在1-999之间，序号未更改', 'error');
                        }
                    }

                    this.renderPartnerList();
                    this.showToast('合作伙伴信息已更新', 'success');
                }
            }

            // 删除合作伙伴
            deletePartner(partnerId) {
                const partner = this.partners.find(p => p.id === partnerId);
                if (!partner) return;

                if (confirm(`确定要删除合作伙伴"${partner.name}"吗？`)) {
                    this.partners = this.partners.filter(p => p.id !== partnerId);
                    this.renderPartnerList();
                    this.showToast('合作伙伴已删除', 'success');
                }
            }

            // 更新添加按钮状态
            updateAddButton(isLoading) {
                const addBtn = document.getElementById('addBtn');
                const addText = document.getElementById('addText');

                if (isLoading) {
                    addBtn.disabled = true;
                    addText.textContent = '添加中...';
                } else {
                    addBtn.disabled = false;
                    addText.textContent = '添加合作伙伴';
                }
            }

            // 重置表单
            resetForm() {
                document.getElementById('partnerForm').reset();
                document.getElementById('imagePreview').style.display = 'none';
                this.selectedImage = null;
                this.setNextAvailableOrder();
            }

            // 按序号排序合作伙伴
            sortPartnersByOrder() {
                this.partners.sort((a, b) => a.order - b.order);
            }

            // 设置下一个可用序号
            setNextAvailableOrder() {
                let nextOrder = 1;
                const usedOrders = this.partners.map(p => p.order).sort((a, b) => a - b);

                for (let order of usedOrders) {
                    if (order === nextOrder) {
                        nextOrder++;
                    } else {
                        break;
                    }
                }

                document.getElementById('partnerOrder').value = nextOrder;
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');
                tabbarIframe.addEventListener('load', () => {
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }
        }

        // 全局函数
        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (file) {
                partnerManagement.handleImage(file);
            }
        }

        function goBack() {
            window.history.back();
        }

        // 全局变量
        let partnerManagement;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            partnerManagement = new PartnerManagement();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                switch(event.data.tab) {
                    case 'home':
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
