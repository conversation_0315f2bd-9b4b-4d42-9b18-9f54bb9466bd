<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 关于我们</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 应用图标样式 */
        .app-icon {
            width: 100px;
            height: 100px;
            border-radius: 20px;
            border: 3px solid rgba(212, 175, 55, 0.5);
            box-shadow:
                0 0 25px rgba(212, 175, 55, 0.3),
                inset 0 0 15px rgba(212, 175, 55, 0.1);
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .app-icon:hover {
            border-color: rgba(212, 175, 55, 0.8);
            box-shadow:
                0 0 35px rgba(212, 175, 55, 0.5),
                inset 0 0 20px rgba(212, 175, 55, 0.2);
        }
        
        /* 功能特色项样式 */
        .feature-item {
            background: rgba(74, 74, 74, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .feature-item:hover {
            background: rgba(74, 74, 74, 0.4);
            border-color: rgba(212, 175, 55, 0.3);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.15);
            transform: translateY(-2px);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            border: 1px solid rgba(212, 175, 55, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }
        
        /* 链接按钮样式 */
        .link-btn {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }
        
        .link-btn:hover {
            background: rgba(74, 74, 74, 0.5);
            border-color: rgba(212, 175, 55, 0.4);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.1);
        }
        
        /* 版本标签样式 */
        .version-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: #D4AF37;
        }
        
        /* 团队成员样式 */
        .team-member {
            text-align: center;
            padding: 16px;
            background: rgba(74, 74, 74, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .team-member:hover {
            background: rgba(74, 74, 74, 0.2);
            border-color: rgba(212, 175, 55, 0.2);
        }
        
        .team-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid rgba(212, 175, 55, 0.3);
            margin: 0 auto 8px;
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 80px 20px 140px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 为不同模块添加延迟效果 */
        .scroll-animate:nth-child(1) { transition-delay: 0ms; }
        .scroll-animate:nth-child(2) { transition-delay: 100ms; }
        .scroll-animate:nth-child(3) { transition-delay: 200ms; }
        .scroll-animate:nth-child(4) { transition-delay: 300ms; }
        .scroll-animate:nth-child(5) { transition-delay: 400ms; }
        .scroll-animate:nth-child(6) { transition-delay: 500ms; }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 反馈按钮样式 */
        .feedback-btn {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
            margin-top: 16px;
        }
        
        .feedback-btn:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            transform: translateY(-2px);
        }
        
        /* 更新日志样式 */
        .changelog-item {
            border-left: 3px solid rgba(212, 175, 55, 0.3);
            padding-left: 16px;
            margin-bottom: 16px;
            padding-bottom: 12px;
        }
        
        .changelog-item:last-child {
            margin-bottom: 0;
        }
        
        .changelog-version {
            color: #D4AF37;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }
        
        .changelog-date {
            color: rgba(245, 245, 220, 0.6);
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .changelog-content {
            color: #F5F5DC;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 应用介绍区域 -->
        <div class="ink-card p-6 text-center scroll-animate mb-6">
            <div class="flex flex-col items-center">
                <div class="app-icon mb-4">
                    <img src="https://unpkg.com/lucide-static@latest/icons/sword.svg" alt="燕友圈"
                         class="w-12 h-12" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                </div>
                <h1 class="text-2xl font-bold gold-text mb-2">燕友圈</h1>
                <p class="text-lg text-gray-300 mb-3">江湖风云榜</p>
                <div class="version-badge">
                    <img src="https://unpkg.com/lucide-static@latest/icons/tag.svg" alt="版本"
                         class="w-3 h-3 mr-1" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <span>v1.0.0</span>
                </div>
                <p class="text-gray-400 text-sm mt-4 leading-relaxed">
                    一个充满古典武侠韵味的排行榜应用，汇聚江湖英雄豪杰，展现武林风云变幻。在这里，每一位侠客都能找到属于自己的江湖传说。
                </p>
            </div>
        </div>
        
        <!-- 功能特色区域 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/sparkles.svg" alt="特色"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 class="text-xl font-semibold gold-text">功能特色</h2>
            </div>
            
            <div class="grid grid-cols-1 gap-4">
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/trophy.svg" alt="排行榜"
                             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    </div>
                    <h3 class="font-semibold text-gray-200 mb-2">江湖排行榜</h3>
                    <p class="text-gray-400 text-sm">实时更新的武林高手排行，见证每一次江湖风云变幻</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/users.svg" alt="社交"
                             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    </div>
                    <h3 class="font-semibold text-gray-200 mb-2">侠客社交</h3>
                    <p class="text-gray-400 text-sm">结识志同道合的江湖朋友，分享武林心得体会</p>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/star.svg" alt="成就"
                             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    </div>
                    <h3 class="font-semibold text-gray-200 mb-2">成就系统</h3>
                    <p class="text-gray-400 text-sm">完成各种江湖挑战，获得专属称号和奖励</p>
                </div>
            </div>
        </div>

        <!-- 版本信息区域 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" alt="版本"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 class="text-xl font-semibold gold-text">版本信息</h2>
            </div>

            <div class="space-y-4">
                <div class="changelog-item">
                    <div class="changelog-version">v1.0.0</div>
                    <div class="changelog-date">2024年1月15日</div>
                    <div class="changelog-content">
                        • 正式发布燕友圈江湖风云榜<br>
                        • 支持用户注册和个人资料管理<br>
                        • 实现基础排行榜功能<br>
                        • 添加古典武侠风格界面设计
                    </div>
                </div>

                <div class="changelog-item">
                    <div class="changelog-version">v0.9.0 Beta</div>
                    <div class="changelog-date">2023年12月20日</div>
                    <div class="changelog-content">
                        • 内测版本发布<br>
                        • 完成核心功能开发<br>
                        • 优化用户体验和界面设计
                    </div>
                </div>
            </div>
        </div>

        <!-- 开发团队区域 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/heart.svg" alt="团队"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 class="text-xl font-semibold gold-text">开发团队</h2>
            </div>

            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-200 mb-2">燕友圈工作室</h3>
                <p class="text-gray-400 text-sm leading-relaxed">
                    我们是一群热爱古典文化和现代技术的开发者，致力于将传统武侠文化与现代互联网技术完美融合，为用户打造独特的江湖体验。
                </p>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="team-member">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face"
                         alt="产品经理" class="team-avatar">
                    <div class="text-sm font-semibold text-gray-200">剑客无名</div>
                    <div class="text-xs text-gray-400">产品经理</div>
                </div>

                <div class="team-member">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face"
                         alt="技术总监" class="team-avatar">
                    <div class="text-sm font-semibold text-gray-200">代码侠客</div>
                    <div class="text-xs text-gray-400">技术总监</div>
                </div>
            </div>
        </div>

        <!-- 法律信息区域 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/shield-check.svg" alt="法律"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 class="text-xl font-semibold gold-text">法律信息</h2>
            </div>

            <div class="space-y-2">
                <a href="#" class="link-btn" onclick="showUserAgreement()">
                    <div class="flex items-center">
                        <img src="https://unpkg.com/lucide-static@latest/icons/file-text.svg" alt="协议"
                             class="w-4 h-4 mr-3" style="filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);">
                        <span>用户服务协议</span>
                    </div>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </a>

                <a href="#" class="link-btn" onclick="showPrivacyPolicy()">
                    <div class="flex items-center">
                        <img src="https://unpkg.com/lucide-static@latest/icons/lock.svg" alt="隐私"
                             class="w-4 h-4 mr-3" style="filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);">
                        <span>隐私保护政策</span>
                    </div>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </a>
            </div>
        </div>

        <!-- 意见反馈区域 -->
        <div class="ink-card p-6 scroll-animate mb-6">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/message-circle.svg" alt="反馈"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 class="text-xl font-semibold gold-text">意见反馈</h2>
            </div>

            <p class="text-gray-400 text-sm mb-4 leading-relaxed">
                您的建议是我们前进的动力！如果您在使用过程中遇到任何问题，或者有任何改进建议，欢迎随时联系我们。
            </p>

            <div class="space-y-3">
                <div class="flex items-center text-sm text-gray-300">
                    <img src="https://unpkg.com/lucide-static@latest/icons/mail.svg" alt="邮箱"
                         class="w-4 h-4 mr-3" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <span><EMAIL></span>
                </div>

                <div class="flex items-center text-sm text-gray-300">
                    <img src="https://unpkg.com/lucide-static@latest/icons/phone.svg" alt="客服"
                         class="w-4 h-4 mr-3" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <span>客服热线：************</span>
                </div>
            </div>

            <button class="feedback-btn" onclick="openFeedback()">
                <span>在线反馈</span>
            </button>
        </div>

        <!-- 版权信息 -->
        <div class="text-center text-gray-500 text-xs scroll-animate">
            <p class="mb-2">© 2024 燕友圈工作室 版权所有</p>
            <p>江湖路远，侠义永存</p>
        </div>
    </div>

    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 关于我们页面管理器
        class AboutManager {
            constructor() {
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();

                // 初始化标签栏
                this.initTabbar();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                // 观察所有需要动画的元素
                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');

                // 等待iframe加载完成
                tabbarIframe.addEventListener('load', () => {
                    // 向标签栏发送消息，设置当前活动标签为"我的"
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                // 根据类型设置样式
                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 全局函数
        function goBack() {
            window.history.back();
        }

        function showUserAgreement() {
            alert('用户服务协议\n\n欢迎使用燕友圈！\n\n本协议是您与燕友圈工作室之间关于使用燕友圈服务的法律协议。\n\n1. 服务内容\n燕友圈为用户提供江湖排行榜、社交互动等服务。\n\n2. 用户义务\n用户应遵守相关法律法规，不得发布违法违规内容。\n\n3. 隐私保护\n我们承诺保护用户隐私，详见隐私保护政策。\n\n4. 免责声明\n在法律允许范围内，燕友圈对服务中断、数据丢失等不承担责任。\n\n如有疑问，请联系客服。');
        }

        function showPrivacyPolicy() {
            alert('隐私保护政策\n\n燕友圈工作室非常重视用户隐私保护。\n\n1. 信息收集\n我们仅收集为提供服务所必需的信息，包括：\n- 基本账户信息（昵称、头像等）\n- 使用行为数据（排行榜参与情况等）\n\n2. 信息使用\n收集的信息仅用于：\n- 提供和改进服务\n- 用户身份验证\n- 数据统计分析\n\n3. 信息保护\n我们采用行业标准的安全措施保护用户信息。\n\n4. 信息共享\n除法律要求外，我们不会向第三方分享用户个人信息。\n\n5. 用户权利\n用户有权查看、修改或删除个人信息。\n\n如有隐私相关问题，请联系我们。');
        }

        function openFeedback() {
            aboutManager.showToast('反馈功能开发中，请通过邮箱或客服热线联系我们', 'info');
        }

        // 全局变量
        let aboutManager;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            aboutManager = new AboutManager();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 允许来自同源或iframe的消息
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
