#!/bin/bash

# 服务启动脚本 - MySQL 已确认可以正常工作

echo "🚀 启动燕友圈榜单系统服务"
echo "========================="

# 1. 清理现有服务
echo "1. 清理现有服务..."
docker-compose -f docker-compose.prod.yml down

# 2. 检查必要文件
echo ""
echo "2. 检查必要文件..."
required_files=(".env.prod" "nginx/nginx.conf" "Dockerfile")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

# 3. 检查 Nginx 配置
if [ ! -f "nginx/conf.d/yysls.conf" ] && [ ! -f "nginx/conf.d/yysls-dev.conf" ]; then
    echo "⚠️  缺少 Nginx 站点配置，使用开发配置..."
    if [ -f "nginx/conf.d/yysls-dev.conf.example" ]; then
        cp nginx/conf.d/yysls-dev.conf.example nginx/conf.d/yysls-dev.conf
        sed -i 's/localhost your-dev-domain.com/localhost/' nginx/conf.d/yysls-dev.conf
        echo "✅ 已创建开发环境配置"
    fi
fi

# 4. 生成 SSL 证书（如果需要）
if [ -f "nginx/conf.d/yysls.conf" ] && [ ! -f "ssl/cert.pem" ]; then
    echo ""
    echo "3. 生成自签名 SSL 证书..."
    mkdir -p ssl
    openssl genrsa -out ssl/key.pem 2048
    openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=YYSLS/OU=IT/CN=localhost"
    chmod 600 ssl/key.pem
    chmod 644 ssl/cert.pem
    echo "✅ SSL 证书已生成"
fi

# 5. 启动服务
echo ""
echo "4. 启动服务..."
echo "启动顺序：数据库 -> Redis -> 应用 -> Nginx"

# 先启动数据库
echo "启动数据库..."
docker-compose -f docker-compose.prod.yml up -d db
sleep 10

# 检查数据库状态
if docker ps | grep -q yysls-ranking-db; then
    echo "✅ 数据库启动成功"
    
    # 等待数据库就绪
    echo "等待数据库就绪..."
    for i in {1..30}; do
        if docker logs yysls-ranking-db 2>&1 | grep -q "ready for connections"; then
            echo "✅ 数据库就绪"
            break
        fi
        echo "等待中... ($i/30)"
        sleep 2
    done
    
    # 启动 Redis
    echo "启动 Redis..."
    docker-compose -f docker-compose.prod.yml up -d redis
    sleep 5
    
    # 启动应用
    echo "启动应用..."
    docker-compose -f docker-compose.prod.yml up -d app
    sleep 10
    
    # 启动 Nginx
    echo "启动 Nginx..."
    docker-compose -f docker-compose.prod.yml up -d nginx
    sleep 5
    
else
    echo "❌ 数据库启动失败"
    docker logs yysls-ranking-db --tail=20
    exit 1
fi

# 6. 检查服务状态
echo ""
echo "5. 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 7. 健康检查
echo ""
echo "6. 健康检查..."

# 检查数据库连接
if docker exec yysls-ranking-db mysqladmin ping -h localhost -u root -p$(grep MYSQL_ROOT_PASSWORD .env.prod | cut -d'=' -f2) 2>/dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "⚠️  数据库连接测试失败"
fi

# 检查应用健康
sleep 5
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过"
elif curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过（通过 Nginx）"
else
    echo "⚠️  应用健康检查失败"
    echo "应用日志："
    docker logs yysls-ranking-app --tail=10
fi

# 8. 显示访问信息
echo ""
echo "🎉 服务启动完成！"
echo ""
echo "📋 访问信息："
echo "   - 前端地址: http://localhost"
echo "   - API 文档: http://localhost/docs"
echo "   - 数据库端口: 3306"
echo ""
echo "🔧 管理命令："
echo "   - 查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "   - 重启服务: docker-compose -f docker-compose.prod.yml restart"
echo "   - 停止服务: docker-compose -f docker-compose.prod.yml down"
echo ""
echo "📊 服务状态："
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "✅ 启动脚本执行完成"
