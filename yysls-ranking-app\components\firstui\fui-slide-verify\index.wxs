var twidth = 300
var swidth = 32
var range = 3
var tleft = 300

function isPC() {
	if (typeof navigator !== 'object') return false;
	var userAgentInfo = navigator.userAgent;
	var Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"];
	var flag = true;
	for (var v = 0; v < Agents.length - 1; v++) {
		if (userAgentInfo.indexOf(Agents[v]) > 0) {
			flag = false;
			break;
		}
	}
	return flag;
}
var isH5 = false
if (typeof window === 'object') isH5 = true

function bool(str) {
	return str === 'true' || str == true ? true : false
}

function touchstart(e, ins) {
	var state = e.instance.getState()
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
	} else {
		touch = e.touches[0] || e.changedTouches[0]
	}
	var dataset = e.instance.getDataset()
	state.startX = touch.pageX
	swidth = +dataset.swidth
	range = +dataset.range
	twidth = +dataset.width
	tleft = +dataset.tleft
	//H5获取bool值为undefined
	state.pass = (+dataset.pass) == 1 ? true : false
}

function styleChange(left, ins) {
	if (!ins) return;
	var slider = ins.selectComponent('.fui-sv__slider')
	var foreground = ins.selectComponent('.fui-sv__foreground')
	if (!slider || !foreground) return;
	if (left == 0) {
		slider.removeClass('fui-sv__un-ani')
		foreground.removeClass('fui-sv__un-ani')
		slider.addClass('fui-sv__reset-ani')
		foreground.addClass('fui-sv__reset-ani')
	} else {

		slider.removeClass('fui-sv__reset-ani')
		foreground.removeClass('fui-sv__reset-ani')
		slider.addClass('fui-sv__un-ani')
		foreground.addClass('fui-sv__un-ani')
	}
	slider.setStyle({
		transform: 'translate3d(' + left + 'px,0,0)'
	})
	foreground.setStyle({
		width: left + 'px'
	})
}

function touchmove(e, ins, event) {
	if (e.preventDefault) {
		// 阻止页面滚动
		e.preventDefault()
	}
	var state = {}
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
		state = event.instance.getState()
	} else {
		state = e.instance.getState()
		touch = e.touches[0] || e.changedTouches[0]
	}
	if (state.pass) return;
	var pageX = touch.pageX;
	var left = pageX - state.startX + (state.lastLeft || 0);
	left = left < 0 ? 0 : left;
	var width = twidth - swidth;
	left = left >= width ? width : left;
	state.startX = pageX
	state.lastLeft = left
	styleChange(left, ins)
}

function touchend(e, ins, event) {
	var state = {}
	if (isH5 && isPC()) {
		state = event.instance.getState()
	} else {
		state = e.instance.getState()
	}
	if (state.pass) return;
	var left = tleft - swidth
	if (Math.abs(left - state.lastLeft) <= range) {
		// styleChange(left, ins)
		state.pass = true
		ins.callMethod('success')
	} else {
		state.startX = 0;
		state.lastLeft = 0;
		state.pass = false;
		styleChange(0, ins)
		ins.callMethod('fail')
	}
}

function slidereset(reset, oldreset, owner, ins) {
	var state = ins.getState()
	if (reset > 0) {
		state.startX = 0;
		state.lastLeft = 0;
		state.pass = false;
		styleChange(0, owner)
	}
}

var movable = false;

function mousedown(e, ins) {
	if (!isH5 || !isPC()) return
	touchstart(e, ins)
	movable = true
	window.onmousemove = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchmove(event, ins, e)
	}
	window.onmouseup = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchend(event, ins, e)
		movable = false
	}
}


module.exports = {
	touchstart: touchstart,
	touchmove: touchmove,
	touchend: touchend,
	mousedown: mousedown,
	slidereset: slidereset
}