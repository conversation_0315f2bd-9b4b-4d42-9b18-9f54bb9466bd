"""Simplify sponsor model - keep only name and logo_url

Revision ID: 001_simplify_sponsor_model
Revises: 
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001_simplify_sponsor_model'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：简化赞助商表结构"""
    
    # 创建新的简化赞助商表
    op.create_table('sponsors_new',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False, comment='赞助商名称'),
        sa.Column('logo_url', sa.String(length=500), nullable=True, comment='Logo URL（用作头像）'),
        sa.Column('sort_order', sa.Integer(), nullable=False, default=0, comment='排序顺序'),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='赞助商表'
    )

    # 创建索引
    op.create_index('idx_sponsors_new_sort_order', 'sponsors_new', ['sort_order'])
    op.create_index('idx_sponsors_new_is_active', 'sponsors_new', ['is_active'])
    op.create_index('idx_sponsors_new_created_at', 'sponsors_new', ['created_at'])
    
    # 如果原表存在，迁移数据
    connection = op.get_bind()
    
    # 检查原表是否存在
    inspector = sa.inspect(connection)
    if 'sponsors' in inspector.get_table_names():
        # 迁移数据：只保留name、logo_url、sort_order和is_active字段
        op.execute("""
            INSERT INTO sponsors_new (id, name, logo_url, sort_order, is_active, created_at, updated_at)
            SELECT id, name, logo_url,
                   COALESCE(sort_order, 0) as sort_order,
                   COALESCE(is_active, true) as is_active,
                   created_at, updated_at
            FROM sponsors
        """)
        
        # 删除原表
        op.drop_table('sponsors')
    
    # 重命名新表
    op.rename_table('sponsors_new', 'sponsors')


def downgrade() -> None:
    """降级数据库：恢复完整的赞助商表结构"""
    
    # 创建完整的赞助商表
    op.create_table('sponsors_full',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False, comment='赞助商名称'),
        sa.Column('logo_url', sa.String(length=500), nullable=True, comment='Logo URL'),
        sa.Column('avatar_url', sa.String(length=500), nullable=True, comment='头像URL'),
        sa.Column('contact_person', sa.String(length=100), nullable=True, comment='联系人'),
        sa.Column('contact_phone', sa.String(length=20), nullable=True, comment='联系电话'),
        sa.Column('contact_email', sa.String(length=100), nullable=True, comment='联系邮箱'),
        sa.Column('contact_wechat', sa.String(length=100), nullable=True, comment='微信号'),
        sa.Column('description', sa.Text(), nullable=True, comment='赞助商描述'),
        sa.Column('website_url', sa.String(length=500), nullable=True, comment='官网地址'),
        sa.Column('address', sa.String(length=500), nullable=True, comment='地址'),
        sa.Column('display_order', sa.Integer(), nullable=False, default=0, comment='显示顺序'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, comment='是否启用'),
        sa.Column('is_featured', sa.Boolean(), nullable=False, default=False, comment='是否推荐'),
        sa.Column('cooperation_level', sa.String(length=50), nullable=True, comment='合作级别'),
        sa.Column('cooperation_start_date', sa.DateTime(), nullable=True, comment='合作开始日期'),
        sa.Column('cooperation_end_date', sa.DateTime(), nullable=True, comment='合作结束日期'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='赞助商表'
    )
    
    # 创建索引
    op.create_index('idx_sponsors_full_display_order', 'sponsors_full', ['display_order'])
    op.create_index('idx_sponsors_full_is_active', 'sponsors_full', ['is_active'])
    op.create_index('idx_sponsors_full_created_at', 'sponsors_full', ['created_at'])
    
    # 迁移数据
    op.execute("""
        INSERT INTO sponsors_full (id, name, logo_url, created_at, updated_at, display_order, is_active, is_featured)
        SELECT id, name, logo_url, created_at, updated_at, sort_order, is_active, false
        FROM sponsors
    """)
    
    # 删除简化表
    op.drop_table('sponsors')
    
    # 重命名完整表
    op.rename_table('sponsors_full', 'sponsors')
