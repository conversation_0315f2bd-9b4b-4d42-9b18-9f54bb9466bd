[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    auth: 认证相关测试
    database: 数据库相关测试
    slow: 慢速测试
    wechat: 微信相关测试
    email: 邮件相关测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning

# 异步测试支持
asyncio_mode = auto

# 测试数据库
env_file = .env.test
