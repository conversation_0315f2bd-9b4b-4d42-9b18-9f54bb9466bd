-- 修正ranking_details表结构以匹配SQLAlchemy模型
-- 执行时间: 2024-12-19
-- 数据库类型: MySQL 8.0+
-- 说明: 根据当前SQLAlchemy模型调整数据库表结构

USE yysls_ranking;

-- 备份当前数据（可选）
-- CREATE TABLE ranking_details_backup AS SELECT * FROM ranking_details;

-- 检查当前表结构
SELECT 'Current ranking_details structure:' AS info;
DESCRIBE ranking_details;

-- 根据您提供的表结构，需要进行以下调整：

-- 1. 添加缺失的字段（如果不存在）
-- 检查并添加participant_name字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'yysls_ranking' 
     AND TABLE_NAME = 'ranking_details' 
     AND COLUMN_NAME = 'participant_name') = 0,
    'ALTER TABLE ranking_details ADD COLUMN participant_name VARCHAR(100) NOT NULL COMMENT ''参与者姓名''',
    'SELECT ''participant_name字段已存在'' AS status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加rank_range字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'yysls_ranking' 
     AND TABLE_NAME = 'ranking_details' 
     AND COLUMN_NAME = 'rank_range') = 0,
    'ALTER TABLE ranking_details ADD COLUMN rank_range VARCHAR(20) NULL COMMENT ''排名范围''',
    'SELECT ''rank_range字段已存在'' AS status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加notes字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'yysls_ranking' 
     AND TABLE_NAME = 'ranking_details' 
     AND COLUMN_NAME = 'notes') = 0,
    'ALTER TABLE ranking_details ADD COLUMN notes TEXT NULL COMMENT ''备注''',
    'SELECT ''notes字段已存在'' AS status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 添加team_name字段（主要任务）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'yysls_ranking' 
     AND TABLE_NAME = 'ranking_details' 
     AND COLUMN_NAME = 'team_name') = 0,
    'ALTER TABLE ranking_details ADD COLUMN team_name VARCHAR(100) NULL COMMENT ''队伍名称''',
    'SELECT ''team_name字段已存在'' AS status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 修改completion_seconds字段为可空（如果需要）
ALTER TABLE ranking_details 
MODIFY COLUMN completion_seconds INT NULL COMMENT '完成时间（秒）';

-- 显示最终表结构
SELECT 'Updated ranking_details structure:' AS info;
DESCRIBE ranking_details;

-- 显示完成信息
SELECT 'ranking_details表结构已根据SQLAlchemy模型进行调整' AS status;
