# 榜单类型中文名称字段修改说明

## 修改概述

为 `/api/v1/rankings` 接口的榜单列表返回数据中增加了一个新的字段 `ranking_type_name`，用于显示榜单类型的中文名称。

## 修改内容

### 1. 新增映射函数

在 `app/schemas/ranking.py` 文件中新增了 `get_ranking_type_display_name` 函数：

```python
def get_ranking_type_display_name(ranking_type: RankingType) -> str:
    """获取榜单类型的中文显示名称"""
    type_mapping = {
        RankingType.FIVE_PERSON: "5人榜单",
        RankingType.TEN_PERSON: "10人榜单"
    }
    return type_mapping.get(ranking_type, str(ranking_type))
```

### 2. 修改响应模型

在 `RankingResponse` 模型中新增了 `ranking_type_name` 字段：

```python
class RankingResponse(RankingBase):
    """榜单响应模型"""
    id: int = Field(description="榜单ID")
    total_participants: int = Field(description="总参与人数")
    status: RankingStatus = Field(description="榜单状态")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    ranking_type_name: str = Field(default="", description="榜单类型中文名称")  # 新增字段
    
    class Config:
        from_attributes = True
```

### 3. 修改API接口逻辑

在 `app/api/v1/endpoints/rankings.py` 文件中修改了以下接口，确保返回数据包含中文名称：

- `get_rankings` - 获取榜单列表
- `get_ranking` - 获取榜单详情
- `create_ranking` - 创建榜单
- `update_ranking` - 更新榜单
- `update_ranking_status` - 更新榜单状态

修改示例（以 `get_rankings` 为例）：

```python
# 转换为响应模型
ranking_responses = []
for ranking in rankings:
    ranking_data = RankingResponse.model_validate(ranking)
    # 确保设置榜单类型中文名称
    ranking_data.ranking_type_name = get_ranking_type_display_name(ranking.ranking_type)
    ranking_responses.append(ranking_data)
```

## 字段映射关系

| 榜单类型枚举值 | 中文显示名称 |
|---------------|-------------|
| `5_person`    | `5人榜单`   |
| `10_person`   | `10人榜单`  |

## API响应示例

### 修改前的响应格式

```json
{
  "code": 200,
  "message": "获取榜单列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "第1期5人竞速榜单",
        "period": 1,
        "ranking_type": "5_person",
        "start_time": "2024-01-01T10:00:00",
        "end_time": "2024-01-01T18:00:00",
        "team_size_limit": 5,
        "total_participants": 25,
        "status": "in_progress",
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 修改后的响应格式

```json
{
  "code": 200,
  "message": "获取榜单列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "第1期5人竞速榜单",
        "period": 1,
        "ranking_type": "5_person",
        "ranking_type_name": "5人榜单",  // 新增字段
        "start_time": "2024-01-01T10:00:00",
        "end_time": "2024-01-01T18:00:00",
        "team_size_limit": 5,
        "total_participants": 25,
        "status": "in_progress",
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 向后兼容性

- ✅ 保持了所有原有字段不变
- ✅ 新增的 `ranking_type_name` 字段作为补充信息
- ✅ 原有的 `ranking_type` 字段继续保留
- ✅ API响应格式完全向后兼容

## 影响的接口

以下接口的响应数据中都会包含新的 `ranking_type_name` 字段：

1. `GET /api/v1/rankings` - 获取榜单列表
2. `GET /api/v1/rankings/{ranking_id}` - 获取榜单详情
3. `POST /api/v1/rankings` - 创建榜单
4. `PUT /api/v1/rankings/{ranking_id}` - 更新榜单
5. `PUT /api/v1/rankings/{ranking_id}/status` - 更新榜单状态

## 测试文件

创建了以下测试文件来验证修改：

1. `test_ranking_type_name.py` - 测试映射函数和响应模型
2. `test_ranking_api_response.py` - 测试API响应格式

## 使用建议

前端可以使用新的 `ranking_type_name` 字段来直接显示榜单类型的中文名称，无需在前端进行类型映射转换。

```javascript
// 前端使用示例
rankings.forEach(ranking => {
  console.log(`榜单类型: ${ranking.ranking_type_name}`);  // 直接显示中文名称
});
```
