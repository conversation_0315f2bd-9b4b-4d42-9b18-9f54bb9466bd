<template>
	<view class="mask mask-show u_loading" v-if="loadingStore.loadingShow" @touchmove.stop.prevent="">
		<div class="loading-wave">
			<div class="loading-bar"></div>
			<div class="loading-bar"></div>
			<div class="loading-bar"></div>
			<div class="loading-bar"></div>
		</div>
	</view>
</template>

<script setup>
	import { useLoadingStore } from "@/pinia/loading.js"
	
	const loadingStore = useLoadingStore()

</script>

<style lang="scss" scoped>
	.u_loading {
		
		&.mask {
			/* pointer-events: none; */
			position: fixed;
			z-index: 99999;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			height: 100vh;
			width: 100vw;
			/* #ifndef APP-NVUE */
			display: flex;
			/* #endif */
			flex-direction: column;
			justify-content: center;
			align-items: center;
			flex-wrap: wrap;
		}

		&.mask-show {
			background: rgba(0, 0, 0, 0.3);
		}

		.loading-wave {
			width: 300rpx;
			height: 100rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.loading-bar {
			width: 20rpx;
			height: 10rpx;
			margin: 0 10rpx;
			background-color: #910808;
			border-radius: 10rpx;
			animation: loading-wave-animation 1s ease-in-out infinite;
		}

		.loading-bar:nth-child(2) {
			animation-delay: 0.1s;
		}

		.loading-bar:nth-child(3) {
			animation-delay: 0.2s;
		}

		.loading-bar:nth-child(4) {
			animation-delay: 0.3s;
		}

		@keyframes loading-wave-animation {
			0% {
				height: 10rpx;
			}

			50% {
				height: 50rpx;
			}

			100% {
				height: 10rpx;
			}
		}
	}
</style>