#!/usr/bin/env python3
"""
测试SQLAlchemy删除操作修复
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'yysls-ranking-backend'))

from sqlalchemy import create_engine, delete
from sqlalchemy.orm import sessionmaker
from app.models.ranking import RankingDetail

def test_delete_syntax():
    """测试删除语法是否正确"""
    try:
        # 创建内存数据库进行测试
        engine = create_engine("sqlite:///:memory:")
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 测试删除语句语法
        delete_stmt = delete(RankingDetail).where(RankingDetail.ranking_id == 1)
        print("✅ 删除语句语法正确")
        print(f"SQL: {delete_stmt}")
        
        return True
    except Exception as e:
        print(f"❌ 删除语句语法错误: {e}")
        return False

if __name__ == "__main__":
    print("测试SQLAlchemy删除操作修复...")
    success = test_delete_syntax()
    if success:
        print("🎉 修复成功！")
    else:
        print("💥 修复失败！")
