"""
榜单业务服务

处理榜单相关的业务逻辑，包括：
- 榜单创建和管理
- 榜单明细管理
- 排名计算和统计
- 榜单状态管理
"""
import os
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, func, delete
from datetime import datetime, time

from app.services.base import BaseService
from app.models.ranking import Ranking, RankingDetail, RankingStatus, RankingType
from app.utils.excel_handler import ExcelHandler
from app.config import settings


class RankingService(BaseService):
    """榜单服务类"""

    def __init__(self):
        super().__init__(Ranking)
        self.excel_handler = ExcelHandler()
    
    def create_ranking(
        self,
        db: Session,
        ranking_data: Dict[str, Any],
        created_by: int
    ) -> Ranking:
        """创建新榜单"""
        try:
            ranking_data['created_by'] = created_by
            ranking_data['status'] = RankingStatus.NOT_STARTED

            db_ranking = Ranking(**ranking_data)
            db.add(db_ranking)
            db.commit()
            db.refresh(db_ranking)

            self.logger.info(f"创建榜单成功 ID={db_ranking.id}, name={db_ranking.name}")
            return db_ranking
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建榜单失败: {str(e)}")
            raise

    async def create_ranking_with_excel(
        self,
        db: Session,
        ranking_data: Dict[str, Any],
        created_by: int,
        excel_file_path: Optional[str] = None,
        auto_import_details: bool = False
    ) -> Ranking:
        """创建榜单并可选择导入Excel数据"""
        try:
            # 移除Excel相关字段，避免传入模型
            excel_path = ranking_data.pop('excel_file_path', excel_file_path)
            auto_import = ranking_data.pop('auto_import_details', auto_import_details)

            # 创建榜单
            ranking = self.create_ranking(db, ranking_data, created_by)

            # 如果需要导入Excel数据
            if auto_import and excel_path and os.path.exists(excel_path):
                await self._import_excel_details(db, ranking.id, excel_path)

                # 清理临时文件
                try:
                    os.remove(excel_path)
                    self.logger.info(f"清理临时Excel文件: {excel_path}")
                except Exception as e:
                    self.logger.warning(f"清理临时文件失败: {str(e)}")

            return ranking

        except Exception as e:
            db.rollback()
            self.logger.error(f"创建榜单并导入Excel失败: {str(e)}")
            raise
    
    def get_by_period(self, db: Session, period: int) -> List[Ranking]:
        """根据期数获取榜单"""
        try:
            result = db.execute(
                select(Ranking).where(Ranking.period == period)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"根据期数获取榜单失败 period={period}: {str(e)}")
            raise
    
    def get_by_type(self, db: Session, ranking_type: RankingType) -> List[Ranking]:
        """根据类型获取榜单"""
        try:
            result = db.execute(
                select(Ranking).where(Ranking.ranking_type == ranking_type)
                .order_by(Ranking.period.desc())
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"根据类型获取榜单失败 type={ranking_type}: {str(e)}")
            raise
    
    def get_current_rankings(self, db: Session) -> List[Ranking]:
        """获取当前进行中的榜单"""
        try:
            now = datetime.utcnow()
            result = db.execute(
                select(Ranking).where(
                    and_(
                        Ranking.status == RankingStatus.IN_PROGRESS,
                        Ranking.start_time <= now,
                        Ranking.end_time >= now
                    )
                )
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取当前榜单失败: {str(e)}")
            raise
    
    def start_ranking(self, db: Session, ranking_id: int, updated_by: int) -> Ranking:
        """开始榜单"""
        try:
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            if ranking.status != RankingStatus.NOT_STARTED:
                raise ValueError(f"榜单状态不允许开始 status={ranking.status}")

            ranking.status = RankingStatus.IN_PROGRESS
            ranking.updated_by = updated_by

            db.commit()
            db.refresh(ranking)

            self.logger.info(f"开始榜单成功 ID={ranking_id}")
            return ranking
        except Exception as e:
            db.rollback()
            self.logger.error(f"开始榜单失败 ID={ranking_id}: {str(e)}")
            raise
    
    def finish_ranking(self, db: Session, ranking_id: int, updated_by: int) -> Ranking:
        """结束榜单"""
        try:
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            if ranking.status != RankingStatus.IN_PROGRESS:
                raise ValueError(f"榜单状态不允许结束 status={ranking.status}")

            ranking.status = RankingStatus.FINISHED
            ranking.updated_by = updated_by

            db.commit()
            db.refresh(ranking)

            self.logger.info(f"结束榜单成功 ID={ranking_id}")
            return ranking
        except Exception as e:
            db.rollback()
            self.logger.error(f"结束榜单失败 ID={ranking_id}: {str(e)}")
            raise
    
    def add_ranking_detail(
        self,
        db: Session,
        ranking_id: int,
        detail_data: Dict[str, Any]
    ) -> RankingDetail:
        """添加榜单明细"""
        try:
            # 验证榜单存在且状态正确
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            if ranking.status != RankingStatus.IN_PROGRESS:
                raise ValueError(f"榜单状态不允许添加明细 status={ranking.status}")

            detail_data['ranking_id'] = ranking_id

            # 计算总秒数
            if 'completion_time' in detail_data:
                completion_time = detail_data['completion_time']
                if isinstance(completion_time, time):
                    detail_data['completion_seconds'] = (
                        completion_time.hour * 3600 +
                        completion_time.minute * 60 +
                        completion_time.second
                    )

            db_detail = RankingDetail(**detail_data)
            db.add(db_detail)

            # 更新榜单参与人数
            ranking.total_participants += detail_data.get('participant_count', 0)

            db.commit()
            db.refresh(db_detail)

            self.logger.info(f"添加榜单明细成功 ranking_id={ranking_id}, detail_id={db_detail.id}")
            return db_detail
        except Exception as e:
            db.rollback()
            self.logger.error(f"添加榜单明细失败 ranking_id={ranking_id}: {str(e)}")
            raise
    
    def get_ranking_details(self, db: Session, ranking_id: int) -> List[RankingDetail]:
        """获取榜单明细"""
        try:
            result = db.execute(
                select(RankingDetail)
                .where(RankingDetail.ranking_id == ranking_id)
                .order_by(RankingDetail.completion_seconds)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取榜单明细失败 ranking_id={ranking_id}: {str(e)}")
            raise

    def update_ranking_detail(
        self,
        db: Session,
        detail_id: int,
        detail_data: Dict[str, Any]
    ) -> RankingDetail:
        """更新榜单明细"""
        try:
            # 获取明细记录
            detail = db.execute(
                select(RankingDetail).where(RankingDetail.id == detail_id)
            ).scalar_one_or_none()

            if not detail:
                raise ValueError(f"榜单明细不存在 ID={detail_id}")

            # 检查榜单状态
            ranking = self.get(db, detail.ranking_id)
            if ranking and ranking.status == RankingStatus.FINISHED:
                raise ValueError("已结束的榜单不允许修改明细")

            # 更新字段
            for field, value in detail_data.items():
                if hasattr(detail, field) and value is not None:
                    setattr(detail, field, value)

            # 重新计算总秒数（如果更新了完成时间）
            if 'completion_time' in detail_data:
                completion_time = detail_data['completion_time']
                if isinstance(completion_time, time):
                    detail.completion_seconds = (
                        completion_time.hour * 3600 +
                        completion_time.minute * 60 +
                        completion_time.second
                    )

            db.commit()
            db.refresh(detail)

            self.logger.info(f"更新榜单明细成功 detail_id={detail_id}")
            return detail
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新榜单明细失败 detail_id={detail_id}: {str(e)}")
            raise
    
    def get_ranking_statistics(self, db: Session, ranking_id: int) -> Dict[str, Any]:
        """获取榜单统计信息"""
        try:
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            # 获取明细统计
            result = db.execute(
                select(
                    func.count(RankingDetail.id).label('total_records'),
                    func.min(RankingDetail.completion_seconds).label('best_time'),
                    func.max(RankingDetail.completion_seconds).label('worst_time'),
                    func.avg(RankingDetail.completion_seconds).label('avg_time'),
                    func.sum(RankingDetail.participant_count).label('total_participants')
                ).where(RankingDetail.ranking_id == ranking_id)
            )
            stats = result.first()

            return {
                'ranking_id': ranking_id,
                'ranking_name': ranking.name,
                'period': ranking.period,
                'type': ranking.ranking_type,
                'status': ranking.status,
                'total_records': stats.total_records or 0,
                'best_time_seconds': stats.best_time,
                'worst_time_seconds': stats.worst_time,
                'average_time_seconds': float(stats.avg_time) if stats.avg_time else 0,
                'total_participants': stats.total_participants or 0
            }
        except Exception as e:
            self.logger.error(f"获取榜单统计失败 ranking_id={ranking_id}: {str(e)}")
            raise

    def update_status(self, db: Session, ranking_id: int, new_status: RankingStatus) -> Ranking:
        """更新榜单状态"""
        try:
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            old_status = ranking.status
            ranking.status = new_status

            # 如果状态变为进行中，设置开始时间
            if new_status == RankingStatus.IN_PROGRESS and not ranking.start_time:
                ranking.start_time = datetime.utcnow()

            # 如果状态变为已结束，设置结束时间
            if new_status == RankingStatus.FINISHED and not ranking.end_time:
                ranking.end_time = datetime.utcnow()

            db.commit()
            db.refresh(ranking)

            self.logger.info(f"更新榜单状态成功 ID={ranking_id}, {old_status} -> {new_status}")
            return ranking
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新榜单状态失败 ID={ranking_id}: {str(e)}")
            raise

    async def _import_excel_details(self, db: Session, ranking_id: int, excel_file_path: str):
        """从Excel文件导入榜单明细数据"""
        try:
            # 创建临时UploadFile对象来解析Excel
            from fastapi import UploadFile

            with open(excel_file_path, 'rb') as f:
                file_content = f.read()

            # 创建临时文件对象
            import tempfile
            from io import BytesIO

            temp_file = UploadFile(
                filename=os.path.basename(excel_file_path),
                file=BytesIO(file_content)
            )

            # 解析Excel数据
            ranking_details_data = await self.excel_handler.parse_ranking_excel(temp_file)

            # 批量创建榜单明细
            for detail_data in ranking_details_data:
                detail_data['ranking_id'] = ranking_id
                db_detail = RankingDetail(**detail_data)
                db.add(db_detail)

            db.commit()
            self.logger.info(f"成功导入{len(ranking_details_data)}条榜单明细数据到榜单{ranking_id}")

        except Exception as e:
            db.rollback()
            self.logger.error(f"导入Excel榜单明细失败 ranking_id={ranking_id}: {str(e)}")
            raise

    async def update_ranking_with_excel(
        self,
        db: Session,
        ranking_id: int,
        ranking_data: Dict[str, Any],
        updated_by: int,
        excel_file_path: Optional[str] = None,
        auto_import_details: bool = False,
        replace_existing_details: bool = False
    ) -> Ranking:
        """更新榜单并可选择导入Excel数据"""
        try:
            # 获取现有榜单
            ranking = self.get(db, ranking_id)
            if not ranking:
                raise ValueError(f"榜单不存在 ID={ranking_id}")

            # 移除Excel相关字段
            excel_path = ranking_data.pop('excel_file_path', excel_file_path)
            auto_import = ranking_data.pop('auto_import_details', auto_import_details)
            replace_existing = ranking_data.pop('replace_existing_details', replace_existing_details)

            # 更新榜单基本信息
            for key, value in ranking_data.items():
                if hasattr(ranking, key) and value is not None:
                    setattr(ranking, key, value)

            ranking.updated_by = updated_by
            db.commit()
            db.refresh(ranking)

            # 如果需要导入Excel数据
            if auto_import and excel_path and os.path.exists(excel_path):
                # 如果需要替换现有数据，先删除
                if replace_existing:
                    db.execute(
                        delete(RankingDetail).where(RankingDetail.ranking_id == ranking_id)
                    )
                    db.commit()

                # 导入新数据
                await self._import_excel_details(db, ranking_id, excel_path)

                # 清理临时文件
                try:
                    os.remove(excel_path)
                    self.logger.info(f"清理临时Excel文件: {excel_path}")
                except Exception as e:
                    self.logger.warning(f"清理临时文件失败: {str(e)}")

            return ranking

        except Exception as e:
            db.rollback()
            self.logger.error(f"更新榜单并导入Excel失败 ranking_id={ranking_id}: {str(e)}")
            raise


# 创建全局榜单服务实例
ranking_service = RankingService()
