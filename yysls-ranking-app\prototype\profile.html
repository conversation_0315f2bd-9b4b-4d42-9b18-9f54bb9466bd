<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 个人资料</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 头像样式 */
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid rgba(212, 175, 55, 0.5);
            box-shadow:
                0 0 30px rgba(212, 175, 55, 0.3),
                inset 0 0 15px rgba(212, 175, 55, 0.1);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .profile-avatar:hover {
            border-color: rgba(212, 175, 55, 0.8);
            box-shadow:
                0 0 40px rgba(212, 175, 55, 0.5),
                inset 0 0 20px rgba(212, 175, 55, 0.2);
        }
        
        /* 头像编辑按钮 */
        .avatar-edit-btn {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 2px solid #1A1A1A;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
            transition: all 0.3s ease;
        }
        
        .avatar-edit-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.6);
        }
        
        /* 表单输入框样式 */
        .profile-input {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .profile-input:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        .profile-input::placeholder {
            color: rgba(245, 245, 220, 0.5);
        }
        
        /* 选择器样式 */
        .profile-select {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .profile-select:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: rgba(74, 74, 74, 0.5);
            border: 1px solid rgba(245, 245, 220, 0.3);
            color: #F5F5DC;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .btn-secondary:hover {
            background: rgba(74, 74, 74, 0.7);
            border-color: rgba(245, 245, 220, 0.5);
        }
        
        /* 表单标签样式 */
        .form-label {
            color: #D4AF37;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }
        
        /* 编辑模式切换 */
        .edit-mode .profile-input:not(:focus) {
            background: rgba(74, 74, 74, 0.2);
            border-color: rgba(212, 175, 55, 0.1);
        }
        
        .view-mode .profile-input {
            background: transparent;
            border: none;
            padding: 8px 0;
            cursor: default;
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 80px 20px 140px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头像区域 -->
        <div class="ink-card p-6 text-center scroll-animate mb-6">
            <div class="flex flex-col items-center">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=240&h=240&fit=crop&crop=face"
                         alt="用户头像" class="profile-avatar" id="profileAvatar">
                    <div class="avatar-edit-btn" onclick="changeAvatar()">
                        <img src="https://unpkg.com/lucide-static@latest/icons/camera.svg" alt="编辑"
                             class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(7%) sepia(8%) saturate(1161%) hue-rotate(316deg) brightness(97%) contrast(97%);">
                    </div>
                </div>
                <h1 class="text-2xl font-bold gold-text mt-4 mb-2">个人资料</h1>
                <p class="text-gray-400 text-sm">完善您的江湖信息</p>
            </div>
        </div>
        
        <!-- 基本信息表单 -->
        <div class="ink-card p-6 scroll-animate mb-6" id="profileForm">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold gold-text">基本信息</h2>
                <button class="btn-secondary" id="editToggleBtn" onclick="toggleEditMode()">
                    <span id="editBtnText">编辑</span>
                </button>
            </div>
            
            <div class="space-y-4" id="formFields">
                <!-- 昵称 -->
                <div>
                    <label class="form-label">江湖名号</label>
                    <input type="text" class="profile-input" id="nickname" value="剑客无名" readonly>
                </div>
                
                <!-- 性别 -->
                <div>
                    <label class="form-label">性别</label>
                    <select class="profile-select" id="gender" disabled>
                        <option value="male" selected>男</option>
                        <option value="female">女</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <!-- 年龄 -->
                <div>
                    <label class="form-label">年龄</label>
                    <input type="number" class="profile-input" id="age" value="25" readonly min="1" max="120">
                </div>
                
                <!-- 个人简介 -->
                <div>
                    <label class="form-label">江湖简介</label>
                    <textarea class="profile-input" id="bio" rows="3" readonly placeholder="介绍一下您的江湖经历...">行走江湖多年，以剑会友，寻求武道真谛。</textarea>
                </div>
                
                <!-- 所在地 -->
                <div>
                    <label class="form-label">所在地</label>
                    <input type="text" class="profile-input" id="location" value="江南水乡" readonly>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex space-x-4 mt-8" id="actionButtons" style="display: none;">
                <button class="btn-primary flex-1" onclick="saveProfile()">
                    <span>保存修改</span>
                </button>
                <button class="btn-secondary flex-1" onclick="cancelEdit()">
                    <span>取消</span>
                </button>
            </div>
        </div>
        
        <!-- 账户信息 -->
        <div class="ink-card p-6 scroll-animate">
            <h2 class="text-xl font-semibold gold-text mb-4">账户信息</h2>
            <div class="space-y-4">
                <div class="flex justify-between items-center py-3 border-b border-gray-600">
                    <span class="text-gray-300">用户ID</span>
                    <span class="text-gray-400">#YY2024001</span>
                </div>
                <div class="flex justify-between items-center py-3 border-b border-gray-600">
                    <span class="text-gray-300">注册时间</span>
                    <span class="text-gray-400">2024年1月15日</span>
                </div>
                <div class="flex justify-between items-center py-3 border-b border-gray-600">
                    <span class="text-gray-300">江湖等级</span>
                    <span class="gold-text font-semibold">武林高手</span>
                </div>
                <div class="flex justify-between items-center py-3">
                    <span class="text-gray-300">积分</span>
                    <span class="gold-text font-semibold">1580分</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 页面状态管理
        class ProfileManager {
            constructor() {
                this.isEditMode = false;
                this.originalData = {};
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();

                // 保存原始数据
                this.saveOriginalData();

                // 初始化标签栏
                this.initTabbar();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                // 观察所有需要动画的元素
                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 保存原始数据
            saveOriginalData() {
                this.originalData = {
                    nickname: document.getElementById('nickname').value,
                    gender: document.getElementById('gender').value,
                    age: document.getElementById('age').value,
                    bio: document.getElementById('bio').value,
                    location: document.getElementById('location').value
                };
            }

            // 切换编辑模式
            toggleEditMode() {
                this.isEditMode = !this.isEditMode;

                const formFields = document.getElementById('formFields');
                const actionButtons = document.getElementById('actionButtons');
                const editBtnText = document.getElementById('editBtnText');
                const editToggleBtn = document.getElementById('editToggleBtn');

                if (this.isEditMode) {
                    // 进入编辑模式
                    formFields.classList.add('edit-mode');
                    formFields.classList.remove('view-mode');

                    // 启用所有输入框
                    document.getElementById('nickname').removeAttribute('readonly');
                    document.getElementById('gender').removeAttribute('disabled');
                    document.getElementById('age').removeAttribute('readonly');
                    document.getElementById('bio').removeAttribute('readonly');
                    document.getElementById('location').removeAttribute('readonly');

                    // 显示操作按钮，隐藏编辑按钮
                    actionButtons.style.display = 'flex';
                    editToggleBtn.style.display = 'none';

                } else {
                    // 退出编辑模式
                    formFields.classList.add('view-mode');
                    formFields.classList.remove('edit-mode');

                    // 禁用所有输入框
                    document.getElementById('nickname').setAttribute('readonly', true);
                    document.getElementById('gender').setAttribute('disabled', true);
                    document.getElementById('age').setAttribute('readonly', true);
                    document.getElementById('bio').setAttribute('readonly', true);
                    document.getElementById('location').setAttribute('readonly', true);

                    // 隐藏操作按钮，显示编辑按钮
                    actionButtons.style.display = 'none';
                    editToggleBtn.style.display = 'block';
                    editBtnText.textContent = '编辑';
                }
            }

            // 保存资料
            saveProfile() {
                const profileData = {
                    nickname: document.getElementById('nickname').value,
                    gender: document.getElementById('gender').value,
                    age: document.getElementById('age').value,
                    bio: document.getElementById('bio').value,
                    location: document.getElementById('location').value
                };

                // 简单验证
                if (!profileData.nickname.trim()) {
                    alert('江湖名号不能为空！');
                    return;
                }

                if (profileData.age < 1 || profileData.age > 120) {
                    alert('请输入有效的年龄！');
                    return;
                }

                // 模拟保存过程
                console.log('保存资料:', profileData);

                // 更新原始数据
                this.originalData = { ...profileData };

                // 退出编辑模式
                this.toggleEditMode();

                // 显示成功提示
                this.showToast('资料保存成功！', 'success');
            }

            // 取消编辑
            cancelEdit() {
                // 恢复原始数据
                document.getElementById('nickname').value = this.originalData.nickname;
                document.getElementById('gender').value = this.originalData.gender;
                document.getElementById('age').value = this.originalData.age;
                document.getElementById('bio').value = this.originalData.bio;
                document.getElementById('location').value = this.originalData.location;

                // 退出编辑模式
                this.toggleEditMode();

                this.showToast('已取消编辑', 'info');
            }

            // 更换头像
            changeAvatar() {
                // 模拟头像选择
                const avatars = [
                    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=240&h=240&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=240&h=240&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=240&h=240&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=240&h=240&fit=crop&crop=face'
                ];

                const currentAvatar = document.getElementById('profileAvatar').src;
                const currentIndex = avatars.indexOf(currentAvatar);
                const nextIndex = (currentIndex + 1) % avatars.length;

                document.getElementById('profileAvatar').src = avatars[nextIndex];
                this.showToast('头像已更换', 'success');
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                // 根据类型设置样式
                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 3000);
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');

                // 等待iframe加载完成
                tabbarIframe.addEventListener('load', () => {
                    // 向标签栏发送消息，设置当前活动标签为"我的"
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }
        }

        // 全局函数
        function toggleEditMode() {
            profileManager.toggleEditMode();
        }

        function saveProfile() {
            profileManager.saveProfile();
        }

        function cancelEdit() {
            profileManager.cancelEdit();
        }

        function changeAvatar() {
            profileManager.changeAvatar();
        }

        function goBack() {
            // 如果正在编辑模式，先询问是否保存
            if (profileManager.isEditMode) {
                if (confirm('您有未保存的修改，是否要保存？')) {
                    profileManager.saveProfile();
                    setTimeout(() => {
                        window.history.back();
                    }, 500);
                } else {
                    profileManager.cancelEdit();
                    setTimeout(() => {
                        window.history.back();
                    }, 300);
                }
            } else {
                window.history.back();
            }
        }

        // 全局变量
        let profileManager;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            profileManager = new ProfileManager();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 允许来自同源或iframe的消息
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        window.location.href = 'user.html';
                        break;
                }
            }
        });

        // 防止页面刷新时丢失编辑状态
        window.addEventListener('beforeunload', (event) => {
            if (profileManager && profileManager.isEditMode) {
                event.preventDefault();
                event.returnValue = '您有未保存的修改，确定要离开吗？';
                return event.returnValue;
            }
        });
    </script>
</body>
</html>
