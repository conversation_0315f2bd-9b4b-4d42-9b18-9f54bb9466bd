
import base from "@/config/baseUrl";
import Request from '@/utils/luch-request/index.js';
import { useStoragePiniaStore } from "@/pinia/storage.js"
import { useLoadingStore } from "@/pinia/loading.js"
const storagePiniaStore = useStoragePiniaStore()
const loadingStore = useLoadingStore()

const http = uni.$http

// 初始化请求配置
http.setConfig((config) => {
    /* config 为默认全局配置*/
    config.baseURL = base.baseUrl; /* 根域名 */
    config.header = {
        // 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
    config.custom = {
        load:true,//是否显示加载动画
        isFactory: false,//true:返回的数据成功只返回data  false:返回response
        catch: true,//默认数据返回不成功进入catch返回
    }
    return config
})

// 请求拦截
http.interceptors.request.use((config) => { // 可使用async await 做异步操作
    // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
    config.data = config.data || {}
    // 根据custom参数中配置的是否需要token，添加对应的请求头
	config.header['Token'] = storagePiniaStore.storageDta.pinia_user.token ? storagePiniaStore.storageDta.pinia_user.token : ''

    if (config?.custom?.load) {
        //打开加载动画
		loadingStore.loadingShow = true
    }

    return config 
}, config => { // 可使用async await 做异步操作
    return Promise.reject(config)
})

// 响应拦截
http.interceptors.response.use((response) => { /* 对响应成功做点什么 可使用async await 做异步操作*/

    // 关闭加载动画
	loadingStore.loadingShow = false
	
    const data = response.data
    // 自定义参数
    const custom = response.config?.custom
    // code:  0、请求成功 7、被迫下线重新登录
	
    if(data.code == 0){
        if(!custom.isFactory){
            return data
        }else{
            return data.result
        }
    }else if(data.code == 1003) {
		// 清空登录信息
		// storagePiniaStore.savePiniaData("pinia_user", {})
		
		// uni.reLaunch({
		//     url: '/subPackages/login/index'
		// });
	}else{
		// 抛出下拉刷新组件异常
		uni.$emit('z-paging-error-emit')
        // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
        if (custom.toast !== false) {
			uni.showToast({
				title: data.msg || data.message || '系统异常',
				icon: 'none'
			});
        }
        // 如果需要catch返回，则进行reject
        if (custom?.catch) {
            return Promise.reject(data)
        } else {
            // 否则返回一个pending中的promise，请求不会进入catch中
            return new Promise(() => { })
        }
    }
}, (response) => {
	 const data = response.data
    // 关闭加载动画
	loadingStore.loadingShow = false
	// 抛出下拉刷新组件异常
	uni.$emit('z-paging-error-emit')
	uni.showToast({
		title: data.msg || data.message || '系统异常',
		icon: 'none'
	});
	
    // 对响应错误做点什么 （statusCode !== 200）
	if(response.statusCode == 1003) {
		// 清空登录信息
		storagePiniaStore.savePiniaData("pinia_user", {})
		
		uni.reLaunch({
		    url: '/subPackages/login/index'
		});
		return new Promise(() => { })
	}
    return Promise.reject(response)
})

export {
  http
}