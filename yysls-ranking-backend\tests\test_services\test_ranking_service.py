"""
RankingService单元测试

测试榜单服务的所有功能
"""
import pytest
import pytest_asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.ranking_service import RankingService
from app.models.ranking import Ranking, RankingDetail, RankingType, RankingStatus
from app.schemas.ranking import RankingCreate, RankingUpdate


@pytest.mark.unit
class TestRankingService:
    """榜单服务测试类"""
    
    @pytest_asyncio.fixture
    async def ranking_service(self):
        """榜单服务实例"""
        return RankingService()
    
    @pytest.mark.asyncio
    async def test_create_ranking(self, db_session: AsyncSession, ranking_service: RankingService, test_data_factory):
        """测试创建榜单"""
        # 准备测试数据
        ranking_data = test_data_factory.ranking_data(
            title="新测试榜单",
            description="这是一个新的测试榜单",
            ranking_type=RankingType.TEN_PERSON,
            max_participants=10
        )
        
        # 创建榜单
        ranking = await ranking_service.create(db_session, obj_in=ranking_data)
        
        # 验证结果
        assert ranking.id is not None
        assert ranking.title == "新测试榜单"
        assert ranking.description == "这是一个新的测试榜单"
        assert ranking.ranking_type == RankingType.TEN_PERSON
        assert ranking.status == RankingStatus.DRAFT
        assert ranking.max_participants == 10
        assert ranking.current_participants == 0
    
    @pytest.mark.asyncio
    async def test_get_ranking_by_id(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试根据ID获取榜单"""
        # 获取榜单
        ranking = await ranking_service.get(db_session, test_ranking.id)
        
        # 验证结果
        assert ranking is not None
        assert ranking.id == test_ranking.id
        assert ranking.title == test_ranking.title
        assert ranking.ranking_type == test_ranking.ranking_type
    
    @pytest.mark.asyncio
    async def test_update_ranking(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试更新榜单"""
        # 准备更新数据
        update_data = RankingUpdate(
            title="更新后的榜单标题",
            description="更新后的榜单描述",
            max_participants=8
        )
        
        # 更新榜单
        updated_ranking = await ranking_service.update(db_session, db_obj=test_ranking, obj_in=update_data)
        
        # 验证结果
        assert updated_ranking.id == test_ranking.id
        assert updated_ranking.title == "更新后的榜单标题"
        assert updated_ranking.description == "更新后的榜单描述"
        assert updated_ranking.max_participants == 8
        assert updated_ranking.ranking_type == test_ranking.ranking_type  # 类型不变
    
    @pytest.mark.asyncio
    async def test_update_ranking_status(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试更新榜单状态"""
        # 更新状态为进行中
        updated_ranking = await ranking_service.update_status(db_session, test_ranking.id, RankingStatus.IN_PROGRESS)
        
        # 验证结果
        assert updated_ranking.status == RankingStatus.IN_PROGRESS
        assert updated_ranking.start_time is not None  # 应该设置开始时间
        
        # 更新状态为已结束
        finished_ranking = await ranking_service.update_status(db_session, test_ranking.id, RankingStatus.FINISHED)
        
        # 验证结果
        assert finished_ranking.status == RankingStatus.FINISHED
        assert finished_ranking.end_time is not None  # 应该设置结束时间
    
    @pytest.mark.asyncio
    async def test_get_rankings_by_type(self, db_session: AsyncSession, ranking_service: RankingService, test_data_factory):
        """测试根据类型获取榜单"""
        # 创建不同类型的榜单
        five_person_data = test_data_factory.ranking_data(
            title="5人榜单",
            ranking_type=RankingType.FIVE_PERSON
        )
        ten_person_data = test_data_factory.ranking_data(
            title="10人榜单",
            ranking_type=RankingType.TEN_PERSON
        )
        
        await ranking_service.create(db_session, obj_in=five_person_data)
        await ranking_service.create(db_session, obj_in=ten_person_data)
        
        # 获取5人榜单
        five_person_rankings = await ranking_service.get_rankings_by_type(db_session, RankingType.FIVE_PERSON)
        
        # 验证结果
        assert len(five_person_rankings) >= 1
        assert all(ranking.ranking_type == RankingType.FIVE_PERSON for ranking in five_person_rankings)
    
    @pytest.mark.asyncio
    async def test_get_active_rankings(self, db_session: AsyncSession, ranking_service: RankingService, test_data_factory):
        """测试获取活跃榜单"""
        # 创建不同状态的榜单
        active_data = test_data_factory.ranking_data(
            title="活跃榜单",
            status=RankingStatus.IN_PROGRESS
        )
        draft_data = test_data_factory.ranking_data(
            title="草稿榜单",
            status=RankingStatus.DRAFT
        )
        
        await ranking_service.create(db_session, obj_in=active_data)
        await ranking_service.create(db_session, obj_in=draft_data)
        
        # 获取活跃榜单
        active_rankings = await ranking_service.get_active_rankings(db_session)
        
        # 验证结果
        assert len(active_rankings) >= 1
        assert all(ranking.status == RankingStatus.IN_PROGRESS for ranking in active_rankings)
    
    @pytest.mark.asyncio
    async def test_add_ranking_detail(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试添加榜单明细"""
        # 准备明细数据
        detail_data = {
            "participant_name": "测试参与者",
            "completion_time": "02:30",  # 2分30秒
            "rank_range": "1-5",
            "notes": "测试备注"
        }
        
        # 添加榜单明细
        detail = await ranking_service.add_ranking_detail(db_session, test_ranking.id, detail_data)
        
        # 验证结果
        assert detail.id is not None
        assert detail.ranking_id == test_ranking.id
        assert detail.participant_name == "测试参与者"
        assert detail.completion_seconds == 150  # 2分30秒 = 150秒
        assert detail.rank_range == "1-5"
        assert detail.notes == "测试备注"
    
    @pytest.mark.asyncio
    async def test_get_ranking_details(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试获取榜单明细"""
        # 添加多个明细
        detail_data_list = [
            {
                "participant_name": "参与者1",
                "completion_seconds": 120,
                "rank_range": "1-3"
            },
            {
                "participant_name": "参与者2", 
                "completion_seconds": 180,
                "rank_range": "4-5"
            }
        ]
        
        for detail_data in detail_data_list:
            await ranking_service.add_ranking_detail(db_session, test_ranking.id, detail_data)
        
        # 获取榜单明细
        details = await ranking_service.get_ranking_details(db_session, test_ranking.id)
        
        # 验证结果
        assert len(details) >= 2
        assert details[0].completion_seconds <= details[1].completion_seconds  # 按时间排序
    
    @pytest.mark.asyncio
    async def test_get_ranking_statistics(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试获取榜单统计"""
        # 添加一些明细数据
        detail_data_list = [
            {"participant_name": "参与者1", "completion_seconds": 120},
            {"participant_name": "参与者2", "completion_seconds": 180},
            {"participant_name": "参与者3", "completion_seconds": 150}
        ]
        
        for detail_data in detail_data_list:
            await ranking_service.add_ranking_detail(db_session, test_ranking.id, detail_data)
        
        # 获取统计信息
        stats = await ranking_service.get_ranking_statistics(db_session, test_ranking.id)
        
        # 验证结果
        assert stats["total_participants"] == 3
        assert stats["best_time"] == 120
        assert stats["average_time"] == 150.0
        assert stats["worst_time"] == 180
    
    @pytest.mark.asyncio
    async def test_get_multi_with_total(self, db_session: AsyncSession, ranking_service: RankingService, test_data_factory):
        """测试分页获取榜单"""
        # 创建多个测试榜单
        for i in range(5):
            ranking_data = test_data_factory.ranking_data(
                title=f"测试榜单{i}",
                description=f"这是第{i}个测试榜单"
            )
            await ranking_service.create(db_session, obj_in=ranking_data)
        
        # 分页获取榜单
        rankings, total = await ranking_service.get_multi_with_total(db_session, skip=0, limit=3)
        
        # 验证结果
        assert total >= 5
        assert len(rankings) == 3
    
    @pytest.mark.asyncio
    async def test_delete_ranking(self, db_session: AsyncSession, ranking_service: RankingService, test_ranking: Ranking):
        """测试删除榜单"""
        ranking_id = test_ranking.id
        
        # 删除榜单
        deleted_ranking = await ranking_service.remove(db_session, id=ranking_id)
        
        # 验证结果
        assert deleted_ranking.id == ranking_id
        
        # 确认榜单已被删除
        ranking = await ranking_service.get(db_session, ranking_id)
        assert ranking is None
