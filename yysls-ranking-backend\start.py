#!/usr/bin/env python3
"""
应用启动脚本 - 用于Docker容器
"""
import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 确保app目录在Python路径中
app_dir = os.path.join(current_dir, 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# 设置环境变量
os.environ.setdefault('PYTHONPATH', f"{current_dir}:{app_dir}")

if __name__ == "__main__":
    import uvicorn

    # 启动应用
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        workers=1,  # 减少worker数量避免日志配置冲突
        log_level="info"
    )
