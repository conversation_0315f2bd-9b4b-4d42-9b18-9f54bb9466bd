"""
安全相关工具函数

包括JWT token生成验证、密码加密验证等功能
"""
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status

from app.config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    创建JWT访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
    
    Returns:
        JWT令牌字符串
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.jwt_secret_key, 
        algorithm=settings.jwt_algorithm
    )
    
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌字符串
    
    Returns:
        解码后的payload数据
    
    Raises:
        HTTPException: 令牌无效时抛出异常
    """
    try:
        payload = jwt.decode(
            token, 
            settings.jwt_secret_key, 
            algorithms=[settings.jwt_algorithm]
        )
        
        # 检查令牌是否过期
        exp = payload.get("exp")
        if exp is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌格式无效"
            )
        
        if datetime.utcnow() > datetime.fromtimestamp(exp):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期"
            )
        
        return payload
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌验证失败"
        )


def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    解码JWT令牌（不验证过期时间）
    
    Args:
        token: JWT令牌字符串
    
    Returns:
        解码后的payload数据，失败时返回None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.jwt_secret_key, 
            algorithms=[settings.jwt_algorithm],
            options={"verify_exp": False}
        )
        return payload
    except JWTError:
        return None


def generate_reset_token(user_id: int) -> str:
    """
    生成密码重置令牌
    
    Args:
        user_id: 用户ID
    
    Returns:
        重置令牌字符串
    """
    data = {
        "sub": str(user_id),
        "type": "password_reset",
        "exp": datetime.utcnow() + timedelta(hours=1)  # 1小时有效期
    }
    
    return jwt.encode(
        data,
        settings.jwt_secret_key,
        algorithm=settings.jwt_algorithm
    )


def verify_reset_token(token: str) -> Optional[int]:
    """
    验证密码重置令牌
    
    Args:
        token: 重置令牌字符串
    
    Returns:
        用户ID，验证失败时返回None
    """
    try:
        payload = jwt.decode(
            token,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "password_reset":
            return None
        
        user_id = payload.get("sub")
        if user_id is None:
            return None
        
        return int(user_id)
        
    except (JWTError, ValueError):
        return None


def create_api_key(user_id: int, name: str) -> str:
    """
    创建API密钥
    
    Args:
        user_id: 用户ID
        name: API密钥名称
    
    Returns:
        API密钥字符串
    """
    data = {
        "sub": str(user_id),
        "type": "api_key",
        "name": name,
        "iat": datetime.utcnow()
    }
    
    return jwt.encode(
        data,
        settings.jwt_secret_key,
        algorithm=settings.jwt_algorithm
    )


def verify_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """
    验证API密钥
    
    Args:
        api_key: API密钥字符串
    
    Returns:
        解码后的数据，验证失败时返回None
    """
    try:
        payload = jwt.decode(
            api_key,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
            options={"verify_exp": False}  # API密钥不设置过期时间
        )
        
        # 检查令牌类型
        if payload.get("type") != "api_key":
            return None
        
        return payload
        
    except JWTError:
        return None
