#!/usr/bin/env python3
"""
测试API端点
"""
import asyncio
import httpx
import json


async def test_miniprogram_endpoint():
    """测试小程序登录端点"""
    print("=== 测试小程序登录API端点 ===")
    
    # 测试数据
    login_data = {
        "code": "test_js_code_from_miniprogram"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/auth/wechat-miniprogram-login",
                json=login_data,
                timeout=10.0
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容:")
            
            try:
                result = response.json()
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except:
                print(response.text)
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")


async def test_old_endpoint():
    """测试旧的微信登录端点"""
    print("\n=== 测试旧的微信登录API端点 ===")
    
    # 测试数据
    login_data = {
        "code": "test_js_code_from_miniprogram"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/auth/wechat-login",
                json=login_data,
                timeout=10.0
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容:")
            
            try:
                result = response.json()
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except:
                print(response.text)
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")


def print_solution():
    """打印解决方案"""
    print("\n=== 解决方案 ===")
    print("根据错误信息 'invalid oauth code, it is miniprogram jscode, please use jscode2session'")
    print("说明前端传入的是小程序jscode，但后端使用了OAuth2方式处理。")
    print()
    print("解决方法：")
    print("1. 前端应该调用新的小程序登录接口:")
    print("   POST /api/v1/auth/wechat-miniprogram-login")
    print("   而不是旧的: POST /api/v1/auth/wechat-login")
    print()
    print("2. 或者修改后端，让 /wechat-login 接口自动检测code类型:")
    print("   - 如果是小程序jscode，使用jscode2session")
    print("   - 如果是OAuth2 code，使用OAuth2流程")
    print()
    print("3. 检查前端代码中的API调用，确保使用正确的接口")


async def main():
    """主函数"""
    print("API端点测试")
    print("=" * 50)
    
    # 测试新的小程序登录端点
    await test_miniprogram_endpoint()
    
    # 测试旧的微信登录端点
    await test_old_endpoint()
    
    # 打印解决方案
    print_solution()


if __name__ == "__main__":
    asyncio.run(main())
