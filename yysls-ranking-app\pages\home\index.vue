<template>
	<view>
		<!-- 公共模块 -->
		<publicModule></publicModule>
		<!-- 主页 引入所有模块 -->

		<!-- 榜单 -->
		<list v-if="currentTab === 0"></list>
		<!-- 我的 -->
		<user v-if="currentTab === 1"></user>

		<!-- 自定义tabbar -->
		<floatingTabbar :current="currentTab" @change="handleTabChange"></floatingTabbar>
	</view>
</template>

<script setup>
	import { ref } from "vue"
	import list from "./components/list.vue"
	import user from "./components/user.vue"
	import floatingTabbar from "@/components/common/floating-tabbar.vue"
	// 隐藏tabbar
	uni.hideTabBar()

	// 当前选中的tab
	const currentTab = ref(0)

	// 处理tab切换
	const handleTabChange = ({ index }) => {
		currentTab.value = index
	}
</script>