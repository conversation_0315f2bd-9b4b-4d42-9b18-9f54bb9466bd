import { ref } from "vue"

// 获取当前页面路径
const getCurrentPageUrl = () => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	return {
		route: currentPage.route,
		options: currentPage.options || {}
	}
}

// 获取当前页面的参数
const getCurrentPageOptions = () => {
	let options = {}
	// 标记是分享进入
	options.isShare = true
	// 获取本地存储的参数
	try {
		const storageOptions = uni.getStorageSync('lifeData').pinia_user
		// 分享人ID
		if (storageOptions.memberId) {
			options.memberId = storageOptions.memberId
		}
	} catch (e) {}

	return options
}

// 统一分享
export const share = {	
	// 分享到好友
	onShareAppMessage() {
		
		const options = getCurrentPageOptions()
		const query = Object.entries(options)
			.map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
			.join('&')

		console.log('Query:', query) // 调试信息
		// 默认配置
		let shareInfo = {
			title: 'DNF公会助手', // 分享标题
			path: `/pages/team/index??${query}`, // 分享路径
			imageUrl: '/subPackages/login/static/logo-share.png', // 分享图片
		}
		
		return shareInfo
	}
}
