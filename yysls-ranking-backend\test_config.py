#!/usr/bin/env python3
"""
测试配置加载
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置加载"""
    print("🔧 测试配置加载...")
    print("=" * 50)
    
    # 显示环境变量
    print("环境变量:")
    env_vars = [
        'DATABASE_URL', 'DATABASE_URL_ASYNC', 'SECRET_KEY', 
        'JWT_SECRET_KEY', 'WECHAT_APP_ID', 'WECHAT_APP_SECRET'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'NOT_SET')
        # 隐藏敏感信息
        if 'SECRET' in var or 'PASSWORD' in var:
            display_value = value[:10] + '...' if len(value) > 10 else value
        else:
            display_value = value
        print(f"  {var}: {display_value}")
    
    print("\n" + "=" * 50)
    
    try:
        # 尝试导入配置
        print("正在导入配置模块...")
        from app.config import Settings
        print("✅ 配置模块导入成功")
        
        # 尝试创建配置实例
        print("正在创建配置实例...")
        settings = Settings()
        print("✅ 配置实例创建成功")
        
        # 显示配置信息
        print("\n配置信息:")
        print(f"  应用名称: {settings.app_name}")
        print(f"  应用版本: {settings.app_version}")
        print(f"  调试模式: {settings.debug}")
        print(f"  数据库URL: {settings.database_url[:50]}...")
        print(f"  JWT算法: {settings.jwt_algorithm}")
        print(f"  微信AppID: {settings.wechat_app_id}")
        
        print("\n🎉 配置加载测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 显示详细错误信息
        import traceback
        print("\n详细错误信息:")
        traceback.print_exc()
        
        return False

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1)
