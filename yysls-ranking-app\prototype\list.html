<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: url('../static/home/<USER>') no-repeat center top fixed;
            background-size: cover;
            color: #F5F5DC;
            min-height: 100vh;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .scroll-text {
            animation: scroll-left 15s linear infinite;
            white-space: nowrap;
        }
        
        @keyframes scroll-left {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .rank-item {
            transition: all 0.3s ease;
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.1);
        }
        
        .rank-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(212, 175, 55, 0.2);
            border-color: rgba(212, 175, 55, 0.3);
        }
        
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .sponsor-item {
            background: rgba(245, 245, 220, 0.1);
            border: 1px solid rgba(245, 245, 220, 0.2);
            transition: all 0.3s ease;
        }
        
        .sponsor-item:hover {
            background: rgba(212, 175, 55, 0.1);
            border-color: rgba(212, 175, 55, 0.3);
        }
        
        .ad-banner {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.05) 100%);
            border: 2px dashed rgba(212, 175, 55, 0.3);
        }

        /* 滚动入场动画样式 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* 为不同模块添加延迟效果 */
        .scroll-animate:nth-child(1) { transition-delay: 0ms; }
        .scroll-animate:nth-child(2) { transition-delay: 100ms; }
        .scroll-animate:nth-child(3) { transition-delay: 200ms; }
        .scroll-animate:nth-child(4) { transition-delay: 300ms; }
        .scroll-animate:nth-child(5) { transition-delay: 400ms; }
        .scroll-animate:nth-child(6) { transition-delay: 500ms; }

        /* 榜单项目的特殊动画 */
        .rank-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .rank-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* 为榜单项目添加递增延迟 */
        .rank-animate:nth-child(1) { transition-delay: 0ms; }
        .rank-animate:nth-child(2) { transition-delay: 150ms; }
        .rank-animate:nth-child(3) { transition-delay: 300ms; }
        .rank-animate:nth-child(4) { transition-delay: 450ms; }
        .rank-animate:nth-child(5) { transition-delay: 600ms; }

        /* 赞助商网格动画 */
        .sponsor-animate {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .sponsor-animate.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .sponsor-animate:nth-child(1) { transition-delay: 0ms; }
        .sponsor-animate:nth-child(2) { transition-delay: 100ms; }
        .sponsor-animate:nth-child(3) { transition-delay: 200ms; }
        .sponsor-animate:nth-child(4) { transition-delay: 300ms; }

        /* 浮动标签栏 iframe 样式 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            border: none;
            z-index: 9999;
            pointer-events: none;
            background: transparent;
        }

        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }

        /* 为页面内容添加底部间距，避免被浮动标签栏遮挡 */
        .main-content {
            padding-bottom: 120px;
        }

        /* Tab切换样式 */
        .tab-container {
            display: flex;
            background: rgba(74, 74, 74, 0.3);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
            border: 1px solid rgba(212, 175, 55, 0.1);
        }

        .tab-item {
            flex: 1;
            padding: 10px 16px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .tab-item:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
            transition: left 0.5s;
        }

        .tab-item:hover:before {
            left: 100%;
        }

        .tab-item.inactive {
            color: #9CA3AF;
            background: transparent;
        }

        .tab-item.active {
            color: #D4AF37;
            background: rgba(212, 175, 55, 0.15);
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.2);
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.3);
        }

        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #D4AF37, transparent);
            animation: glow-line 2s ease-in-out infinite alternate;
        }

        @keyframes glow-line {
            0% { box-shadow: 0 0 5px rgba(212, 175, 55, 0.5); }
            100% { box-shadow: 0 0 15px rgba(212, 175, 55, 0.8); }
        }

        /* 榜单内容切换动画 */
        .ranking-content {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-content.fade-out {
            opacity: 0;
            transform: translateY(20px);
        }

        .ranking-content.fade-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* 标题切换动画 */
        .ranking-title {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
        }

        .ranking-title.title-fade-out {
            opacity: 0;
            transform: translateY(-10px);
        }

        .ranking-title.title-fade-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* 榜单表头样式 */
        .ranking-header {
            display: flex;
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            font-weight: 600;
            color: #D4AF37;
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.3);
        }

        .ranking-header .header-rank {
            width: 80px;
            text-align: center;
        }

        .ranking-header .header-time {
            flex: 1;
            text-align: center;
        }

        .ranking-header .header-count {
            width: 80px;
            text-align: center;
        }

        /* 新的榜单项目样式 */
        .ranking-item {
            display: flex;
            align-items: center;
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .ranking-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(212, 175, 55, 0.2);
            border-color: rgba(212, 175, 55, 0.3);
            background: rgba(74, 74, 74, 0.4);
        }

        .ranking-item .rank-number {
            width: 80px;
            text-align: center;
            font-weight: bold;
            color: #FFFFFF;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ranking-item .rank-time {
            flex: 1;
            text-align: center;
            color: #F5F5DC;
            font-weight: 500;
        }

        .ranking-item .rank-count {
            width: 80px;
            text-align: center;
            color: #D4AF37;
            font-weight: 600;
        }

        /* 皇冠图标样式 */
        .crown-icon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            transform: rotate(-18deg);
            display: inline-block;
        }

        /* 第1名皇冠 - 金色 */
        .ranking-item.rank-1 .crown-icon {
            filter: brightness(0) saturate(100%) invert(84%) sepia(100%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);
        }

        /* 第2名皇冠 - 银色 */
        .ranking-item.rank-2 .crown-icon {
            filter: brightness(0) saturate(100%) invert(75%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
        }

        /* 第3名皇冠 - 铜色 */
        .ranking-item.rank-3 .crown-icon {
            filter: brightness(0) saturate(100%) invert(55%) sepia(85%) saturate(1567%) hue-rotate(15deg) brightness(90%) contrast(85%);
        }

        /* 前三名特殊样式 - 增强版 */
        .ranking-item.rank-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.35) 0%, rgba(255, 215, 0, 0.15) 100%);
            border: 3px solid rgba(255, 215, 0, 0.8);
            box-shadow:
                0 0 30px rgba(255, 215, 0, 0.6),
                0 0 60px rgba(255, 215, 0, 0.3),
                inset 0 2px 0 rgba(255, 215, 0, 0.4),
                inset 0 -2px 0 rgba(255, 215, 0, 0.2);
        }

        .ranking-item.rank-1 .rank-number {
            color: #FFD700;
            text-shadow:
                0 0 20px rgba(255, 215, 0, 1),
                0 0 40px rgba(255, 215, 0, 0.6),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            font-size: 18px;
            font-weight: 900;
        }

        .ranking-item.rank-1:hover {
            box-shadow:
                0 0 40px rgba(255, 215, 0, 0.8),
                0 0 80px rgba(255, 215, 0, 0.4),
                inset 0 2px 0 rgba(255, 215, 0, 0.5);
            transform: translateY(-4px);
        }

        .ranking-item.rank-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.3) 0%, rgba(192, 192, 192, 0.12) 100%);
            border: 3px solid rgba(192, 192, 192, 0.7);
            box-shadow:
                0 0 25px rgba(192, 192, 192, 0.5),
                0 0 50px rgba(192, 192, 192, 0.25),
                inset 0 2px 0 rgba(192, 192, 192, 0.35),
                inset 0 -2px 0 rgba(192, 192, 192, 0.15);
        }

        .ranking-item.rank-2 .rank-number {
            color: #C0C0C0;
            text-shadow:
                0 0 15px rgba(192, 192, 192, 0.9),
                0 0 30px rgba(192, 192, 192, 0.5),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            font-size: 17px;
            font-weight: 800;
        }

        .ranking-item.rank-2:hover {
            box-shadow:
                0 0 35px rgba(192, 192, 192, 0.7),
                0 0 70px rgba(192, 192, 192, 0.35),
                inset 0 2px 0 rgba(192, 192, 192, 0.4);
            transform: translateY(-4px);
        }

        .ranking-item.rank-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.3) 0%, rgba(205, 127, 50, 0.12) 100%);
            border: 3px solid rgba(205, 127, 50, 0.7);
            box-shadow:
                0 0 25px rgba(205, 127, 50, 0.5),
                0 0 50px rgba(205, 127, 50, 0.25),
                inset 0 2px 0 rgba(205, 127, 50, 0.35),
                inset 0 -2px 0 rgba(205, 127, 50, 0.15);
        }

        .ranking-item.rank-3 .rank-number {
            color: #CD7F32;
            text-shadow:
                0 0 15px rgba(205, 127, 50, 0.9),
                0 0 30px rgba(205, 127, 50, 0.5),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            font-size: 17px;
            font-weight: 800;
        }

        .ranking-item.rank-3:hover {
            box-shadow:
                0 0 35px rgba(205, 127, 50, 0.7),
                0 0 70px rgba(205, 127, 50, 0.35),
                inset 0 2px 0 rgba(205, 127, 50, 0.4);
            transform: translateY(-4px);
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <div class="min-h-screen p-4 space-y-6 main-content">
        
        <!-- 播报信息区域 -->
        <div class="ink-card p-3 overflow-hidden">
            <div class="flex items-center space-x-2 mb-2">
                <img src="https://unpkg.com/lucide-static@latest/icons/volume-2.svg" alt="播报" class="w-4 h-4 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <span class="text-sm gold-text font-medium">最新播报</span>
            </div>
            <div class="relative h-6 overflow-hidden">
                <div class="scroll-text text-sm text-gray-300">
                    🎉 恭喜"剑客无名"荣登本周武功榜首位！ | 📢 新一期"江湖风云榜"即将发布 | ⚔️ 武林大会报名火热进行中
                </div>
            </div>
        </div>

        <!-- 榜单名称区域 -->
        <div class="text-center py-6">
            <div class="floating-element">
                <h1 class="text-3xl font-bold mb-2" style="color: #D4AF37; text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 2px 2px 4px rgba(0, 0, 0, 0.9), 0 0 16px rgba(212, 175, 55, 0.6); letter-spacing: 0.5px;">江湖风云榜</h1>
                <div class="flex items-center justify-center space-x-3">
                    <img src="https://unpkg.com/lucide-static@latest/icons/sword.svg" alt="剑" class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%) drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 4px rgba(212, 175, 55, 0.4));">
                    <span class="text-xl font-bold" style="color: #D4AF37; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 1px 1px 2px rgba(0, 0, 0, 0.9), 0 0 12px rgba(212, 175, 55, 0.5); letter-spacing: 0.5px;">第2024期</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/sword.svg" alt="剑" class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%) drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 4px rgba(212, 175, 55, 0.4));">
                </div>
            </div>
        </div>

        <!-- 竞速榜单区域 -->
        <div class="ink-card p-4 scroll-animate">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/trophy.svg" alt="奖杯" class="w-5 h-5 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h2 id="ranking-title" class="text-xl font-semibold gold-text ranking-title">5人竞速榜单</h2>
            </div>

            <!-- Tab切换区域 -->
            <div class="tab-container">
                <div class="tab-item active" data-tab="5-person" onclick="switchTab('5-person')">
                    <span>5人榜单</span>
                </div>
                <div class="tab-item inactive" data-tab="10-person" onclick="switchTab('10-person')">
                    <span>10人榜单</span>
                </div>
            </div>

            <!-- 5人榜单内容 -->
            <div id="ranking-5-person" class="ranking-content">
                <!-- 表头 -->
                <div class="ranking-header">
                    <div class="header-rank">排名</div>
                    <div class="header-time">时间</div>
                    <div class="header-count">人数</div>
                </div>

                <!-- 榜单数据 -->
                <div class="ranking-item rank-1 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>1</span>
                    </div>
                    <div class="rank-time">1分35秒</div>
                    <div class="rank-count">5</div>
                </div>

                <div class="ranking-item rank-2 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>2</span>
                    </div>
                    <div class="rank-time">1分48秒</div>
                    <div class="rank-count">5</div>
                </div>

                <div class="ranking-item rank-3 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>3</span>
                    </div>
                    <div class="rank-time">2分02秒</div>
                    <div class="rank-count">5</div>
                </div>

                <div class="ranking-item rank-animate">
                    <div class="rank-number">4</div>
                    <div class="rank-time">2分15秒</div>
                    <div class="rank-count">5</div>
                </div>

                <div class="ranking-item rank-animate">
                    <div class="rank-number">5</div>
                    <div class="rank-time">2分28秒</div>
                    <div class="rank-count">5</div>
                </div>
            </div>

            <!-- 10人榜单内容 -->
            <div id="ranking-10-person" class="ranking-content" style="display: none;">
                <!-- 表头 -->
                <div class="ranking-header">
                    <div class="header-rank">排名</div>
                    <div class="header-time">时间</div>
                    <div class="header-count">人数</div>
                </div>

                <!-- 榜单数据 -->
                <div class="ranking-item rank-1 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>1</span>
                    </div>
                    <div class="rank-time">3分12秒</div>
                    <div class="rank-count">10</div>
                </div>

                <div class="ranking-item rank-2 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>2</span>
                    </div>
                    <div class="rank-time">3分25秒</div>
                    <div class="rank-count">10</div>
                </div>

                <div class="ranking-item rank-3 rank-animate">
                    <div class="rank-number">
                        <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="皇冠" class="crown-icon">
                        <span>3</span>
                    </div>
                    <div class="rank-time">3分38秒</div>
                    <div class="rank-count">10</div>
                </div>

                <div class="ranking-item rank-animate">
                    <div class="rank-number">4</div>
                    <div class="rank-time">3分52秒</div>
                    <div class="rank-count">10</div>
                </div>

                <div class="ranking-item rank-animate">
                    <div class="rank-number">5</div>
                    <div class="rank-time">4分06秒</div>
                    <div class="rank-count">10</div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button class="px-6 py-2 bg-gradient-to-r from-yellow-600 to-yellow-700 text-black font-medium rounded-lg hover:from-yellow-500 hover:to-yellow-600 transition-all">
                    查看完整榜单
                </button>
            </div>
        </div>

        <!-- 榜单时间信息 -->
        <div class="ink-card p-4 scroll-animate">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" alt="时钟" class="w-4 h-4 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <span class="text-sm gold-text">更新时间</span>
                </div>
                <span class="text-sm text-gray-300">2024年1月15日 18:00</span>
            </div>
            <div class="flex items-center justify-between mt-2">
                <div class="flex items-center space-x-2">
                    <img src="https://unpkg.com/lucide-static@latest/icons/calendar.svg" alt="日历" class="w-4 h-4 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <span class="text-sm gold-text">统计周期</span>
                </div>
                <span class="text-sm text-gray-300">2024.01.08 - 2024.01.14</span>
            </div>
        </div>

        <!-- 赞助商列表 -->
        <div class="ink-card p-4 scroll-animate">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/handshake.svg" alt="握手" class="w-5 h-5 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h3 class="text-lg font-semibold gold-text">合作伙伴</h3>
            </div>

            <div class="grid grid-cols-2 gap-3">
                <div class="sponsor-item p-3 rounded-lg text-center sponsor-animate">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=60&h=40&fit=crop" alt="赞助商1" class="w-full h-10 object-cover rounded mb-2">
                    <span class="text-xs text-gray-300">武林盟主</span>
                </div>
                <div class="sponsor-item p-3 rounded-lg text-center sponsor-animate">
                    <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=60&h=40&fit=crop" alt="赞助商2" class="w-full h-10 object-cover rounded mb-2">
                    <span class="text-xs text-gray-300">江湖客栈</span>
                </div>
                <div class="sponsor-item p-3 rounded-lg text-center sponsor-animate">
                    <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=60&h=40&fit=crop" alt="赞助商3" class="w-full h-10 object-cover rounded mb-2">
                    <span class="text-xs text-gray-300">神兵阁</span>
                </div>
                <div class="sponsor-item p-3 rounded-lg text-center sponsor-animate">
                    <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=60&h=40&fit=crop" alt="赞助商4" class="w-full h-10 object-cover rounded mb-2">
                    <span class="text-xs text-gray-300">丹药坊</span>
                </div>
            </div>
        </div>

        <!-- 广告招租区域 -->
        <div class="ad-banner p-6 rounded-lg text-center scroll-animate">
            <img src="https://unpkg.com/lucide-static@latest/icons/megaphone.svg" alt="喇叭" class="w-8 h-8 mx-auto mb-3 filter brightness-0 invert" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
            <h4 class="text-lg font-semibold gold-text mb-2">广告招租</h4>
            <p class="text-sm text-gray-300 mb-4">在江湖中展示您的品牌<br>让更多侠客了解您的产品</p>
            <button class="px-6 py-2 bg-gradient-to-r from-yellow-600 to-yellow-700 text-black font-medium rounded-lg hover:from-yellow-500 hover:to-yellow-600 transition-all">
                联系我们
            </button>
        </div>

        <!-- 底部间距 -->
        <div class="h-8"></div>
    </div>

    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                style="background: transparent;">
        </iframe>
    </div>

    <script>
        // 滚动入场动画控制器
        class ScrollAnimationController {
            constructor() {
                this.observerOptions = {
                    root: null,
                    rootMargin: '50px 0px -50px 0px', // 上方50px，下方50px的边距
                    threshold: [0, 0.1, 0.5] // 多个阈值，更精确地检测进入和离开
                };

                // 存储动画定时器，用于清理
                this.animationTimers = new Map();

                this.init();
            }

            init() {
                // 创建观察器
                this.observer = new IntersectionObserver(
                    this.handleIntersection.bind(this),
                    this.observerOptions
                );

                // 观察所有需要动画的元素
                this.observeElements();

                // 为已经在视口中的元素立即添加动画
                this.checkInitialVisibility();
            }

            handleIntersection(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 元素进入视口，添加动画类
                        entry.target.classList.add('animate-in');

                        // 如果是赞助商容器，为其子元素添加动画
                        if (entry.target.querySelector('.sponsor-animate')) {
                            this.animateSponsorItems(entry.target);
                        }

                        // 如果是榜单容器，为其子元素添加动画
                        if (entry.target.querySelector('.rank-animate')) {
                            this.animateRankItems(entry.target);
                        }
                    } else {
                        // 元素离开视口，移除动画类以便下次重新触发
                        entry.target.classList.remove('animate-in');

                        // 移除子元素的动画类
                        if (entry.target.querySelector('.sponsor-animate')) {
                            this.resetSponsorItems(entry.target);
                        }

                        if (entry.target.querySelector('.rank-animate')) {
                            this.resetRankItems(entry.target);
                        }
                    }
                });
            }

            animateSponsorItems(container) {
                // 清除之前的定时器
                this.clearContainerTimers(container);

                const sponsorItems = container.querySelectorAll('.sponsor-animate');
                const timers = [];

                sponsorItems.forEach((item, index) => {
                    const timer = setTimeout(() => {
                        item.classList.add('animate-in');
                    }, index * 100); // 每个项目延迟100ms
                    timers.push(timer);
                });

                // 存储定时器以便后续清理
                this.animationTimers.set(container, timers);
            }

            animateRankItems(container) {
                // 清除之前的定时器
                this.clearContainerTimers(container);

                const rankItems = container.querySelectorAll('.rank-animate');
                const timers = [];

                rankItems.forEach((item, index) => {
                    const timer = setTimeout(() => {
                        item.classList.add('animate-in');
                    }, index * 150); // 每个项目延迟150ms
                    timers.push(timer);
                });

                // 存储定时器以便后续清理
                this.animationTimers.set(container, timers);
            }

            resetSponsorItems(container) {
                // 清除定时器
                this.clearContainerTimers(container);

                const sponsorItems = container.querySelectorAll('.sponsor-animate');
                sponsorItems.forEach(item => {
                    item.classList.remove('animate-in');
                });
            }

            resetRankItems(container) {
                // 清除定时器
                this.clearContainerTimers(container);

                const rankItems = container.querySelectorAll('.rank-animate');
                rankItems.forEach(item => {
                    item.classList.remove('animate-in');
                });
            }

            clearContainerTimers(container) {
                if (this.animationTimers.has(container)) {
                    const timers = this.animationTimers.get(container);
                    timers.forEach(timer => clearTimeout(timer));
                    this.animationTimers.delete(container);
                }
            }

            observeElements() {
                // 观察主要模块
                const scrollElements = document.querySelectorAll('.scroll-animate');
                scrollElements.forEach(element => {
                    this.observer.observe(element);
                });
            }

            checkInitialVisibility() {
                // 检查页面加载时已经在视口中的元素
                const scrollElements = document.querySelectorAll('.scroll-animate');
                scrollElements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    const isVisible = rect.top < (window.innerHeight - 50) && rect.bottom > 50;

                    if (isVisible) {
                        element.classList.add('animate-in');

                        // 处理子元素动画
                        if (element.querySelector('.sponsor-animate')) {
                            this.animateSponsorItems(element);
                        }
                        if (element.querySelector('.rank-animate')) {
                            this.animateRankItems(element);
                        }
                    } else {
                        // 确保不可见的元素没有动画类
                        element.classList.remove('animate-in');

                        if (element.querySelector('.sponsor-animate')) {
                            this.resetSponsorItems(element);
                        }
                        if (element.querySelector('.rank-animate')) {
                            this.resetRankItems(element);
                        }
                    }
                });
            }
        }

        // 全局动画控制器实例
        let globalAnimationController = null;

        // Tab切换功能
        function switchTab(tabType) {
            // 获取所有tab元素
            const tabs = document.querySelectorAll('.tab-item');
            const fivePersonContent = document.getElementById('ranking-5-person');
            const tenPersonContent = document.getElementById('ranking-10-person');
            const titleElement = document.getElementById('ranking-title');

            // 移除所有tab的active状态
            tabs.forEach(tab => {
                tab.classList.remove('active');
                tab.classList.add('inactive');
            });

            // 激活当前选中的tab
            const activeTab = document.querySelector(`[data-tab="${tabType}"]`);
            if (activeTab) {
                activeTab.classList.remove('inactive');
                activeTab.classList.add('active');
            }

            // 更新标题
            updateTitle(titleElement, tabType);

            // 切换内容显示
            if (tabType === '5-person') {
                // 显示5人榜单，隐藏10人榜单
                switchContent(fivePersonContent, tenPersonContent);
            } else if (tabType === '10-person') {
                // 显示10人榜单，隐藏5人榜单
                switchContent(tenPersonContent, fivePersonContent);
            }
        }

        // 标题更新函数
        function updateTitle(titleElement, tabType) {
            // 先让标题淡出
            titleElement.classList.add('title-fade-out');

            setTimeout(() => {
                // 更新标题文本
                if (tabType === '5-person') {
                    titleElement.textContent = '5人竞速榜单';
                } else if (tabType === '10-person') {
                    titleElement.textContent = '10人竞速榜单';
                }

                // 强制重绘
                titleElement.offsetHeight;

                // 让标题淡入
                titleElement.classList.remove('title-fade-out');
                titleElement.classList.add('title-fade-in');

                // 清理动画类
                setTimeout(() => {
                    titleElement.classList.remove('title-fade-in');
                }, 300);

            }, 150); // 等待淡出动画完成一半时更新文本
        }

        // 内容切换动画函数
        function switchContent(showElement, hideElement) {
            // 先让当前显示的内容淡出
            hideElement.classList.add('fade-out');

            setTimeout(() => {
                // 隐藏淡出的内容
                hideElement.style.display = 'none';
                hideElement.classList.remove('fade-out');

                // 显示新内容
                showElement.style.display = 'block';
                showElement.classList.add('fade-out'); // 先设为淡出状态

                // 强制重绘
                showElement.offsetHeight;

                // 触发淡入动画
                showElement.classList.remove('fade-out');
                showElement.classList.add('fade-in');

                // 重新触发榜单项目动画
                setTimeout(() => {
                    if (globalAnimationController) {
                        const rankItems = showElement.querySelectorAll('.rank-animate');
                        rankItems.forEach((item, index) => {
                            item.classList.remove('animate-in');
                            setTimeout(() => {
                                item.classList.add('animate-in');
                            }, index * 100);
                        });
                    }
                }, 100);

            }, 200); // 等待淡出动画完成
        }

        // 页面加载完成后初始化动画控制器
        document.addEventListener('DOMContentLoaded', () => {
            // 检查浏览器是否支持 Intersection Observer
            if ('IntersectionObserver' in window) {
                globalAnimationController = new ScrollAnimationController();
            } else {
                // 降级处理：直接显示所有元素
                console.warn('浏览器不支持 Intersection Observer，使用降级方案');
                const allAnimateElements = document.querySelectorAll('.scroll-animate, .rank-animate, .sponsor-animate');
                allAnimateElements.forEach(element => {
                    element.classList.add('animate-in');
                });
            }
        });

        // 添加页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && globalAnimationController) {
                // 页面重新可见时，重新检查动画状态
                globalAnimationController.checkInitialVisibility();
            }
        });

        // 添加窗口大小变化监听，重新计算可见性
        window.addEventListener('resize', () => {
            if (globalAnimationController) {
                setTimeout(() => {
                    globalAnimationController.checkInitialVisibility();
                }, 100); // 延迟100ms等待布局稳定
            }
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 处理标签栏切换事件
            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                // 根据选中的标签执行相应操作
                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页/排行页');
                        // 滚动到榜单区域
                        const rankingSection = document.querySelector('.ink-card');
                        if (rankingSection) {
                            rankingSection.scrollIntoView({ behavior: 'smooth' });
                        } else {
                            // 如果没有找到榜单区域，滚动到顶部
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        }
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        alert('用户中心功能开发中...');
                        break;
                }
            }
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 确保消息来源安全
            if (event.origin !== window.location.origin) {
                return;
            }

            // 处理标签栏切换事件
            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                // 根据选中的标签执行相应操作
                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        // 当前就是首页，可以滚动到顶部
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        break;
                    case 'search':
                        console.log('切换到搜索页');
                        // 这里可以添加搜索页面的逻辑
                        break;
                    case 'ranking':
                        console.log('切换到排行页');
                        // 滚动到榜单区域
                        const rankingSection = document.querySelector('.ink-card');
                        if (rankingSection) {
                            rankingSection.scrollIntoView({ behavior: 'smooth' });
                        }
                        break;
                    case 'user':
                        console.log('切换到用户页');
                        // 这里可以添加用户页面的逻辑
                        break;
                }
            }
        });
    </script>
</body>
</html>
