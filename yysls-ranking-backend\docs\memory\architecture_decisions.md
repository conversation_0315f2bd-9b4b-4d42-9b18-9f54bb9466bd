# 架构决策记录 (Architecture Decision Records)

## ADR-001: 选择FastAPI作为Web框架

**状态**: 已接受  
**日期**: 项目初期  
**决策者**: 开发团队  

### 背景
需要选择一个Python Web框架来构建榜单系统后端API。

### 决策
选择FastAPI作为主要的Web框架。

### 理由
1. **自动文档生成**: 基于OpenAPI规范自动生成API文档
2. **高性能**: 基于Starlette和Pydantic，性能优异
3. **类型提示**: 原生支持Python类型提示
4. **异步支持**: 内置异步编程支持
5. **数据验证**: 自动请求/响应数据验证
6. **现代化**: 符合现代Python开发标准

### 后果
- 正面: 开发效率高，文档自动生成，性能优秀
- 负面: 相对较新的框架，生态系统不如Django成熟

---

## ADR-002: 采用服务层模式架构

**状态**: 已接受  
**日期**: 项目中期重构  
**决策者**: 开发团队  

### 背景
初期采用简单的CRUD模式，随着业务逻辑复杂度增加，需要更好的代码组织方式。

### 决策
将项目架构从简单CRUD模式重构为服务层模式（Service Layer Pattern）。

### 理由
1. **业务逻辑分离**: 将业务逻辑从API层分离到服务层
2. **可测试性**: 服务层便于编写单元测试
3. **可维护性**: 代码组织更清晰，便于维护
4. **可扩展性**: 支持复杂的业务场景和跨模型操作
5. **事务管理**: 在服务层统一管理数据库事务

### 后果
- 正面: 代码结构清晰，业务逻辑集中，易于测试和维护
- 负面: 增加了代码层次，简单操作可能显得冗余

---

## ADR-003: 使用PostgreSQL作为主数据库

**状态**: 已接受  
**日期**: 项目初期  
**决策者**: 开发团队  

### 背景
需要选择合适的数据库来存储榜单系统的数据。

### 决策
选择PostgreSQL作为主数据库。

### 理由
1. **ACID特性**: 完整的事务支持，保证数据一致性
2. **复杂查询**: 支持复杂的SQL查询和聚合操作
3. **JSON支持**: 原生支持JSON数据类型，适合存储灵活数据
4. **并发性能**: 优秀的并发读写性能
5. **扩展性**: 丰富的扩展插件生态
6. **生产就绪**: 成熟稳定，适合生产环境

### 后果
- 正面: 功能强大，性能优秀，适合复杂业务场景
- 负面: 相比SQLite部署复杂度更高

---

## ADR-004: 集成微信登录系统

**状态**: 已接受  
**日期**: 用户系统设计阶段  
**决策者**: 开发团队  

### 背景
根据原型图需求，系统需要支持微信登录功能。

### 决策
集成微信登录，支持OpenID和UnionID。

### 理由
1. **用户体验**: 降低用户注册和登录门槛
2. **原型需求**: 符合原型图的功能要求
3. **用户获取**: 便于获取微信用户信息（头像、昵称等）
4. **移动友好**: 适合移动端应用场景

### 实现方案
- 存储微信OpenID和UnionID
- 支持微信用户信息同步
- 兼容传统用户名密码登录

### 后果
- 正面: 提升用户体验，符合移动应用习惯
- 负面: 增加了第三方依赖，需要处理微信API变更

---

## ADR-005: 使用SQLAlchemy 2.0异步ORM

**状态**: 已接受  
**日期**: 数据库层设计阶段  
**决策者**: 开发团队  

### 背景
需要选择合适的ORM来处理数据库操作。

### 决策
使用SQLAlchemy 2.0的异步功能。

### 理由
1. **异步支持**: 与FastAPI的异步特性匹配
2. **性能优势**: 异步操作提升并发性能
3. **现代化**: SQLAlchemy 2.0采用了现代化的API设计
4. **类型提示**: 更好的类型提示支持
5. **生态成熟**: 丰富的插件和工具支持

### 后果
- 正面: 高性能，现代化API，与FastAPI配合良好
- 负面: 学习曲线相对陡峭，异步编程复杂度增加

---

## ADR-006: 实现配置管理系统

**状态**: 已接受  
**日期**: 系统配置设计阶段  
**决策者**: 开发团队  

### 背景
系统需要灵活的配置管理，支持运行时配置修改。

### 决策
实现基于数据库的配置管理系统，支持类型转换和缓存。

### 理由
1. **灵活性**: 支持运行时修改配置
2. **类型安全**: 支持多种数据类型和验证
3. **性能优化**: 内存缓存减少数据库查询
4. **管理界面**: 便于管理员通过界面管理配置
5. **版本控制**: 配置变更可追踪

### 实现特性
- 支持string、boolean、integer、float、json类型
- 内存缓存机制
- 配置分组和排序
- 公开/私有配置区分

### 后果
- 正面: 配置管理灵活，支持动态修改
- 负面: 增加了系统复杂度，需要考虑缓存一致性

---

## ADR-007: 榜单系统设计

**状态**: 已接受  
**日期**: 业务模型设计阶段  
**决策者**: 开发团队  

### 背景
根据原型图，需要设计支持5人和10人两种类型的竞速榜单系统。

### 决策
设计分离的榜单主表和明细表结构。

### 理由
1. **数据分离**: 榜单基础信息与具体排名数据分离
2. **扩展性**: 便于支持不同类型的榜单
3. **查询优化**: 分表设计便于查询优化
4. **状态管理**: 支持榜单生命周期管理

### 设计要点
- `rankings`表存储榜单基础信息
- `ranking_details`表存储具体排名数据
- 支持榜单状态流转（未开始→进行中→已结束）
- 时间字段同时存储Time和总秒数便于排序

### 后果
- 正面: 数据结构清晰，查询性能好，扩展性强
- 负面: 增加了表关联复杂度

---

## 未来的架构决策

### 待决策项目
1. **缓存策略**: 是否引入Redis缓存
2. **消息队列**: 是否需要异步任务处理
3. **文件存储**: 图片和文件的存储方案
4. **监控方案**: 应用性能监控和日志管理
5. **部署策略**: Docker化部署vs传统部署

### 决策原则
1. **简单性优先**: 在满足需求的前提下选择最简单的方案
2. **性能考虑**: 关键路径的性能优化
3. **可维护性**: 代码的可读性和可维护性
4. **扩展性**: 为未来的功能扩展留有余地
5. **成本效益**: 考虑开发和运维成本
