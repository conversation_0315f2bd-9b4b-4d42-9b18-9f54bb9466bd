"""Add user profile fields - location, user_number, gender, age

Revision ID: 002_add_user_profile_fields
Revises: 001_simplify_sponsor_model
Create Date: 2024-12-19 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_add_user_profile_fields'
down_revision = '001_simplify_sponsor_model'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：为用户表添加新的个人资料字段"""
    
    # 添加新字段到用户表
    op.add_column('users', sa.Column('location', sa.String(length=200), nullable=True, comment='所在地'))
    op.add_column('users', sa.Column('user_number', sa.String(length=50), nullable=True, comment='用户编号'))
    op.add_column('users', sa.Column('gender', sa.String(length=20), nullable=True, comment='性别'))
    op.add_column('users', sa.Column('age', sa.Integer(), nullable=True, comment='年龄'))
    
    # 创建唯一索引
    op.create_index('idx_users_user_number', 'users', ['user_number'], unique=True)
    
    # 添加检查约束
    op.create_check_constraint(
        'check_age_range',
        'users',
        'age IS NULL OR (age >= 0 AND age <= 150)'
    )


def downgrade() -> None:
    """降级数据库：移除用户表的新字段"""
    
    # 删除检查约束
    op.drop_constraint('check_gender_values', 'users', type_='check')
    op.drop_constraint('check_age_range', 'users', type_='check')
    
    # 删除索引
    op.drop_index('idx_users_user_number', table_name='users')
    
    # 删除字段
    op.drop_column('users', 'age')
    op.drop_column('users', 'gender')
    op.drop_column('users', 'user_number')
    op.drop_column('users', 'location')
