<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目原型图展示 - YYSLS Ranking App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto 30px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255,255,255,1);
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .prototype-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .prototype-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        }

        .card-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .card-filename {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Courier New', monospace;
        }

        .iframe-container {
            position: relative;
            width: 100%;
            height: 667px; /* iPhone 6/7/8 Plus height ratio */
            overflow: hidden;
        }

        .prototype-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: #666;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .card-actions {
            padding: 15px 20px;
            background: rgba(248,249,250,0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .open-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border: 2px solid #667eea;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .open-link:hover {
            background: #667eea;
            color: white;
        }

        .no-results {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin-top: 50px;
        }

        .fullscreen-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .fullscreen-btn:hover {
            background: rgba(255,255,255,1);
            transform: scale(1.1);
        }

        .prototype-card.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            border-radius: 0;
            max-width: none;
            max-height: none;
        }

        .prototype-card.fullscreen .iframe-container {
            height: calc(100vh - 140px);
        }

        .error-state {
            background: rgba(231, 76, 60, 0.1);
            border: 2px dashed #e74c3c;
        }

        @media (max-width: 768px) {
            .prototype-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            body {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 项目原型图展示</h1>
        <p>YYSLS Ranking App - 原型设计预览</p>
    </div>

    <div class="search-container">
        <input type="text" class="search-input" placeholder="搜索原型页面..." id="searchInput">
        <span class="search-icon">🔍</span>
    </div>

    <div class="prototype-grid" id="prototypeGrid">
        <!-- 原型卡片将通过 JavaScript 动态生成 -->
    </div>

    <div class="no-results" id="noResults" style="display: none;">
        没有找到匹配的原型页面
    </div>

    <script>
        // 原型文件列表
        const prototypeFiles = [
            { filename: 'about.html', title: '关于页面' },
            { filename: 'announcement-management.html', title: '公告管理' },
            { filename: 'list.html', title: '首页' },
            { filename: 'partner-management.html', title: '合作伙伴管理' },
            { filename: 'profile.html', title: '个人资料' },
            { filename: 'ranking-management.html', title: '排行榜管理' },
            { filename: 'system-management.html', title: '系统管理' },
            { filename: 'user-management.html', title: '用户管理' },
            { filename: 'user.html', title: '用户页面' },
            { filename: 'components/floating-tabbar.html', title: '浮动标签栏组件' }
        ];

        // 生成原型卡片
        function generatePrototypeCards(files = prototypeFiles) {
            const grid = document.getElementById('prototypeGrid');
            const noResults = document.getElementById('noResults');
            
            if (files.length === 0) {
                grid.innerHTML = '';
                noResults.style.display = 'block';
                return;
            }
            
            noResults.style.display = 'none';
            
            grid.innerHTML = files.map(file => `
                <div class="prototype-card">
                    <div class="card-header">
                        <div class="card-title">${file.title}</div>
                        <div class="card-filename">${file.filename}</div>
                    </div>
                    <div class="iframe-container">
                        <button class="fullscreen-btn" onclick="toggleFullscreen(this.nextElementSibling)" title="全屏查看">
                            ⛶
                        </button>
                        <iframe
                            class="prototype-iframe"
                            src="./${file.filename}"
                            title="${file.title}"
                            onload="hideLoading(this)"
                            onerror="handleIframeError(this)"
                        ></iframe>
                        <div class="loading-overlay">
                            <div class="loading-spinner"></div>
                            加载中...
                        </div>
                    </div>
                    <div class="card-actions">
                        <span style="color: #666; font-size: 0.9rem;">375px × 667px</span>
                        <a href="prototype/${file.filename}" target="_blank" class="open-link">
                            新窗口打开
                        </a>
                    </div>
                </div>
            `).join('');
        }

        // 隐藏加载动画
        function hideLoading(iframe) {
            const loadingOverlay = iframe.nextElementSibling;
            if (loadingOverlay && loadingOverlay.classList.contains('loading-overlay')) {
                loadingOverlay.style.display = 'none';
            }
        }

        // 搜索功能
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase().trim();
                
                if (searchTerm === '') {
                    generatePrototypeCards();
                    return;
                }
                
                const filteredFiles = prototypeFiles.filter(file => 
                    file.title.toLowerCase().includes(searchTerm) ||
                    file.filename.toLowerCase().includes(searchTerm)
                );
                
                generatePrototypeCards(filteredFiles);
            });
        }

        // 错误处理
        function handleIframeError(iframe) {
            const loadingOverlay = iframe.nextElementSibling;
            if (loadingOverlay && loadingOverlay.classList.contains('loading-overlay')) {
                loadingOverlay.innerHTML = `
                    <div style="text-align: center; color: #e74c3c;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">⚠️</div>
                        <div>加载失败</div>
                        <div style="font-size: 0.9rem; margin-top: 5px; opacity: 0.7;">请检查文件是否存在</div>
                    </div>
                `;
            }
        }

        // 添加全屏查看功能
        function toggleFullscreen(iframe) {
            const card = iframe.closest('.prototype-card');
            if (card.classList.contains('fullscreen')) {
                card.classList.remove('fullscreen');
                document.body.style.overflow = 'auto';
            } else {
                card.classList.add('fullscreen');
                document.body.style.overflow = 'hidden';
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            generatePrototypeCards();
            setupSearch();

            // 添加键盘快捷键支持
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    const fullscreenCard = document.querySelector('.prototype-card.fullscreen');
                    if (fullscreenCard) {
                        fullscreenCard.classList.remove('fullscreen');
                        document.body.style.overflow = 'auto';
                    }
                }
            });
        });
    </script>
</body>
</html>
