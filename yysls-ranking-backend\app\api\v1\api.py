"""
API v1 路由汇总

采用服务层模式，API层负责：
1. 请求参数验证
2. 调用业务服务层
3. 响应格式化
4. 异常处理
"""
from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    rankings,
    users,
    sponsors,
    upload,
    system_config,
    content
)

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 榜单相关路由
api_router.include_router(rankings.router, prefix="/rankings", tags=["榜单管理"])

# 用户相关路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 赞助商相关路由
api_router.include_router(sponsors.router, prefix="/sponsors", tags=["赞助商管理"])

# 文件上传相关路由
api_router.include_router(upload.router, prefix="/upload", tags=["文件上传"])

# 系统配置相关路由
api_router.include_router(system_config.router, prefix="/system", tags=["系统配置"])

# 内容管理相关路由
api_router.include_router(content.router, prefix="/content", tags=["内容管理"])
