"""Add team_name field to ranking_details table

Revision ID: 003_add_team_name_to_ranking_details
Revises: 002_add_user_profile_fields
Create Date: 2024-12-19 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003_add_team_name_to_ranking_details'
down_revision = '002_add_user_profile_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：为ranking_details表添加team_name字段"""
    
    # 添加team_name字段
    op.add_column('ranking_details', sa.Column('team_name', sa.String(length=100), nullable=True, comment='队伍名称'))


def downgrade() -> None:
    """降级数据库：移除ranking_details表的team_name字段"""
    
    # 删除team_name字段
    op.drop_column('ranking_details', 'team_name')
