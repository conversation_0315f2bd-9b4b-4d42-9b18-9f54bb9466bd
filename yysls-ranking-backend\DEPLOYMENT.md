# 燕友圈榜单系统 - 部署指南

## 🚀 部署概述

本文档提供燕友圈榜单系统的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 📋 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git
- Nginx (可选，用于反向代理)

## 🛠️ 环境准备

### 1. 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

### 2. 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 克隆项目
```bash
c <repository-url>
cd yysls-ranking-backend
```

## 🔧 配置文件设置

### 1. 生产环境配置
```bash
# 复制配置文件模板
cp .env.prod.example .env.prod

# 编辑配置文件
nano .env.prod
```

### 2. 必要配置项
```env
# 数据库配置
POSTGRES_PASSWORD=your_strong_password
POSTGRES_DB=yysls_ranking

# JWT密钥（必须修改）
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 邮件配置
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>

# 域名配置
FRONTEND_URL=https://your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

## 🚀 部署方式

### 方式1: 使用部署脚本（推荐）
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": ["https://docker.m.daocloud.io"]
}
EOF
docker pull 1ms.run/library/python:3.11-slim
docker tag 1ms.run/library/python:3.11-slim python:3.11-slim


### 方式2: 手动部署
```bash
# 1. 构建镜像
docker build -t yysls-ranking:latest .

# 2. 启动服务
docker-compose -f docker-compose.prod.yml up -d

docker-compose -f docker-compose.prod.yml up -d --build app  

# 3. 运行数据库迁移
docker-compose -f docker-compose.prod.yml exec app alembic upgrade head

# 4. 检查服务状态
docker-compose -f docker-compose.prod.yml ps
```

## 📊 服务管理

### 查看服务状态
```bash
docker-compose -f docker-compose.prod.yml ps
```
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d db
### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f app
docker-compose -f docker-compose.prod.yml logs -f db
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 重启特定服务
docker-compose -f docker-compose.prod.yml restart app
```

### 停止服务
```bash
docker-compose -f docker-compose.prod.yml down
```

### 更新应用
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 重新构建和部署
./deploy.sh
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许SSH（如果需要）
sudo ufw allow 22

# 启用防火墙
sudo ufw enable
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt（推荐）
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 数据库安全
- 使用强密码
- 限制数据库访问
- 定期备份数据

## 📈 监控和维护

### 1. 健康检查
```bash
# 检查应用健康状态
curl http://localhost:8000/health

# 检查API文档
curl http://localhost:8000/docs
```

### 2. 数据库备份
```bash
# 手动备份
docker exec yysls-ranking-db pg_dump -U postgres yysls_ranking > backup_$(date +%Y%m%d).sql

# 设置定时备份（crontab）
0 2 * * * docker exec yysls-ranking-db pg_dump -U postgres yysls_ranking > /backups/backup_$(date +\%Y\%m\%d).sql
```

### 3. 日志管理
```bash
# 清理旧日志
docker system prune -f

# 设置日志轮转
# 在 /etc/logrotate.d/docker 中配置
```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose -f docker-compose.prod.yml logs app
   
   # 检查配置文件
   docker-compose -f docker-compose.prod.yml config
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose -f docker-compose.prod.yml exec db pg_isready -U postgres
   
   # 检查网络连接
   docker network ls
   ```

3. **应用无法访问**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8000
   
   # 检查防火墙设置
   sudo ufw status
   ```

### 调试技巧

1. **进入容器调试**
   ```bash
   docker-compose -f docker-compose.prod.yml exec app bash
   ```

2. **查看容器资源使用**
   ```bash
   docker stats
   ```

3. **检查容器网络**
   ```bash
   docker network inspect yysls-ranking_yysls-network
   ```

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器配置满足要求
- [ ] Docker和Docker Compose已安装
- [ ] 配置文件已正确设置
- [ ] 域名DNS已配置
- [ ] SSL证书已准备

### 部署后检查
- [ ] 所有容器正常运行
- [ ] 数据库连接正常
- [ ] API接口可访问
- [ ] 健康检查通过
- [ ] 日志无错误信息

### 安全检查
- [ ] 防火墙已配置
- [ ] SSL证书已安装
- [ ] 数据库密码已修改
- [ ] JWT密钥已更换
- [ ] 敏感信息已保护

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /path/to/yysls-ranking-backend
          git pull origin main
          ./deploy.sh
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队

---

**部署状态**: 🟢 就绪 | **文档版本**: v1.0 | **最后更新**: 2024年当前日期
