"""
Excel文件上传API测试
"""
import os
import tempfile
from io import BytesIO

import pytest
from fastapi.testclient import TestClient
from openpyxl import Workbook

from app.main import app
from tests.conftest import TestingSessionLocal, override_get_db


class TestUploadExcelAPI:
    """Excel文件上传API测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.client = TestClient(app)
        # 这里需要添加认证token，实际测试时需要先登录获取token
        self.headers = {
            "Authorization": "Bearer test_token"  # 需要替换为实际的测试token
        }
    
    def create_test_excel(self, data_rows=None):
        """创建测试用的Excel文件"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍名称", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)

        # 写入测试数据
        if data_rows is None:
            data_rows = [
                [1, 5, "05:30", 5, "燕友圈战队", "队伍A,队伍B,队伍C,队伍D,队伍E"],
                [6, 10, "06:15", 5, "竞速联盟", "队伍F,队伍G,队伍H,队伍I,队伍J"]
            ]
        
        for row_idx, row_data in enumerate(data_rows, 2):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        # 保存到字节流
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        return excel_buffer
    
    def test_download_excel_template(self):
        """测试下载Excel模板"""
        response = self.client.get(
            "/api/v1/upload/excel/template?ranking_type=5_person",
            headers=self.headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert "attachment" in response.headers["content-disposition"]
        assert len(response.content) > 0
    
    def test_download_excel_template_invalid_type(self):
        """测试下载无效类型的Excel模板"""
        response = self.client.get(
            "/api/v1/upload/excel/template?ranking_type=invalid_type",
            headers=self.headers
        )
        
        assert response.status_code == 400
        assert "无效的榜单类型" in response.json()["detail"]
    
    def test_validate_excel_file_success(self):
        """测试验证Excel文件成功"""
        excel_buffer = self.create_test_excel()
        
        response = self.client.post(
            "/api/v1/upload/excel/validate",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["is_valid"] is True
        assert data["data"]["filename"] == "test.xlsx"
    
    def test_validate_excel_file_invalid_format(self):
        """测试验证无效格式的文件"""
        text_content = BytesIO(b"This is not an Excel file")
        
        response = self.client.post(
            "/api/v1/upload/excel/validate",
            headers=self.headers,
            files={"file": ("test.txt", text_content, "text/plain")}
        )
        
        assert response.status_code == 400
        assert "不支持的文件格式" in response.json()["detail"]
    
    def test_parse_excel_file_success(self):
        """测试解析Excel文件成功"""
        excel_buffer = self.create_test_excel()
        
        response = self.client.post(
            "/api/v1/upload/excel/parse",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["total_records"] == 2
        assert len(data["data"]["ranking_details"]) == 2
        
        # 检查解析的数据
        first_record = data["data"]["ranking_details"][0]
        assert first_record["rank_start"] == 1
        assert first_record["rank_end"] == 5
        assert first_record["participant_count"] == 5
        assert first_record["completion_seconds"] == 5 * 60 + 30
        assert first_record["team_name"] == "燕友圈战队"
    
    def test_parse_excel_file_invalid_data(self):
        """测试解析包含无效数据的Excel文件"""
        # 创建包含无效数据的Excel
        data_rows = [
            ["abc", 5, "05:30", 5, "燕友圈战队", "队伍A"],  # 排名开始应为数字
        ]
        excel_buffer = self.create_test_excel(data_rows)
        
        response = self.client.post(
            "/api/v1/upload/excel/parse",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 400
        assert "数据解析错误" in response.json()["detail"]
    
    def test_upload_excel_temp_success(self):
        """测试临时上传Excel文件成功"""
        excel_buffer = self.create_test_excel()
        
        response = self.client.post(
            "/api/v1/upload/excel/upload-temp",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["original_filename"] == "test.xlsx"
        assert "temp_filename" in data["data"]
        assert "temp_filepath" in data["data"]
        
        # 验证文件确实被保存
        temp_filepath = data["data"]["temp_filepath"]
        assert os.path.exists(temp_filepath)
        
        # 清理测试文件
        if os.path.exists(temp_filepath):
            os.remove(temp_filepath)
    
    def test_upload_excel_temp_invalid_file(self):
        """测试上传无效文件"""
        text_content = BytesIO(b"This is not an Excel file")
        
        response = self.client.post(
            "/api/v1/upload/excel/upload-temp",
            headers=self.headers,
            files={"file": ("test.txt", text_content, "text/plain")}
        )
        
        assert response.status_code == 400
        assert "不支持的文件格式" in response.json()["detail"]
    
    def test_delete_temp_excel_success(self):
        """测试删除临时Excel文件成功"""
        # 首先上传一个文件
        excel_buffer = self.create_test_excel()
        
        upload_response = self.client.post(
            "/api/v1/upload/excel/upload-temp",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert upload_response.status_code == 200
        temp_filename = upload_response.json()["data"]["temp_filename"]
        
        # 删除文件
        delete_response = self.client.delete(
            f"/api/v1/upload/excel/temp/{temp_filename}",
            headers=self.headers
        )
        
        assert delete_response.status_code == 200
        assert delete_response.json()["code"] == 200
    
    def test_delete_temp_excel_not_found(self):
        """测试删除不存在的临时文件"""
        response = self.client.delete(
            "/api/v1/upload/excel/temp/nonexistent_file.xlsx",
            headers=self.headers
        )
        
        assert response.status_code == 404
        assert "临时文件不存在" in response.json()["detail"]
    
    def test_cleanup_temp_files(self):
        """测试清理过期临时文件"""
        response = self.client.get(
            "/api/v1/upload/excel/temp/cleanup",
            headers=self.headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "deleted_count" in data["data"]
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        excel_buffer = self.create_test_excel()
        
        # 不提供认证头
        response = self.client.post(
            "/api/v1/upload/excel/validate",
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 401
    
    def test_missing_file_parameter(self):
        """测试缺少文件参数"""
        response = self.client.post(
            "/api/v1/upload/excel/validate",
            headers=self.headers
        )
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_empty_file(self):
        """测试空文件"""
        empty_buffer = BytesIO(b"")
        
        response = self.client.post(
            "/api/v1/upload/excel/validate",
            headers=self.headers,
            files={"file": ("empty.xlsx", empty_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
        
        assert response.status_code == 400


class TestRankingExcelIntegration:
    """榜单Excel集成测试"""

    def setup_method(self):
        """测试前准备"""
        self.client = TestClient(app)
        self.headers = {
            "Authorization": "Bearer test_token"
        }

    def create_test_excel(self):
        """创建测试用的Excel文件"""
        workbook = Workbook()
        worksheet = workbook.active

        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)

        # 写入测试数据
        data_rows = [
            [1, 5, "05:30", 5, "队伍A,队伍B,队伍C,队伍D,队伍E"],
            [6, 10, "06:15", 5, "队伍F,队伍G,队伍H,队伍I,队伍J"]
        ]

        for row_idx, row_data in enumerate(data_rows, 2):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)

        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()

        return excel_buffer

    def test_create_ranking_with_excel_import(self):
        """测试创建榜单并导入Excel数据"""
        # 1. 先上传Excel文件
        excel_buffer = self.create_test_excel()
        upload_response = self.client.post(
            "/api/v1/upload/excel/upload-temp",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )

        assert upload_response.status_code == 200
        temp_filepath = upload_response.json()["data"]["temp_filepath"]

        # 2. 创建榜单并导入Excel数据
        ranking_data = {
            "name": "测试榜单",
            "period": 1,
            "ranking_type": "5_person",
            "start_time": "2024-01-01T10:00:00",
            "end_time": "2024-01-01T18:00:00",
            "team_size_limit": 5,
            "excel_file_path": temp_filepath,
            "auto_import_details": True
        }

        create_response = self.client.post(
            "/api/v1/rankings",
            headers=self.headers,
            json=ranking_data
        )

        assert create_response.status_code == 200
        data = create_response.json()
        assert "已导入Excel数据" in data["message"]

        ranking_id = data["data"]["id"]

        # 3. 验证榜单明细是否正确导入
        details_response = self.client.get(
            f"/api/v1/rankings/{ranking_id}/details",
            headers=self.headers
        )

        assert details_response.status_code == 200
        details_data = details_response.json()
        assert len(details_data["data"]) == 2  # 应该有2条明细记录

    def test_update_ranking_with_excel_import(self):
        """测试更新榜单并导入Excel数据"""
        # 1. 先创建一个榜单
        ranking_data = {
            "name": "测试榜单",
            "period": 1,
            "ranking_type": "5_person",
            "start_time": "2024-01-01T10:00:00",
            "end_time": "2024-01-01T18:00:00",
            "team_size_limit": 5
        }

        create_response = self.client.post(
            "/api/v1/rankings",
            headers=self.headers,
            json=ranking_data
        )

        assert create_response.status_code == 200
        ranking_id = create_response.json()["data"]["id"]

        # 2. 上传Excel文件
        excel_buffer = self.create_test_excel()
        upload_response = self.client.post(
            "/api/v1/upload/excel/upload-temp",
            headers=self.headers,
            files={"file": ("test.xlsx", excel_buffer, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )

        assert upload_response.status_code == 200
        temp_filepath = upload_response.json()["data"]["temp_filepath"]

        # 3. 更新榜单并导入Excel数据
        update_data = {
            "name": "更新后的榜单",
            "excel_file_path": temp_filepath,
            "auto_import_details": True,
            "replace_existing_details": True
        }

        update_response = self.client.put(
            f"/api/v1/rankings/{ranking_id}",
            headers=self.headers,
            json=update_data
        )

        assert update_response.status_code == 200
        data = update_response.json()
        assert "已导入Excel数据" in data["message"]

        # 4. 验证榜单明细是否正确导入
        details_response = self.client.get(
            f"/api/v1/rankings/{ranking_id}/details",
            headers=self.headers
        )

        assert details_response.status_code == 200
        details_data = details_response.json()
        assert len(details_data["data"]) == 2
