#!/bin/bash
# quick_fix_session.sh - 数据库会话问题快速修复脚本
# 
# 使用方法:
#   bash scripts/quick_fix_session.sh
#   bash scripts/quick_fix_session.sh --dry-run  # 预览修改但不执行
#
# 功能:
#   1. 自动备份原文件
#   2. 批量替换AsyncSession相关问题
#   3. 生成修复报告
#   4. 提供回滚功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_ROOT="."
API_DIR="app/api"
BACKUP_DIR="backups/session_fix_$(date +%Y%m%d_%H%M%S)"
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            echo "数据库会话问题快速修复脚本"
            echo ""
            echo "使用方法:"
            echo "  $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --dry-run    预览修改但不执行"
            echo "  --help, -h   显示此帮助信息"
            echo ""
            echo "功能:"
            echo "  - 自动备份原文件"
            echo "  - 修复AsyncSession导入问题"
            echo "  - 修复类型注解问题"
            echo "  - 生成修复报告"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    if [[ ! -d "$API_DIR" ]]; then
        log_error "未找到API目录: $API_DIR"
        exit 1
    fi
    
    if [[ ! -f "app/main.py" ]]; then
        log_error "未找到主应用文件: app/main.py"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 创建备份目录
create_backup_dir() {
    if [[ "$DRY_RUN" == "false" ]]; then
        log_info "创建备份目录: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
}

# 备份文件
backup_file() {
    local file_path="$1"
    local backup_path="$BACKUP_DIR/$file_path"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        mkdir -p "$(dirname "$backup_path")"
        cp "$file_path" "$backup_path"
        log_info "已备份: $file_path -> $backup_path"
    else
        log_info "[DRY-RUN] 将备份: $file_path"
    fi
}

# 修复单个文件
fix_file() {
    local file_path="$1"
    local temp_file="${file_path}.tmp"
    local changes_made=false
    
    log_info "处理文件: $file_path"
    
    # 检查文件是否包含需要修复的内容
    if ! grep -q "AsyncSession\|await.*service\." "$file_path" 2>/dev/null; then
        return 0
    fi
    
    # 备份原文件
    backup_file "$file_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY-RUN] 将修复的问题:"
        
        # 检查AsyncSession导入
        if grep -q "from sqlalchemy.ext.asyncio import AsyncSession" "$file_path"; then
            log_warning "  - AsyncSession导入问题"
        fi
        
        # 检查类型注解
        if grep -q "db: AsyncSession = Depends(get_db)" "$file_path"; then
            log_warning "  - 类型注解问题"
        fi
        
        # 检查await调用
        if grep -q "await.*service\." "$file_path"; then
            log_warning "  - await service调用问题（需要手动检查）"
        fi
        
        return 0
    fi
    
    # 开始修复
    cp "$file_path" "$temp_file"
    
    # 修复1: AsyncSession导入
    if sed -i 's/from sqlalchemy\.ext\.asyncio import AsyncSession/from sqlalchemy.orm import Session/g' "$temp_file" 2>/dev/null; then
        if ! cmp -s "$file_path" "$temp_file"; then
            changes_made=true
            log_success "  ✓ 修复AsyncSession导入"
        fi
    fi
    
    # 修复2: 类型注解
    if sed -i 's/db: AsyncSession = Depends(get_db)/db: Session = Depends(get_db)/g' "$temp_file" 2>/dev/null; then
        if ! cmp -s "$file_path" "$temp_file"; then
            changes_made=true
            log_success "  ✓ 修复类型注解"
        fi
    fi
    
    # 检查await service调用（不自动修复，只提醒）
    if grep -q "await.*service\." "$temp_file"; then
        log_warning "  ⚠ 发现await service调用，需要手动检查和修复"
    fi
    
    # 应用修改
    if [[ "$changes_made" == "true" ]]; then
        mv "$temp_file" "$file_path"
        log_success "已修复: $file_path"
        return 1  # 表示有修改
    else
        rm -f "$temp_file"
        return 0  # 表示无修改
    fi
}

# 主修复函数
main_fix() {
    log_info "开始修复数据库会话问题..."
    
    local total_files=0
    local fixed_files=0
    local files_with_issues=()
    
    # 查找所有Python文件
    while IFS= read -r -d '' file; do
        total_files=$((total_files + 1))
        
        if fix_file "$file"; then
            # 无修改
            :
        else
            # 有修改
            fixed_files=$((fixed_files + 1))
            files_with_issues+=("$file")
        fi
    done < <(find "$API_DIR" -name "*.py" -not -path "*/.*" -print0)
    
    # 生成报告
    echo ""
    log_info "修复完成报告:"
    echo "  总文件数: $total_files"
    echo "  修复文件数: $fixed_files"
    
    if [[ ${#files_with_issues[@]} -gt 0 ]]; then
        echo ""
        log_info "已修复的文件:"
        for file in "${files_with_issues[@]}"; do
            echo "  - $file"
        done
    fi
    
    if [[ "$DRY_RUN" == "false" && "$fixed_files" -gt 0 ]]; then
        echo ""
        log_success "备份位置: $BACKUP_DIR"
        log_warning "请手动检查并移除不必要的await调用"
        log_info "建议运行以下命令验证修复结果:"
        echo "  python scripts/verify_session_consistency.py"
        echo "  python -c \"from app.main import create_application; create_application()\""
    fi
}

# 生成回滚脚本
generate_rollback_script() {
    if [[ "$DRY_RUN" == "false" && -d "$BACKUP_DIR" ]]; then
        local rollback_script="$BACKUP_DIR/rollback.sh"
        
        cat > "$rollback_script" << EOF
#!/bin/bash
# 自动生成的回滚脚本
# 使用方法: bash $rollback_script

echo "开始回滚数据库会话修复..."

# 恢复所有备份文件
find "$BACKUP_DIR" -name "*.py" | while read backup_file; do
    original_file="\${backup_file#$BACKUP_DIR/}"
    if [[ -f "\$original_file" ]]; then
        cp "\$backup_file" "\$original_file"
        echo "已恢复: \$original_file"
    fi
done

echo "回滚完成"
EOF
        
        chmod +x "$rollback_script"
        log_info "回滚脚本已生成: $rollback_script"
    fi
}

# 主函数
main() {
    echo "🔧 数据库会话问题快速修复工具"
    echo "=================================="
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "运行模式: 预览模式（不会实际修改文件）"
    else
        log_info "运行模式: 修复模式"
    fi
    
    echo ""
    
    # 执行检查和修复
    check_project_structure
    create_backup_dir
    main_fix
    generate_rollback_script
    
    echo ""
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预览完成。使用不带 --dry-run 参数运行以执行实际修复。"
    else
        log_success "修复完成！"
    fi
}

# 执行主函数
main "$@"
