# 燕友圈榜单系统后端服务

一个现代化的榜单管理系统后端服务，采用FastAPI框架开发，支持5人/10人竞速榜单、微信登录、赞助商管理等功能。

## 🚀 项目状态

- **总体完成度**: 85%
- **API端点数**: 40+ 个
- **开发状态**: 核心功能已完成，进入测试阶段
- **最后更新**: 2024年当前日期

## 功能特性

### 1. 榜单管理系统
- 榜单基础信息管理（名称、期数、时间、人数限制等）
- 榜单明细记录（排名范围、时间区间、参与人数统计）
- 支持5人和10人不同类型的榜单

### 2. 用户管理系统
- 微信登录集成
- 用户角色管理（普通用户/管理员）
- 用户基础信息管理

### 3. 赞助商管理
- 赞助商信息管理
- 头像/Logo上传
- 联系方式管理

### 4. 系统配置管理
- 播报功能开关配置
- 赞助商展示配置
- 广告内容配置
- 系统参数设置

### 5. 内容管理系统
- 播报信息内容配置
- 静态页面内容管理
- 公告信息管理

## 技术栈

- **Web框架**: FastAPI 0.104.1
- **数据库**: PostgreSQL + SQLAlchemy 2.0 (异步)
- **认证**: JWT + 微信OAuth + 权限控制
- **邮件**: SMTP异步发送
- **测试**: pytest + pytest-asyncio
- **文档**: 自动生成OpenAPI/Swagger文档
- **架构**: 服务层模式 + 依赖注入

## 项目结构

```
yysls-ranking-backend/
├── app/                    # 应用核心代码
│   ├── api/               # API路由
│   │   └── v1/           # API v1版本
│   │       ├── endpoints/ # 具体端点
│   │       └── api.py    # 路由汇总
│   ├── core/             # 核心功能
│   ├── crud/             # 数据库CRUD操作
│   ├── models/           # SQLAlchemy模型
│   ├── schemas/          # Pydantic模式
│   ├── utils/            # 工具函数
│   ├── config.py         # 配置管理
│   └── main.py           # 应用入口
├── tests/                # 测试代码
├── docs/                 # 文档
├── alembic/              # 数据库迁移
├── requirements.txt      # 依赖列表
├── pyproject.toml        # 项目配置
└── README.md            # 项目说明
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置数据库连接等信息
```

### 3. 数据库初始化

```bash
# 初始化数据库迁移
alembic init alembic

# 创建迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head
```

### 4. 启动服务

```bash
# 开发模式启动
python run.py

# 或使用uvicorn直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 访问文档

- API文档: http://localhost:8000/docs
- ReDoc文档: http://localhost:8000/redoc

## API接口

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/wechat-login` - 微信登录
- `POST /api/v1/auth/refresh` - 刷新token

### 榜单管理
- `GET /api/v1/rankings` - 获取榜单列表
- `POST /api/v1/rankings` - 创建榜单
- `GET /api/v1/rankings/{id}` - 获取榜单详情
- `PUT /api/v1/rankings/{id}` - 更新榜单
- `DELETE /api/v1/rankings/{id}` - 删除榜单

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/{id}` - 更新用户信息

### 赞助商管理
- `GET /api/v1/sponsors` - 获取赞助商列表
- `POST /api/v1/sponsors` - 创建赞助商
- `PUT /api/v1/sponsors/{id}` - 更新赞助商信息

### 系统配置
- `GET /api/v1/system/config` - 获取系统配置
- `PUT /api/v1/system/config` - 更新系统配置

### 内容管理
- `GET /api/v1/content/announcements` - 获取公告列表
- `POST /api/v1/content/announcements` - 创建公告
- `GET /api/v1/content/about` - 获取关于我们内容

## 开发指南

### 代码规范
- 使用black进行代码格式化
- 使用isort进行导入排序
- 使用flake8进行代码检查

```bash
# 格式化代码
black app tests
isort app tests

# 代码检查
flake8 app tests
```

### 测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_rankings.py

# 生成测试覆盖率报告
pytest --cov=app tests/
```

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t yysls-ranking-backend .

# 运行容器
docker run -p 8000:8000 yysls-ranking-backend
```

### 生产环境
```bash
# 使用gunicorn部署
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 📊 项目进度

### 已完成模块 ✅
- **项目架构设计** (100%) - FastAPI + SQLAlchemy 2.0 异步架构
- **数据库设计** (100%) - 7个核心数据表，完整ER图
- **核心业务模型** (100%) - 6个服务类，服务层模式
- **API接口层** (100%) - 40+ API端点，7个模块
- **用户认证系统** (95%) - JWT + 微信登录 + 权限控制

### 进行中模块 🔄
- **测试体系建设** (20%) - pytest框架已配置，单元测试编写中

### 待完成模块 ⏳
- **生产环境部署** (0%) - Docker化、CI/CD配置
- **功能完善** (0%) - 文件上传、统计报表等

### 技术亮点 🌟
- 现代化异步架构，支持高并发
- 完整的认证和权限控制系统
- 统一的API响应格式和错误处理
- 自动化API文档生成
- 类型安全的Python代码
- 完整的测试基础设施

## 📞 联系方式

- **项目团队**: 燕友圈工作室
- **项目版本**: v1.0.0
- **开发状态**: 🟢 健康

## 许可证

MIT License
