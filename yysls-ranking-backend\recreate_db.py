#!/usr/bin/env python3
"""
重新创建数据库表的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import engine, Base
from app.models.user import User
from app.models.ranking import Ranking, RankingDetail
from app.models.sponsor import Sponsor
from app.models.system_config import SystemConfig
from app.models.content import Content
from app.models.broadcast_message import BroadcastMessage


async def recreate_database():
    """重新创建数据库表"""
    try:
        print("开始重新创建数据库表...")
        
        # 删除所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            print("已删除所有现有表")
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            print("已创建所有表")
        
        print("数据库表重新创建完成！")
        
    except Exception as e:
        print(f"重新创建数据库表失败: {e}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(recreate_database())
