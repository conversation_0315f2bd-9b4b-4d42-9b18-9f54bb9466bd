#!/bin/bash

# 燕友圈榜单系统 - Nginx配置生成脚本

set -e

echo "🌐 Nginx 配置生成工具"
echo "===================="

# 检查模板文件
if [ ! -f "nginx/conf.d/yysls.conf.template" ]; then
    echo "❌ 模板文件不存在: nginx/conf.d/yysls.conf.template"
    exit 1
fi

# 检查是否已有配置文件
if [ -f "nginx/conf.d/yysls.conf" ]; then
    echo "⚠️  配置文件已存在: nginx/conf.d/yysls.conf"
    read -p "是否要重新生成？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "👋 操作已取消"
        exit 0
    fi
fi

echo ""
echo "📝 请输入配置信息："

# 获取域名
read -p "请输入域名 (例: example.com): " domain_name
if [ -z "$domain_name" ]; then
    echo "❌ 域名不能为空"
    exit 1
fi

# 选择环境
echo ""
echo "选择部署环境："
echo "   1) 生产环境 (HTTPS + 严格限流)"
echo "   2) 测试环境 (HTTPS + 宽松限流)"
echo "   3) 开发环境 (HTTP only)"
read -p "请选择 (1-3): " env_choice

# 根据环境选择不同的模板处理
case $env_choice in
    1)
        echo "✅ 生产环境配置"
        api_burst=20
        login_burst=3
        enable_ssl=true
        enable_docs_restriction=true
        ;;
    2)
        echo "✅ 测试环境配置"
        api_burst=50
        login_burst=10
        enable_ssl=true
        enable_docs_restriction=false
        ;;
    3)
        echo "✅ 开发环境配置"
        # 使用开发模板
        if [ -f "nginx/conf.d/yysls-dev.conf.example" ]; then
            cp nginx/conf.d/yysls-dev.conf.example nginx/conf.d/yysls.conf
            sed -i "s/localhost your-dev-domain.com/$domain_name/g" nginx/conf.d/yysls.conf
            echo "✅ 开发环境配置生成完成"
            echo "   配置文件: nginx/conf.d/yysls.conf"
            echo "   协议: HTTP only"
            echo "   域名: $domain_name"
            exit 0
        else
            echo "❌ 开发环境模板不存在"
            exit 1
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 生成配置文件
echo ""
echo "🔧 生成配置文件..."

# 复制模板
cp nginx/conf.d/yysls.conf.template nginx/conf.d/yysls.conf

# 替换域名
sed -i "s/{{DOMAIN_NAME}}/$domain_name/g" nginx/conf.d/yysls.conf

# 根据环境调整配置
if [ "$enable_docs_restriction" = true ]; then
    # 生产环境：限制文档访问
    sed -i '/location \/docs {/,/}/ {
        /proxy_pass/i\        # 生产环境限制访问\
        allow 127.0.0.1;\
        allow 10.0.0.0/8;\
        allow **********/12;\
        allow ***********/16;\
        deny all;
    }' nginx/conf.d/yysls.conf
fi

# 调整限流配置
sed -i "s/burst=20/burst=$api_burst/g" nginx/conf.d/yysls.conf
sed -i "s/burst=3/burst=$login_burst/g" nginx/conf.d/yysls.conf

echo "✅ 配置文件生成完成"

# 显示配置信息
echo ""
echo "📋 配置信息："
echo "   域名: $domain_name"
echo "   环境: $([ $env_choice -eq 1 ] && echo '生产' || echo '测试')"
echo "   SSL: $([ "$enable_ssl" = true ] && echo '启用' || echo '禁用')"
echo "   API限流: $api_burst 请求/秒"
echo "   登录限流: $login_burst 请求/分钟"
echo "   文档访问: $([ "$enable_docs_restriction" = true ] && echo '受限' || echo '开放')"

# 验证配置
echo ""
echo "🔍 验证配置文件..."
if command -v nginx >/dev/null 2>&1; then
    nginx -t -c $(pwd)/nginx/nginx.conf -p $(pwd)/nginx/
    echo "✅ 配置文件语法正确"
else
    echo "⚠️  nginx 未安装，跳过语法检查"
    echo "   可以使用 Docker 检查: docker run --rm -v \$(pwd)/nginx:/etc/nginx:ro nginx:alpine nginx -t"
fi

# 下一步提示
echo ""
echo "🚀 下一步："
echo "   1. 配置SSL证书: ./scripts/generate-ssl.sh"
echo "   2. 启动服务: docker-compose -f docker-compose.prod.yml up -d"
echo "   3. 测试访问: https://$domain_name"
echo ""
echo "⚠️  重要提醒："
echo "   - 配置文件包含域名等敏感信息，不会提交到版本控制"
echo "   - 如需修改配置，请重新运行此脚本或手动编辑"
echo "   - 生产环境请确保SSL证书有效"
