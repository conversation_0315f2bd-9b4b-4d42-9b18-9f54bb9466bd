{
	"easycom": {
		"autoscan": true, 
		"custom": {
			"fui-(.*)": "@/components/firstui/fui-$1/fui-$1.vue",
			"lui-(.*)": "@/components/lui/lui-$1/lui-$1.vue",
			"sx-svg": "@/components/sx-svg/sx-svg.vue",
			"top-navbar": "@/components/common/top-navbar.vue"
		}
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/reserveASeat/index",
			"style": {
				"navigationBarTitleText": "占位",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [],
	"preloadRule": {},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "dnf",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	"mp-weixin": {
	    "sharing": true
	},
	"tabBar": {
		"color": "#999",
		"selectedColor": "#333",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/home/<USER>",
				"text": "首页"
			},
			{
				"pagePath": "pages/reserveASeat/index",
				"text": "占位"
			}
		]
	}
}
