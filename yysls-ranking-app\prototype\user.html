<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: url('../static/home/<USER>') no-repeat center top fixed;
            background-size: cover;
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        /* 背景遮罩层 */
        .bg-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(26, 26, 26, 0.3) 0%,
                rgba(26, 26, 26, 0.6) 50%,
                rgba(26, 26, 26, 0.8) 100%
            );
            z-index: -1;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 用户头像样式 */
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(212, 175, 55, 0.5);
            box-shadow:
                0 0 20px rgba(212, 175, 55, 0.3),
                inset 0 0 10px rgba(212, 175, 55, 0.1);
        }
        
        /* 身份标签样式 */
        .user-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .user-badge.admin {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #D4AF37;
            text-shadow: 0 0 8px rgba(212, 175, 55, 0.4);
        }
        
        .user-badge.normal {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(245, 245, 220, 0.3);
            color: #F5F5DC;
        }
        
        /* 功能列表项样式 */
        .function-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: rgba(74, 74, 74, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .function-item:hover {
            background: rgba(74, 74, 74, 0.4);
            border-color: rgba(212, 175, 55, 0.3);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.15);
        }
        
        .function-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);
        }

        .function-item:hover .function-icon {
            filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);
        }
        
        /* 管理员功能区域 */
        .admin-section {
            border: 2px solid rgba(212, 175, 55, 0.3);
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.05) 0%, rgba(212, 175, 55, 0.02) 100%);
        }
        
        /* 退出按钮样式 */
        .logout-btn {
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.8) 0%, rgba(185, 28, 28, 0.9) 100%);
            border: 1px solid rgba(220, 38, 38, 0.5);
            color: #FFFFFF;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.9) 0%, rgba(185, 28, 28, 1) 100%);
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        }
        
        /* 移除滚动动画 - 保持静态显示 */
        .scroll-animate {
            opacity: 1;
        }

        /* 未登录状态样式 */
        .default-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
            border: 3px solid rgba(212, 175, 55, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 0 20px rgba(212, 175, 55, 0.2),
                inset 0 0 10px rgba(212, 175, 55, 0.1);
        }

        .default-avatar img {
            width: 40px;
            height: 40px;
            filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);
        }

        /* 微信登录按钮样式 */
        .wechat-login-btn {
            background: linear-gradient(135deg, #07C160 0%, #06AD56 100%);
            border: 1px solid rgba(7, 193, 96, 0.5);
            color: #FFFFFF;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(7, 193, 96, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
        }

        .wechat-login-btn:hover {
            background: linear-gradient(135deg, #06AD56 0%, #059A4C 100%);
            box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
        }

        .wechat-login-btn img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        /* 未登录提示文字 */
        .login-prompt {
            color: #B8B8B8;
            font-size: 14px;
            margin-top: 8px;
        }

        /* 悬浮测试按钮 */
        .floating-test-btn {
            position: fixed;
            bottom: 140px;
            right: 20px;
            background: rgba(74, 74, 74, 0.9);
            border: 1px solid rgba(212, 175, 55, 0.3);
            color: #F5F5DC;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .floating-test-btn:hover {
            background: rgba(74, 74, 74, 1);
            border-color: rgba(212, 175, 55, 0.5);
        }

        /* 隐藏元素 */
        .hidden {
            display: none !important;
        }

        /* 未登录状态的垂直居中布局 */
        .not-logged-in-layout {
            min-height: calc(100vh - 140px); /* 减去底部标签栏高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px 0;
        }

        /* 未登录状态的关于我们按钮 */
        .about-btn-simple {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            color: #F5F5DC;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .about-btn-simple:hover {
            background: rgba(74, 74, 74, 0.5);
            border-color: rgba(212, 175, 55, 0.4);
        }

        .about-btn-simple img {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);
        }
        
        /* 浮动标签栏间距 */
        .main-content {
            padding-bottom: 140px;
        }

        /* 浮动标签栏容器样式 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }

        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 背景遮罩层 -->
    <div class="bg-overlay"></div>
    
    <!-- 已登录状态的布局 -->
    <div id="loggedInLayout" class="min-h-screen p-4 space-y-6 main-content">

        <!-- 用户信息区域 -->
        <div class="ink-card p-6 text-center scroll-animate">
            <div class="flex flex-col items-center">
                <!-- 已登录状态 -->
                <div id="loggedInUser" class="flex flex-col items-center">
                    <!-- 用户头像 -->
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=160&h=160&fit=crop&crop=face"
                         alt="用户头像" class="user-avatar mb-4">

                    <!-- 用户名和身份标签 -->
                    <div class="flex items-center mb-2">
                        <h2 class="text-xl font-bold gold-text">剑客无名</h2>
                        <!-- 管理员标签 - 根据用户身份动态显示 -->
                        <span class="user-badge admin" id="adminBadge">
                            <img src="https://unpkg.com/lucide-static@latest/icons/crown.svg" alt="管理员"
                                 class="w-3 h-3 mr-1" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                            管理员
                        </span>
                        <!-- 普通用户标签 -->
                        <span class="user-badge normal hidden" id="normalBadge">
                            <img src="https://unpkg.com/lucide-static@latest/icons/user.svg" alt="用户"
                                 class="w-3 h-3 mr-1" style="filter: brightness(0) saturate(100%) invert(96%) sepia(6%) saturate(248%) hue-rotate(315deg) brightness(100%) contrast(93%);">
                            江湖侠客
                        </span>
                    </div>

                    <!-- 用户等级/积分信息 -->
                    <div class="flex items-center space-x-4 text-sm text-gray-300">
                        <div class="flex items-center">
                            <img src="https://unpkg.com/lucide-static@latest/icons/star.svg" alt="等级"
                                 class="w-4 h-4 mr-1" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                            <span>武林高手</span>
                        </div>
                        <div class="flex items-center">
                            <img src="https://unpkg.com/lucide-static@latest/icons/zap.svg" alt="积分"
                                 class="w-4 h-4 mr-1" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                            <span>1580积分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通用功能列表 -->
        <div class="ink-card p-4 scroll-animate" id="functionList">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/menu.svg" alt="功能"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h3 class="text-lg font-semibold gold-text">功能中心</h3>
            </div>

            <div class="space-y-2">
                <!-- 需要登录的功能 -->
                <div class="function-item login-required" onclick="handleFunctionClick('profile')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/shield-user.svg" alt="个人资料" class="function-icon">
                    <span class="flex-1 text-gray-200">个人资料</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item login-required" onclick="handleFunctionClick('history')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/history.svg" alt="历史记录" class="function-icon">
                    <span class="flex-1 text-gray-200">历史记录</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <!-- 无需登录的功能 -->
                <div class="function-item" onclick="handleFunctionClick('settings')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/settings.svg" alt="设置" class="function-icon">
                    <span class="flex-1 text-gray-200">设置</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item" onclick="handleFunctionClick('about')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/info.svg" alt="关于我们" class="function-icon">
                    <span class="flex-1 text-gray-200">关于我们</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <!-- 未登录状态的登录提示 -->
                <div class="function-item hidden" id="loginPromptItem" onclick="handleWechatLogin()">
                    <img src="https://unpkg.com/lucide-static@latest/icons/log-in.svg" alt="登录" class="function-icon">
                    <span class="flex-1 text-gray-200">点击登录解锁更多功能</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>
            </div>
        </div>

        <!-- 管理员功能区域 -->
        <div class="ink-card admin-section p-4 scroll-animate" id="adminSection">
            <div class="flex items-center space-x-2 mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/shield-check.svg" alt="管理员"
                     class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h3 class="text-lg font-semibold gold-text">管理员专区</h3>
                <div class="flex-1"></div>
                <span class="text-xs text-gray-400">仅管理员可见</span>
            </div>

            <div class="space-y-2">
                <div class="function-item" onclick="handleAdminFunction('userManage')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/users.svg" alt="用户管理" class="function-icon">
                    <span class="flex-1 text-gray-200">用户管理</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item" onclick="handleAdminFunction('rankingManage')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/trophy.svg" alt="榜单管理" class="function-icon">
                    <span class="flex-1 text-gray-200">榜单管理</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item" onclick="handleAdminFunction('broadcastManage')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/megaphone.svg" alt="播报管理" class="function-icon">
                    <span class="flex-1 text-gray-200">播报管理</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item" onclick="handleAdminFunction('partnerManage')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/handshake.svg" alt="合作伙伴管理" class="function-icon">
                    <span class="flex-1 text-gray-200">合作伙伴管理</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>

                <div class="function-item" onclick="handleAdminFunction('systemSettings')">
                    <img src="https://unpkg.com/lucide-static@latest/icons/cog.svg" alt="系统设置" class="function-icon">
                    <span class="flex-1 text-gray-200">系统设置</span>
                    <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" alt=">"
                         class="w-4 h-4" style="filter: brightness(0) saturate(100%) invert(75%) sepia(11%) saturate(73%) hue-rotate(316deg) brightness(98%) contrast(94%);">
                </div>
            </div>
        </div>

        <!-- 退出按钮 -->
        <div class="scroll-animate login-required" id="logoutSection">
            <div class="logout-btn" onclick="handleLogout()">
                <div class="flex items-center justify-center">
                    <img src="https://unpkg.com/lucide-static@latest/icons/log-out.svg" alt="退出"
                         class="w-5 h-5 mr-2" style="filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);">
                    <span>退出登录</span>
                </div>
            </div>
        </div>

        <!-- 底部间距 -->
        <div class="h-16"></div>
    </div>

    <!-- 未登录状态的布局 -->
    <div id="notLoggedInLayout" class="not-logged-in-layout main-content hidden">
        <div class="p-4 w-full max-w-md mx-auto">
            <!-- 未登录用户信息卡片 -->
            <div class="ink-card p-6 text-center scroll-animate">
                <div class="flex flex-col items-center">
                    <!-- 默认头像 -->
                    <div class="default-avatar mb-4">
                        <img src="https://unpkg.com/lucide-static@latest/icons/user.svg" alt="未登录">
                    </div>

                    <!-- 未登录提示 -->
                    <h2 class="text-xl font-bold text-gray-400 mb-2">未登录</h2>
                    <p class="login-prompt">登录后可享受更多江湖功能</p>

                    <!-- 微信登录按钮 -->
                    <div class="wechat-login-btn" onclick="handleWechatLogin()">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40NzcgMiAyIDYuNDc3IDIgMTJTNi40NzcgMjIgMTIgMjJTMjIgMTcuNTIzIDIyIDEyUzE3LjUyMyAyIDEyIDJaTTEwLjUgOEMxMS4zMjggOCAxMiA4LjY3MiAxMiA5LjVTMTEuMzI4IDExIDEwLjUgMTFTOSAxMC4zMjggOSA5LjVTOS42NzIgOCAxMC41IDhaTTEzLjUgOEMxNC4zMjggOCAxNSA4LjY3MiAxNSA5LjVTMTQuMzI4IDExIDEzLjUgMTFTMTIgMTAuMzI4IDEyIDkuNVMxMi42NzIgOCAxMy41IDhaTTEyIDE4QzEwLjM0MyAxOCA4Ljg5MSAxNi44OTEgOC4yMTkgMTUuMzQ0QzguNjU2IDE1LjEyNSA5LjE1NiAxNSA5LjY4OCAxNUgxNC4zMTJDMTQuODQ0IDE1IDE1LjM0NCAxNS4xMjUgMTUuNzgxIDE1LjM0NEMxNS4xMDkgMTYuODkxIDEzLjY1NyAxOCAxMiAxOFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=" alt="微信">
                        <span>微信一键登录</span>
                    </div>

                    <!-- 关于我们按钮 -->
                    <div class="about-btn-simple" onclick="handleFunctionClick('about')">
                        <img src="https://unpkg.com/lucide-static@latest/icons/info.svg" alt="关于我们">
                        <span>关于我们</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮测试按钮 -->
    <div class="floating-test-btn" id="testToggleBtn" onclick="toggleLoginState()">
        <span id="testBtnText">切换到未登录</span>
    </div>

    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 移除滚动动画控制器 - 不再需要

        // 用户身份管理
        class UserManager {
            constructor() {
                this.isLoggedIn = true; // 登录状态，默认已登录用于测试
                this.isAdmin = true; // 模拟用户身份，实际应从后端获取
                this.initUserInterface();
            }

            initUserInterface() {
                this.updateLoginState();
                this.updateAdminState();
            }

            updateLoginState() {
                const loggedInLayout = document.getElementById('loggedInLayout');
                const notLoggedInLayout = document.getElementById('notLoggedInLayout');
                const testBtnText = document.getElementById('testBtnText');

                if (this.isLoggedIn) {
                    // 显示已登录布局，隐藏未登录布局
                    loggedInLayout.classList.remove('hidden');
                    notLoggedInLayout.classList.add('hidden');

                    // 更新测试按钮文字
                    testBtnText.textContent = '切换到未登录';
                } else {
                    // 显示未登录布局，隐藏已登录布局
                    loggedInLayout.classList.add('hidden');
                    notLoggedInLayout.classList.remove('hidden');

                    // 更新测试按钮文字
                    testBtnText.textContent = '切换到已登录';
                }
            }

            updateAdminState() {
                // 只在已登录状态下处理管理员功能
                if (!this.isLoggedIn) return;

                const adminSection = document.getElementById('adminSection');
                const adminBadge = document.getElementById('adminBadge');
                const normalBadge = document.getElementById('normalBadge');

                if (this.isAdmin) {
                    adminSection.classList.remove('hidden');
                    adminBadge.classList.remove('hidden');
                    normalBadge.classList.add('hidden');
                } else {
                    adminSection.classList.add('hidden');
                    adminBadge.classList.add('hidden');
                    normalBadge.classList.remove('hidden');
                }
            }

            toggleLoginState() {
                this.isLoggedIn = !this.isLoggedIn;
                this.initUserInterface();
                console.log('登录状态已切换:', this.isLoggedIn ? '已登录' : '未登录');
            }

            login() {
                this.isLoggedIn = true;
                this.initUserInterface();
                console.log('用户已登录');
            }

            logout() {
                this.isLoggedIn = false;
                this.initUserInterface();
                console.log('用户已退出登录');
            }

            toggleAdminMode() {
                this.isAdmin = !this.isAdmin;
                this.updateAdminState();
                console.log('用户身份已更新:', this.isAdmin ? '管理员' : '普通用户');
            }
        }

        // 功能处理函数
        function handleFunctionClick(functionType) {
            console.log('点击功能:', functionType);

            switch(functionType) {
                case 'profile':
                    alert('个人资料功能开发中...');
                    break;
                case 'history':
                    alert('历史记录功能开发中...');
                    break;
                case 'settings':
                    alert('设置功能开发中...');
                    break;
                case 'about':
                    alert('关于我们：\n\n燕友圈 - 江湖风云榜\n一个充满古典武侠韵味的排行榜应用\n\n版本：v1.0.0\n开发团队：燕友圈工作室');
                    break;
            }
        }

        function handleAdminFunction(functionType) {
            console.log('点击管理员功能:', functionType);

            switch(functionType) {
                case 'userManage':
                    alert('用户管理功能开发中...');
                    break;
                case 'rankingManage':
                    alert('榜单管理功能开发中...');
                    break;
                case 'broadcastManage':
                    alert('播报管理功能开发中...');
                    break;
                case 'partnerManage':
                    alert('合作伙伴管理功能开发中...');
                    break;
                case 'systemSettings':
                    alert('系统设置功能开发中...');
                    break;
            }
        }

        function handleLogout() {
            if (confirm('确定要退出登录吗？')) {
                userManager.logout();
                alert('退出成功！');
            }
        }

        // 微信登录处理
        function handleWechatLogin() {
            console.log('开始微信登录...');

            // 模拟微信登录过程
            const loginSteps = [
                '正在唤起微信授权...',
                '正在获取用户信息...',
                '正在验证身份...',
                '登录成功！'
            ];

            let currentStep = 0;
            const showStep = () => {
                if (currentStep < loginSteps.length - 1) {
                    alert(loginSteps[currentStep]);
                    currentStep++;
                    setTimeout(showStep, 800);
                } else {
                    alert(loginSteps[currentStep]);
                    // 登录成功，更新状态
                    userManager.login();
                }
            };

            showStep();
        }

        // 测试状态切换
        function toggleLoginState() {
            userManager.toggleLoginState();
        }

        // 全局用户管理器实例
        let userManager;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化用户管理器
            userManager = new UserManager();

            // 初始化标签栏
            initTabbar();
        });

        // 初始化标签栏
        function initTabbar() {
            const tabbarIframe = document.getElementById('tabbarIframe');

            // 等待iframe加载完成
            tabbarIframe.addEventListener('load', () => {
                // 向标签栏发送消息，设置当前活动标签为"我的"
                setTimeout(() => {
                    tabbarIframe.contentWindow.postMessage({
                        type: 'setActiveTab',
                        tab: 'user'
                    }, '*');
                }, 100);
            });
        }

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            // 允许来自同源或iframe的消息
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);

                switch(event.data.tab) {
                    case 'home':
                        console.log('切换到首页');
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        console.log('当前就是用户页');
                        window.scrollTo({ top: 0 });
                        break;
                }
            }
        });
    </script>
</body>
</html>
