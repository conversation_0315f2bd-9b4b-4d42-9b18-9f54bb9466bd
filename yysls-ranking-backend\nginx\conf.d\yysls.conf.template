# 燕友圈榜单系统 - 站点配置模板
# 使用方法：复制此文件为 yysls.conf 并修改相应的域名和配置

# HTTP 服务器配置（重定向到HTTPS）
server {
    listen 80;
    # 🔧 修改为你的实际域名
    server_name {{DOMAIN_NAME}} www.{{DOMAIN_NAME}};
    
    # 健康检查端点（不重定向）
    location /health {
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 其他请求重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS 服务器配置
server {
    listen 443 ssl;
    http2 on;
    # 🔧 修改为你的实际域名
    server_name {{DOMAIN_NAME}} www.{{DOMAIN_NAME}};
    
    # SSL证书配置
    # 🔧 确保证书文件路径正确
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # API接口代理
    location /api/ {
        # 限流配置 - 🔧 根据需要调整
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 登录接口特殊限流
    location /api/v1/auth/login {
        # 🔧 根据安全需求调整限流
        limit_req zone=login burst=3 nodelay;
        
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文档接口 - 🔧 生产环境可能需要限制访问
    location /docs {
        # 可选：限制访问IP
        # allow ***********/24;
        # deny all;
        
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /redoc {
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /openapi.json {
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务
    location /uploads/ {
        alias /app/uploads/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        
        # 安全设置
        location ~* \.(php|jsp|cgi|asp|aspx)$ {
            deny all;
        }
    }
    
    # 健康检查
    location /health {
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        access_log off;
    }
    
    # 根路径
    location / {
        proxy_pass http://yysls_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
