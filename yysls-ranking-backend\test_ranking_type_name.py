#!/usr/bin/env python3
"""
测试榜单类型中文名称功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schemas.ranking import RankingResponse, get_ranking_type_display_name
from app.models.ranking import RankingType, RankingStatus
from datetime import datetime


def test_ranking_type_display_name():
    """测试榜单类型中文名称映射函数"""
    print("测试榜单类型中文名称映射函数...")
    
    # 测试5人榜单
    five_person_name = get_ranking_type_display_name(RankingType.FIVE_PERSON)
    print(f"5人榜单类型: {RankingType.FIVE_PERSON} -> {five_person_name}")
    assert five_person_name == "5人榜单", f"期望 '5人榜单'，实际得到 '{five_person_name}'"
    
    # 测试10人榜单
    ten_person_name = get_ranking_type_display_name(RankingType.TEN_PERSON)
    print(f"10人榜单类型: {RankingType.TEN_PERSON} -> {ten_person_name}")
    assert ten_person_name == "10人榜单", f"期望 '10人榜单'，实际得到 '{ten_person_name}'"
    
    print("✅ 榜单类型中文名称映射函数测试通过")


def test_ranking_response_model():
    """测试RankingResponse模型"""
    print("\n测试RankingResponse模型...")
    
    # 创建测试数据
    test_data = {
        "id": 1,
        "name": "测试榜单",
        "period": 1,
        "ranking_type": RankingType.FIVE_PERSON,
        "start_time": datetime.now(),
        "end_time": datetime.now(),
        "team_size_limit": 5,
        "total_participants": 10,
        "status": RankingStatus.IN_PROGRESS,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
    }
    
    # 创建RankingResponse实例
    ranking_response = RankingResponse(**test_data)
    
    # 手动设置中文名称（模拟API中的逻辑）
    ranking_response.ranking_type_name = get_ranking_type_display_name(ranking_response.ranking_type)
    
    print(f"榜单ID: {ranking_response.id}")
    print(f"榜单名称: {ranking_response.name}")
    print(f"榜单类型: {ranking_response.ranking_type}")
    print(f"榜单类型中文名称: {ranking_response.ranking_type_name}")
    
    # 验证中文名称是否正确设置
    assert ranking_response.ranking_type_name == "5人榜单", f"期望 '5人榜单'，实际得到 '{ranking_response.ranking_type_name}'"
    
    print("✅ RankingResponse模型测试通过")


def test_ranking_response_json():
    """测试RankingResponse模型的JSON序列化"""
    print("\n测试RankingResponse模型的JSON序列化...")
    
    # 创建测试数据
    test_data = {
        "id": 2,
        "name": "10人竞速榜单",
        "period": 2,
        "ranking_type": RankingType.TEN_PERSON,
        "start_time": datetime.now(),
        "end_time": datetime.now(),
        "team_size_limit": 10,
        "total_participants": 20,
        "status": RankingStatus.FINISHED,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
    }
    
    # 创建RankingResponse实例
    ranking_response = RankingResponse(**test_data)
    ranking_response.ranking_type_name = get_ranking_type_display_name(ranking_response.ranking_type)
    
    # 转换为字典
    response_dict = ranking_response.model_dump()
    
    print(f"JSON响应数据:")
    print(f"  id: {response_dict['id']}")
    print(f"  name: {response_dict['name']}")
    print(f"  ranking_type: {response_dict['ranking_type']}")
    print(f"  ranking_type_name: {response_dict['ranking_type_name']}")
    print(f"  status: {response_dict['status']}")
    
    # 验证字段是否存在
    assert "ranking_type_name" in response_dict, "响应数据中缺少 ranking_type_name 字段"
    assert response_dict["ranking_type_name"] == "10人榜单", f"期望 '10人榜单'，实际得到 '{response_dict['ranking_type_name']}'"
    
    print("✅ RankingResponse模型JSON序列化测试通过")


if __name__ == "__main__":
    print("开始测试榜单类型中文名称功能...\n")
    
    try:
        test_ranking_type_display_name()
        test_ranking_response_model()
        test_ranking_response_json()
        
        print("\n🎉 所有测试通过！榜单类型中文名称功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        sys.exit(1)
