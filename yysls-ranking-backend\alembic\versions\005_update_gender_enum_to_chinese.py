"""更新用户性别枚举为中文

Revision ID: 005
Revises: 004
Create Date: 2024-08-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：将用户性别枚举值从英文更新为中文"""
    
    # 首先更新现有数据
    connection = op.get_bind()
    
    # 更新现有的性别数据
    connection.execute(sa.text("""
        UPDATE users 
        SET gender = CASE 
            WHEN gender = 'male' THEN '男'
            WHEN gender = 'female' THEN '女'
            WHEN gender = 'other' THEN '不愿意透露'
            WHEN gender = 'prefer_not_to_say' THEN '不愿意透露'
            ELSE gender
        END
        WHERE gender IS NOT NULL
    """))
    
    # 删除旧的约束
    op.drop_constraint('check_gender_values', 'users', type_='check')
    
    # 添加新的约束
    op.create_check_constraint(
        'check_gender_values',
        'users',
        "gender IS NULL OR gender IN ('男', '女', '不愿意透露')"
    )


def downgrade() -> None:
    """降级数据库：将用户性别枚举值从中文恢复为英文"""
    
    # 首先更新现有数据
    connection = op.get_bind()
    
    # 更新现有的性别数据
    connection.execute(sa.text("""
        UPDATE users 
        SET gender = CASE 
            WHEN gender = '男' THEN 'male'
            WHEN gender = '女' THEN 'female'
            WHEN gender = '不愿意透露' THEN 'prefer_not_to_say'
            ELSE gender
        END
        WHERE gender IS NOT NULL
    """))
    
    # 删除新的约束
    op.drop_constraint('check_gender_values', 'users', type_='check')
    
    # 恢复旧的约束
    op.create_check_constraint(
        'check_gender_values',
        'users',
        "gender IS NULL OR gender IN ('male', 'female', 'other', 'prefer_not_to_say')"
    )
