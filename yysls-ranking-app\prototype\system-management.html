<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: url('../static/home/<USER>') no-repeat center top fixed;
            background-size: cover;
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        /* 背景遮罩层 */
        .bg-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(26, 26, 26, 0.3) 0%,
                rgba(26, 26, 26, 0.6) 50%,
                rgba(26, 26, 26, 0.8) 100%
            );
            z-index: -1;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 返回按钮样式 - 参考 about.html */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 开关控件样式 */
        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px;
            background: rgba(74, 74, 74, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 12px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        
        .switch-container:hover {
            background: rgba(74, 74, 74, 0.3);
            border-color: rgba(212, 175, 55, 0.2);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.1);
        }
        
        .switch-info {
            flex: 1;
        }
        
        .switch-title {
            font-size: 18px;
            font-weight: 600;
            color: #F5F5DC;
            margin-bottom: 4px;
        }
        
        .switch-description {
            font-size: 14px;
            color: #B8B8B8;
            line-height: 1.4;
        }
        
        /* 自定义开关样式 */
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 32px;
            background: rgba(74, 74, 74, 0.6);
            border-radius: 16px;
            border: 2px solid rgba(212, 175, 55, 0.2);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            margin-left: 16px;
        }
        
        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: #F5F5DC;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.8) 0%, rgba(212, 175, 55, 0.6) 100%);
            border-color: rgba(212, 175, 55, 0.8);
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
        }
        
        .toggle-switch.active::before {
            transform: translateX(28px);
            background: #FFFFFF;
            box-shadow: 0 2px 12px rgba(212, 175, 55, 0.4);
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-top: 8px;
        }
        
        .status-indicator.enabled {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.4);
            color: #22C55E;
        }
        
        .status-indicator.disabled {
            background: rgba(156, 163, 175, 0.2);
            border: 1px solid rgba(156, 163, 175, 0.4);
            color: #9CA3AF;
        }
        
        /* 主内容区域 - 参考 about.html */
        .main-content {
            padding: 80px 20px 140px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 浮动标签栏容器样式 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 页面标题样式 */
        .page-title {
            text-align: center;
            padding: 24px 0;
        }
        
        .page-title h1 {
            font-size: 28px;
            font-weight: 700;
            color: #D4AF37;
            text-shadow: 
                3px 3px 6px rgba(0, 0, 0, 0.8), 
                2px 2px 4px rgba(0, 0, 0, 0.9), 
                0 0 16px rgba(212, 175, 55, 0.6);
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 16px;
            color: #B8B8B8;
            font-weight: 500;
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 背景遮罩层 -->
    <div class="bg-overlay"></div>
    
    <!-- 返回按钮 - 参考 about.html 设计 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-title">
            <h1>系统管理中心</h1>
            <p class="page-subtitle">管理系统核心功能开关</p>
        </div>

        <!-- 系统功能开关区域 -->
        <div class="space-y-6">
            <!-- 功能开关卡片 -->
            <div class="ink-card p-6">
                <div class="flex items-center space-x-2 mb-6">
                    <img src="https://unpkg.com/lucide-static@latest/icons/settings.svg" alt="设置" 
                         class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <h3 class="text-xl font-semibold gold-text">功能开关</h3>
                </div>
                
                <!-- 播报功能开关 -->
                <div class="switch-container">
                    <div class="switch-info">
                        <div class="switch-title">播报功能</div>
                        <div class="switch-description">控制首页滚动播报信息的显示与隐藏</div>
                        <div class="status-indicator enabled" id="broadcast-status">
                            <span>●</span>
                            <span class="ml-1">已开启</span>
                        </div>
                    </div>
                    <div class="toggle-switch active" id="broadcast-toggle" onclick="toggleSwitch('broadcast')"></div>
                </div>
                
                <!-- 合作伙伴功能开关 -->
                <div class="switch-container">
                    <div class="switch-info">
                        <div class="switch-title">合作伙伴功能</div>
                        <div class="switch-description">控制合作伙伴展示区域的显示与隐藏</div>
                        <div class="status-indicator enabled" id="partner-status">
                            <span>●</span>
                            <span class="ml-1">已开启</span>
                        </div>
                    </div>
                    <div class="toggle-switch active" id="partner-toggle" onclick="toggleSwitch('partner')"></div>
                </div>
                
                <!-- 广告招租功能开关 -->
                <div class="switch-container">
                    <div class="switch-info">
                        <div class="switch-title">广告招租功能</div>
                        <div class="switch-description">控制广告招租横幅的显示与隐藏</div>
                        <div class="status-indicator disabled" id="ad-status">
                            <span>●</span>
                            <span class="ml-1">已关闭</span>
                        </div>
                    </div>
                    <div class="toggle-switch" id="ad-toggle" onclick="toggleSwitch('ad')"></div>
                </div>
            </div>
            
            <!-- 操作说明卡片 -->
            <div class="ink-card p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <img src="https://unpkg.com/lucide-static@latest/icons/info.svg" alt="说明" 
                         class="w-5 h-5" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                    <h4 class="text-lg font-semibold gold-text">操作说明</h4>
                </div>
                <div class="space-y-3 text-sm text-gray-300 leading-relaxed">
                    <p>• <strong class="text-gray-200">播报功能：</strong>控制首页顶部滚动播报信息的显示，关闭后用户将看不到最新公告</p>
                    <p>• <strong class="text-gray-200">合作伙伴功能：</strong>控制首页合作伙伴展示区域，关闭后该模块将从首页隐藏</p>
                    <p>• <strong class="text-gray-200">广告招租功能：</strong>控制广告招租横幅的显示，可根据运营需要灵活开启或关闭</p>
                    <p class="text-yellow-400 mt-4">⚠️ 功能开关修改后将立即生效，请谨慎操作</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent;">
        </iframe>
    </div>
    
    <script>
        // 系统功能开关管理
        class SystemManager {
            constructor() {
                // 初始化功能状态（实际项目中应从后端获取）
                this.functionStates = {
                    broadcast: true,    // 播报功能
                    partner: true,      // 合作伙伴功能
                    ad: false          // 广告招租功能
                };
                
                this.initSwitches();
            }
            
            initSwitches() {
                // 根据状态初始化开关UI
                Object.keys(this.functionStates).forEach(key => {
                    this.updateSwitchUI(key, this.functionStates[key]);
                });
            }
            
            toggleFunction(functionName) {
                // 切换功能状态
                this.functionStates[functionName] = !this.functionStates[functionName];
                const newState = this.functionStates[functionName];
                
                // 更新UI
                this.updateSwitchUI(functionName, newState);
                
                // 显示操作反馈
                this.showFeedback(functionName, newState);
                
                // 实际项目中这里应该调用API保存状态
                console.log(`${functionName} 功能已${newState ? '开启' : '关闭'}`);
            }
            
            updateSwitchUI(functionName, isEnabled) {
                const toggle = document.getElementById(`${functionName}-toggle`);
                const status = document.getElementById(`${functionName}-status`);
                
                if (isEnabled) {
                    toggle.classList.add('active');
                    status.classList.remove('disabled');
                    status.classList.add('enabled');
                    status.innerHTML = '<span>●</span><span class="ml-1">已开启</span>';
                } else {
                    toggle.classList.remove('active');
                    status.classList.remove('enabled');
                    status.classList.add('disabled');
                    status.innerHTML = '<span>●</span><span class="ml-1">已关闭</span>';
                }
            }
            
            showFeedback(functionName, isEnabled) {
                const functionNames = {
                    broadcast: '播报功能',
                    partner: '合作伙伴功能',
                    ad: '广告招租功能'
                };
                
                const message = `${functionNames[functionName]}已${isEnabled ? '开启' : '关闭'}`;
                
                // 创建临时提示元素
                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(26, 26, 26, 0.95);
                    color: #D4AF37;
                    padding: 16px 24px;
                    border-radius: 8px;
                    border: 1px solid rgba(212, 175, 55, 0.3);
                    z-index: 10000;
                    font-weight: 600;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(10px);
                `;
                feedback.textContent = message;
                
                document.body.appendChild(feedback);
                
                // 2秒后移除提示
                setTimeout(() => {
                    document.body.removeChild(feedback);
                }, 2000);
            }
        }
        
        // 全局系统管理器实例
        let systemManager;
        
        // 开关切换函数
        function toggleSwitch(functionName) {
            systemManager.toggleFunction(functionName);
        }
        
        // 返回按钮处理 - 参考 about.html
        function goBack() {
            window.history.back();
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化系统管理器
            systemManager = new SystemManager();
            
            // 初始化标签栏
            initTabbar();
        });
        
        // 初始化标签栏
        function initTabbar() {
            const tabbarIframe = document.getElementById('tabbarIframe');
            
            // 等待iframe加载完成
            tabbarIframe.addEventListener('load', () => {
                // 向标签栏发送消息，设置当前活动标签
                setTimeout(() => {
                    tabbarIframe.contentWindow.postMessage({
                        type: 'setActiveTab',
                        tab: 'user' // 系统管理属于用户相关功能
                    }, '*');
                }, 100);
            });
        }
        
        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }
            
            if (event.data && event.data.type === 'tabchange') {
                console.log('收到标签栏切换事件:', event.data);
                
                switch(event.data.tab) {
                    case 'home':
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
