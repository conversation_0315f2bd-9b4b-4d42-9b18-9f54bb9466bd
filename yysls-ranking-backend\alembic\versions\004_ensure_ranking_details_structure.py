"""Ensure ranking_details table structure matches current model

Revision ID: 004_ensure_ranking_details_structure
Revises: 003_add_team_name_to_ranking_details
Create Date: 2024-12-19 19:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004_ensure_ranking_details_structure'
down_revision = '003_add_team_name_to_ranking_details'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：确保ranking_details表结构与当前模型一致"""
    
    # 检查并添加可能缺少的字段
    # 根据当前模型，确保所有字段都存在
    
    # 检查并添加rank_start字段（如果不存在）
    try:
        op.add_column('ranking_details', sa.Column('rank_start', sa.Integer(), nullable=False, comment='排名开始'))
    except Exception:
        # 字段可能已存在，忽略错误
        pass
    
    # 检查并添加rank_end字段（如果不存在）
    try:
        op.add_column('ranking_details', sa.Column('rank_end', sa.Integer(), nullable=False, comment='排名结束'))
    except Exception:
        # 字段可能已存在，忽略错误
        pass
    
    # 检查并添加completion_time字段（如果不存在）
    try:
        op.add_column('ranking_details', sa.Column('completion_time', sa.Time(), nullable=False, comment='完成时间(分秒)'))
    except Exception:
        # 字段可能已存在，忽略错误
        pass
    
    # 检查并添加participant_count字段（如果不存在）
    try:
        op.add_column('ranking_details', sa.Column('participant_count', sa.Integer(), nullable=False, comment='当前时间区间参与人数'))
    except Exception:
        # 字段可能已存在，忽略错误
        pass
    
    # 检查并添加team_info字段（如果不存在）
    try:
        op.add_column('ranking_details', sa.Column('team_info', sa.Text(), nullable=True, comment='队伍信息(JSON格式)'))
    except Exception:
        # 字段可能已存在，忽略错误
        pass
    
    # team_name字段应该已经在003迁移中添加了
    
    # 检查并创建索引（如果不存在）
    try:
        op.create_index('idx_completion_seconds', 'ranking_details', ['completion_seconds'])
    except Exception:
        # 索引可能已存在，忽略错误
        pass
    
    try:
        op.create_index('idx_created_at', 'ranking_details', ['created_at'])
    except Exception:
        # 索引可能已存在，忽略错误
        pass
    
    try:
        op.create_index('idx_ranking_id', 'ranking_details', ['ranking_id'])
    except Exception:
        # 索引可能已存在，忽略错误
        pass


def downgrade() -> None:
    """降级数据库：移除添加的字段和索引"""
    
    # 删除索引
    try:
        op.drop_index('idx_completion_seconds', table_name='ranking_details')
    except Exception:
        pass
    
    try:
        op.drop_index('idx_created_at', table_name='ranking_details')
    except Exception:
        pass
    
    try:
        op.drop_index('idx_ranking_id', table_name='ranking_details')
    except Exception:
        pass
    
    # 删除字段（注意：这可能会导致数据丢失）
    try:
        op.drop_column('ranking_details', 'team_info')
    except Exception:
        pass
    
    try:
        op.drop_column('ranking_details', 'participant_count')
    except Exception:
        pass
    
    try:
        op.drop_column('ranking_details', 'completion_time')
    except Exception:
        pass
    
    try:
        op.drop_column('ranking_details', 'rank_end')
    except Exception:
        pass
    
    try:
        op.drop_column('ranking_details', 'rank_start')
    except Exception:
        pass
