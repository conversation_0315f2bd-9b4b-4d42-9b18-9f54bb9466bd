<template>
	<view 
		class="floating-tabbar-container" 
		:class="{ 'show': isVisible }"
		:style="{ 
			bottom: bottom + 'rpx',
			left: left + 'rpx',
			right: right + 'rpx'
		}"
	>
		<view class="floating-tabbar" :style="{ maxWidth: maxWidth + 'rpx' }">
			<template v-for="(item, index) in tabList" :key="index">
				<view 
					class="floating-tab-item" 
					:class="{ 'active': currentIndex === index }"
					:data-tab="item.name"
					@click="handleTabClick(index, item)"
				>
					<view class="floating-tab-icon-container">
						<view class="floating-tab-indicator"></view>
						<sx-svg 
							:name="item.icon" 
							:size="iconSize"
							:color="currentIndex === index ? activeColor : inactiveColor"
							class="floating-tab-icon"
						/>
						<!-- 徽章 -->
						<view 
							v-if="item.badge && item.badge > 0" 
							class="floating-tab-badge"
						>
							<text class="floating-tab-badge-text">
								{{ item.badge > 99 ? '99+' : item.badge }}
							</text>
						</view>
					</view>
					<text 
						class="floating-tab-text"
						:style="{ 
							color: currentIndex === index ? activeColor : inactiveColor,
							fontSize: textSize + 'rpx'
						}"
					>
						{{ item.text }}
					</text>
				</view>
				
				<!-- 分割线 (不在最后一个item后显示) -->
				<view 
					v-if="index < tabList.length - 1 && showDivider" 
					class="tab-divider"
				></view>
			</template>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

const props = defineProps({
	// 标签列表
	tabList: {
		type: Array,
		default: () => [
			{
				name: 'home',
				text: '榜单',
				icon: 'trophy',
				badge: 0
			},
			{
				name: 'user',
				text: '我的',
				icon: 'user',
				badge: 0
			}
		]
	},
	// 当前选中索引
	current: {
		type: Number,
		default: 0
	},
	// 激活颜色
	activeColor: {
		type: String,
		default: '#D4AF37'
	},
	// 未激活颜色
	inactiveColor: {
		type: String,
		default: '#9CA3AF'
	},
	// 图标大小
	iconSize: {
		type: Number,
		default: 48
	},
	// 文字大小
	textSize: {
		type: Number,
		default: 20
	},
	// 底部距离
	bottom: {
		type: Number,
		default: 40
	},
	// 左右边距
	left: {
		type: Number,
		default: 40
	},
	right: {
		type: Number,
		default: 40
	},
	// 最大宽度
	maxWidth: {
		type: Number,
		default: 560
	},
	// 是否显示分割线
	showDivider: {
		type: Boolean,
		default: true
	},
	// 是否自动显示
	autoShow: {
		type: Boolean,
		default: true
	}
})

const emit = defineEmits(['change', 'click'])

// 当前选中索引
const currentIndex = ref(props.current)
// 是否显示
const isVisible = ref(false)

// 处理标签点击
const handleTabClick = (index, item) => {
	if (currentIndex.value === index) return
	
	// 添加点击动画效果
	addRippleEffect(index)
	
	// 更新选中状态
	currentIndex.value = index
	
	// 触发事件
	emit('change', {
		index,
		item,
		current: index
	})
	
	emit('click', {
		index,
		item,
		current: index
	})
}

// 添加点击波纹效果
const addRippleEffect = (index) => {
	// 这里可以添加更复杂的动画效果
	console.log('Tab clicked:', index)
}

// 显示tabbar
const show = () => {
	isVisible.value = true
}

// 隐藏tabbar
const hide = () => {
	isVisible.value = false
}

// 设置当前选中项
const setCurrentIndex = (index) => {
	if (index >= 0 && index < props.tabList.length) {
		currentIndex.value = index
	}
}

// 根据名称设置当前选中项
const setCurrentByName = (name) => {
	const index = props.tabList.findIndex(item => item.name === name)
	if (index !== -1) {
		setCurrentIndex(index)
	}
}

// 设置徽章
const setBadge = (index, count) => {
	if (index >= 0 && index < props.tabList.length) {
		props.tabList[index].badge = count
	}
}

// 根据名称设置徽章
const setBadgeByName = (name, count) => {
	const index = props.tabList.findIndex(item => item.name === name)
	if (index !== -1) {
		setBadge(index, count)
	}
}

// 暴露方法给父组件
defineExpose({
	show,
	hide,
	setCurrentIndex,
	setCurrentByName,
	setBadge,
	setBadgeByName
})

// 组件挂载后自动显示
onMounted(() => {
	if (props.autoShow) {
		nextTick(() => {
			setTimeout(() => {
				show()
			}, 100)
		})
	}
})
</script>

<style scoped>
/* 悬浮式 Tabbar 容器 */
.floating-tabbar-container {
	position: fixed;
	z-index: 9999;
	transform: translateY(400rpx);
	opacity: 0;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.floating-tabbar-container.show {
	transform: translateY(0);
	opacity: 1;
}

/* 悬浮式 Tabbar 主体 */
.floating-tabbar {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 120rpx;
	border-radius: 60rpx;
	background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(74, 74, 74, 0.85) 100%);
	backdrop-filter: blur(40rpx);
	border: 2rpx solid rgba(212, 175, 55, 0.2);
	box-shadow:
		0 16rpx 64rpx rgba(0, 0, 0, 0.4),
		0 8rpx 32rpx rgba(0, 0, 0, 0.3),
		inset 0 2rpx 0 rgba(212, 175, 55, 0.1);
	position: relative;
	overflow: hidden;
	animation: float-glow 4s ease-in-out infinite;
	margin: 0 auto;
	padding: 0 40rpx;
}

/* 背景装饰效果 */
.floating-tabbar::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		135deg,
		rgba(212, 175, 55, 0.05) 0%,
		transparent 50%,
		rgba(212, 175, 55, 0.03) 100%
	);
	pointer-events: none;
}

/* Tab项目样式 */
.floating-tab-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
	height: 100%;
	position: relative;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	margin: 0 30rpx;
}

.floating-tab-item:active {
	transform: scale(0.95);
}

/* 分割线样式 */
.tab-divider {
	width: 4rpx;
	height: 70rpx;
	background: linear-gradient(
		to bottom,
		transparent 0%,
		rgba(212, 175, 55, 0.2) 15%,
		rgba(212, 175, 55, 0.5) 30%,
		rgba(212, 175, 55, 0.8) 50%,
		rgba(212, 175, 55, 0.5) 70%,
		rgba(212, 175, 55, 0.2) 85%,
		transparent 100%
	);
	position: relative;
	flex-shrink: 0;
	border-radius: 2rpx;
	box-shadow:
		0 0 16rpx rgba(212, 175, 55, 0.3),
		inset 0 0 4rpx rgba(212, 175, 55, 0.4);
}

.tab-divider::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 8rpx;
	height: 8rpx;
	background: radial-gradient(circle, rgba(212, 175, 55, 1) 0%, rgba(212, 175, 55, 0.6) 70%, transparent 100%);
	border-radius: 50%;
	box-shadow:
		0 0 16rpx rgba(212, 175, 55, 0.6),
		0 0 8rpx rgba(212, 175, 55, 0.8);
}

.tab-divider::after {
	content: '';
	position: absolute;
	top: 20%;
	left: 50%;
	transform: translateX(-50%);
	width: 2rpx;
	height: 60%;
	background: linear-gradient(
		to bottom,
		transparent 0%,
		rgba(255, 255, 255, 0.1) 50%,
		transparent 100%
	);
}

/* 图标容器 */
.floating-tab-icon-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 8rpx;
}

/* 选中状态指示器 */
.floating-tab-indicator {
	position: absolute;
	top: -8rpx;
	left: -8rpx;
	right: -8rpx;
	bottom: -8rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
	transform: scale(0);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.floating-tab-item.active .floating-tab-indicator {
	transform: scale(1);
	box-shadow:
		0 0 20rpx rgba(212, 175, 55, 0.4),
		0 0 40rpx rgba(212, 175, 55, 0.2);
}

/* 图标样式 */
.floating-tab-icon {
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	transform: scale(1);
}

.floating-tab-item.active .floating-tab-icon {
	transform: scale(1.1);
	filter: drop-shadow(0 0 8rpx rgba(212, 175, 55, 0.6));
}

/* 徽章样式 */
.floating-tab-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	min-width: 32rpx;
	height: 32rpx;
	border-radius: 16rpx;
	background: linear-gradient(135deg, #FF4444 0%, #FF6666 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 8rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.4);
}

.floating-tab-badge-text {
	color: #FFFFFF;
	font-size: 20rpx;
	font-weight: 600;
	line-height: 1;
}

/* 文字样式 */
.floating-tab-text {
	font-weight: 500;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	text-shadow: none;
}

.floating-tab-item.active .floating-tab-text {
	font-weight: 600;
	text-shadow: 0 0 8rpx rgba(212, 175, 55, 0.4);
	transform: scale(1.05);
}

/* 悬浮动画 */
@keyframes float-glow {
	0%, 100% {
		box-shadow:
			0 16rpx 64rpx rgba(0, 0, 0, 0.4),
			0 8rpx 32rpx rgba(0, 0, 0, 0.3),
			inset 0 2rpx 0 rgba(212, 175, 55, 0.1);
	}
	50% {
		box-shadow:
			0 20rpx 80rpx rgba(0, 0, 0, 0.5),
			0 12rpx 40rpx rgba(0, 0, 0, 0.4),
			inset 0 2rpx 0 rgba(212, 175, 55, 0.15),
			0 0 40rpx rgba(212, 175, 55, 0.1);
	}
}

/* 点击波纹效果 */
.floating-tab-item::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 0;
	border-radius: 50%;
	background: rgba(212, 175, 55, 0.2);
	transform: translate(-50%, -50%);
	transition: all 0.3s ease;
	pointer-events: none;
}

.floating-tab-item.ripple::before {
	width: 120rpx;
	height: 120rpx;
	opacity: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
	.floating-tabbar-container {
		left: 20rpx;
		right: 20rpx;
	}

	.floating-tab-text {
		font-size: 18rpx;
	}

	.floating-tab-icon {
		width: 40rpx;
		height: 40rpx;
	}
}
</style>
