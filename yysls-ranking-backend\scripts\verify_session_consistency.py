#!/usr/bin/env python3
"""
数据库会话一致性验证脚本
用于检查项目中的会话使用是否一致

使用方法:
    python scripts/verify_session_consistency.py
    python scripts/verify_session_consistency.py --fix  # 自动修复简单问题
"""
import os
import re
import sys
import argparse
from pathlib import Path
from typing import List, Dict, Tuple

class SessionConsistencyChecker:
    """数据库会话一致性检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.api_path = self.project_root / "app" / "api"
        self.issues_found = []
        
    def check_file(self, file_path: Path) -> List[Dict[str, str]]:
        """检查单个文件的会话一致性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return [{"type": "error", "message": f"无法读取文件: {e}"}]
        
        issues = []
        lines = content.split('\n')
        
        # 检查1: AsyncSession导入但使用get_db
        if 'AsyncSession' in content and 'get_db' in content:
            for i, line in enumerate(lines, 1):
                if 'from sqlalchemy.ext.asyncio import AsyncSession' in line:
                    issues.append({
                        "type": "import_mismatch",
                        "line": i,
                        "message": "AsyncSession导入但项目使用同步get_db",
                        "suggestion": "改为: from sqlalchemy.orm import Session"
                    })
        
        # 检查2: 类型注解不匹配
        async_session_pattern = r'db:\s*AsyncSession\s*=\s*Depends\(get_db\)'
        for i, line in enumerate(lines, 1):
            if re.search(async_session_pattern, line):
                issues.append({
                    "type": "type_annotation_mismatch",
                    "line": i,
                    "message": "AsyncSession类型注解但使用get_db",
                    "suggestion": "改为: db: Session = Depends(get_db)"
                })
        
        # 检查3: await service调用
        await_service_pattern = r'await\s+\w+_service\.\w+'
        for i, line in enumerate(lines, 1):
            if re.search(await_service_pattern, line):
                issues.append({
                    "type": "async_call_sync_method",
                    "line": i,
                    "message": "异步调用同步服务方法",
                    "suggestion": "移除await关键字"
                })
        
        # 检查4: 混合使用AsyncSession和Session
        has_async_session = 'AsyncSession' in content
        has_session = 'from sqlalchemy.orm import Session' in content
        if has_async_session and has_session:
            issues.append({
                "type": "mixed_session_types",
                "line": 0,
                "message": "文件中混合使用AsyncSession和Session",
                "suggestion": "统一使用Session类型"
            })
        
        return issues
    
    def check_all_files(self) -> Dict[str, List[Dict[str, str]]]:
        """检查所有API文件"""
        if not self.api_path.exists():
            print(f"❌ 未找到API目录: {self.api_path}")
            return {}
        
        results = {}
        
        for py_file in self.api_path.rglob('*.py'):
            if py_file.name.startswith('__'):
                continue
                
            issues = self.check_file(py_file)
            if issues:
                relative_path = py_file.relative_to(self.project_root)
                results[str(relative_path)] = issues
        
        return results
    
    def fix_simple_issues(self, file_path: Path) -> bool:
        """自动修复简单的问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复1: AsyncSession导入
            content = re.sub(
                r'from sqlalchemy\.ext\.asyncio import AsyncSession',
                'from sqlalchemy.orm import Session',
                content
            )
            
            # 修复2: 类型注解
            content = re.sub(
                r'db:\s*AsyncSession\s*=\s*Depends\(get_db\)',
                'db: Session = Depends(get_db)',
                content
            )
            
            # 如果有修改，写回文件
            if content != original_content:
                # 备份原文件
                backup_path = file_path.with_suffix('.py.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 写入修复后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已修复: {file_path} (备份: {backup_path})")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")
            return False
    
    def generate_report(self, results: Dict[str, List[Dict[str, str]]]) -> str:
        """生成检查报告"""
        if not results:
            return "✅ 所有文件的数据库会话使用都是一致的！"
        
        report = ["🔍 数据库会话一致性检查报告", "=" * 50, ""]
        
        total_files = len(results)
        total_issues = sum(len(issues) for issues in results.values())
        
        report.extend([
            f"📊 统计信息:",
            f"  问题文件数: {total_files}",
            f"  问题总数: {total_issues}",
            ""
        ])
        
        # 按问题类型分组统计
        issue_types = {}
        for file_issues in results.values():
            for issue in file_issues:
                issue_type = issue.get('type', 'unknown')
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        report.extend([
            "📋 问题类型分布:",
            *[f"  {issue_type}: {count}个" for issue_type, count in issue_types.items()],
            ""
        ])
        
        # 详细问题列表
        report.append("📁 详细问题列表:")
        for file_path, issues in results.items():
            report.append(f"\n📄 {file_path}:")
            for issue in issues:
                line_info = f" (第{issue['line']}行)" if issue.get('line', 0) > 0 else ""
                report.append(f"  ❌ {issue['message']}{line_info}")
                if 'suggestion' in issue:
                    report.append(f"     💡 建议: {issue['suggestion']}")
        
        report.extend([
            "",
            "🛠️ 修复建议:",
            "1. 运行 `python scripts/verify_session_consistency.py --fix` 自动修复简单问题",
            "2. 手动检查并移除不必要的await调用",
            "3. 参考 docs/solutions/database_session_management_fix.md 获取详细指导",
            ""
        ])
        
        return "\n".join(report)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库会话一致性检查工具')
    parser.add_argument('--fix', action='store_true', help='自动修复简单问题')
    parser.add_argument('--output', '-o', help='输出报告到文件')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    
    args = parser.parse_args()
    
    checker = SessionConsistencyChecker(args.project_root)
    
    print("🔍 开始检查数据库会话一致性...")
    results = checker.check_all_files()
    
    # 生成报告
    report = checker.generate_report(results)
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 报告已保存到: {args.output}")
    else:
        print(report)
    
    # 自动修复
    if args.fix and results:
        print("\n🔧 开始自动修复...")
        fixed_count = 0
        
        for file_path_str in results.keys():
            file_path = Path(args.project_root) / file_path_str
            if checker.fix_simple_issues(file_path):
                fixed_count += 1
        
        print(f"\n✅ 自动修复完成，共修复 {fixed_count} 个文件")
        print("⚠️  请手动检查并移除不必要的await调用")
        print("💡 建议运行测试确保修复后的代码正常工作")
    
    # 返回适当的退出码
    sys.exit(1 if results else 0)

if __name__ == "__main__":
    main()
