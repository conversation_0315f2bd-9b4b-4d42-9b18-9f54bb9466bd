#!/usr/bin/env python3
"""
API文档生成脚本

从FastAPI应用自动生成OpenAPI/Swagger文档和Markdown文档
"""
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from app.main import app
except ImportError as e:
    print(f"❌ 导入应用失败: {e}")
    print("请确保在项目根目录运行此脚本，并且已安装所有依赖")
    sys.exit(1)


def generate_openapi_json():
    """生成OpenAPI JSON文档"""
    openapi_schema = app.openapi()
    
    # 保存到文件
    docs_dir = project_root / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    with open(docs_dir / "openapi.json", "w", encoding="utf-8") as f:
        json.dump(openapi_schema, f, ensure_ascii=False, indent=2)
    
    print("✅ OpenAPI JSON文档已生成: docs/openapi.json")
    return openapi_schema


def generate_markdown_docs(openapi_schema):
    """从OpenAPI schema生成Markdown文档"""
    
    markdown_content = f"""# {openapi_schema['info']['title']} - API文档

## 📋 API概述

**版本**: {openapi_schema['info']['version']}  
**描述**: {openapi_schema['info'].get('description', '')}  
**基础URL**: `/api/v1`

## 🔐 认证方式

本API使用JWT Bearer Token认证：

```http
Authorization: Bearer <access_token>
```

## 📚 API端点列表

"""
    
    # 按标签分组端点
    paths_by_tag = {}
    for path, methods in openapi_schema['paths'].items():
        for method, details in methods.items():
            if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                tags = details.get('tags', ['未分类'])
                tag = tags[0] if tags else '未分类'
                
                if tag not in paths_by_tag:
                    paths_by_tag[tag] = []
                
                paths_by_tag[tag].append({
                    'path': path,
                    'method': method.upper(),
                    'summary': details.get('summary', ''),
                    'description': details.get('description', ''),
                    'operationId': details.get('operationId', ''),
                    'parameters': details.get('parameters', []),
                    'requestBody': details.get('requestBody', {}),
                    'responses': details.get('responses', {}),
                    'security': details.get('security', [])
                })
    
    # 生成每个标签的文档
    for tag, endpoints in paths_by_tag.items():
        markdown_content += f"\n## {tag}\n\n"
        
        for endpoint in endpoints:
            # 端点标题
            markdown_content += f"### {endpoint['method']} {endpoint['path']}\n\n"
            
            # 描述
            if endpoint['summary']:
                markdown_content += f"**描述**: {endpoint['summary']}\n\n"
            
            # 认证要求
            auth_required = bool(endpoint['security'])
            markdown_content += f"**认证**: {'需要' if auth_required else '不需要'}\n\n"
            
            # 路径参数
            path_params = [p for p in endpoint['parameters'] if p.get('in') == 'path']
            if path_params:
                markdown_content += "#### 路径参数\n\n"
                markdown_content += "| 参数 | 类型 | 必填 | 描述 |\n"
                markdown_content += "|------|------|------|------|\n"
                for param in path_params:
                    name = param['name']
                    param_type = param.get('schema', {}).get('type', 'string')
                    required = '是' if param.get('required', False) else '否'
                    description = param.get('description', '')
                    markdown_content += f"| {name} | {param_type} | {required} | {description} |\n"
                markdown_content += "\n"
            
            # 查询参数
            query_params = [p for p in endpoint['parameters'] if p.get('in') == 'query']
            if query_params:
                markdown_content += "#### 查询参数\n\n"
                markdown_content += "| 参数 | 类型 | 必填 | 默认值 | 描述 |\n"
                markdown_content += "|------|------|------|--------|------|\n"
                for param in query_params:
                    name = param['name']
                    param_type = param.get('schema', {}).get('type', 'string')
                    required = '是' if param.get('required', False) else '否'
                    default = param.get('schema', {}).get('default', '-')
                    description = param.get('description', '')
                    markdown_content += f"| {name} | {param_type} | {required} | {default} | {description} |\n"
                markdown_content += "\n"
            
            # 请求体
            if endpoint['requestBody']:
                markdown_content += "#### 请求体\n\n"
                content = endpoint['requestBody'].get('content', {})
                if 'application/json' in content:
                    schema_ref = content['application/json'].get('schema', {}).get('$ref', '')
                    if schema_ref:
                        schema_name = schema_ref.split('/')[-1]
                        markdown_content += f"请求体格式: `{schema_name}`\n\n"
                markdown_content += "```json\n{\n  // 请求体示例\n}\n```\n\n"
            
            # 响应
            if endpoint['responses']:
                markdown_content += "#### 响应示例\n\n"
                for status_code, response in endpoint['responses'].items():
                    description = response.get('description', '')
                    markdown_content += f"**{status_code}**: {description}\n\n"
                    
                    # 响应示例
                    content = response.get('content', {})
                    if 'application/json' in content:
                        markdown_content += "```json\n{\n  // 响应示例\n}\n```\n\n"
            
            markdown_content += "---\n\n"
    
    # 保存Markdown文档
    with open(project_root / "docs" / "API_REFERENCE.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("✅ Markdown API文档已生成: docs/API_REFERENCE.md")


def generate_postman_collection(openapi_schema):
    """生成Postman集合"""
    
    collection = {
        "info": {
            "name": openapi_schema['info']['title'],
            "description": openapi_schema['info'].get('description', ''),
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "auth": {
            "type": "bearer",
            "bearer": [
                {
                    "key": "token",
                    "value": "{{access_token}}",
                    "type": "string"
                }
            ]
        },
        "variable": [
            {
                "key": "base_url",
                "value": "http://localhost:8000/api/v1",
                "type": "string"
            },
            {
                "key": "access_token",
                "value": "",
                "type": "string"
            }
        ],
        "item": []
    }
    
    # 按标签分组
    folders = {}
    
    for path, methods in openapi_schema['paths'].items():
        for method, details in methods.items():
            if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                tags = details.get('tags', ['未分类'])
                tag = tags[0] if tags else '未分类'
                
                if tag not in folders:
                    folders[tag] = {
                        "name": tag,
                        "item": []
                    }
                
                # 创建请求项
                request_item = {
                    "name": details.get('summary', f"{method.upper()} {path}"),
                    "request": {
                        "method": method.upper(),
                        "header": [],
                        "url": {
                            "raw": "{{base_url}}" + path,
                            "host": ["{{base_url}}"],
                            "path": path.strip('/').split('/')
                        }
                    }
                }
                
                # 添加认证
                if details.get('security'):
                    request_item["request"]["auth"] = {
                        "type": "bearer",
                        "bearer": [
                            {
                                "key": "token",
                                "value": "{{access_token}}",
                                "type": "string"
                            }
                        ]
                    }
                
                # 添加请求体
                if details.get('requestBody'):
                    request_item["request"]["header"].append({
                        "key": "Content-Type",
                        "value": "application/json"
                    })
                    request_item["request"]["body"] = {
                        "mode": "raw",
                        "raw": "{\n  // 请求体\n}",
                        "options": {
                            "raw": {
                                "language": "json"
                            }
                        }
                    }
                
                folders[tag]["item"].append(request_item)
    
    # 添加文件夹到集合
    collection["item"] = list(folders.values())
    
    # 保存Postman集合
    with open(project_root / "docs" / "postman_collection.json", "w", encoding="utf-8") as f:
        json.dump(collection, f, ensure_ascii=False, indent=2)
    
    print("✅ Postman集合已生成: docs/postman_collection.json")


def main():
    """主函数"""
    print("🚀 开始生成API文档...")
    
    try:
        # 生成OpenAPI JSON
        openapi_schema = generate_openapi_json()
        
        # 生成Markdown文档
        generate_markdown_docs(openapi_schema)
        
        # 生成Postman集合
        generate_postman_collection(openapi_schema)
        
        print("\n🎉 API文档生成完成！")
        print("\n📁 生成的文件:")
        print("  - docs/openapi.json - OpenAPI规范文件")
        print("  - docs/API_REFERENCE.md - Markdown API文档")
        print("  - docs/postman_collection.json - Postman集合")
        print("\n💡 使用说明:")
        print("  - 在浏览器中访问 http://localhost:8000/docs 查看交互式文档")
        print("  - 将 postman_collection.json 导入Postman进行API测试")
        
    except Exception as e:
        print(f"❌ 生成文档时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
