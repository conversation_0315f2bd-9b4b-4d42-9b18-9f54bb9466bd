"""
用户会话管理工具

提供用户在线状态跟踪、多设备登录控制等功能
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class DeviceType(str, Enum):
    """设备类型"""
    WEB = "web"
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"
    UNKNOWN = "unknown"


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: str
    user_id: int
    device_type: DeviceType
    device_info: str
    ip_address: str
    user_agent: str
    login_time: datetime
    last_activity: datetime
    is_active: bool = True
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['login_time'] = self.login_time.isoformat()
        data['last_activity'] = self.last_activity.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SessionInfo':
        """从字典创建"""
        data['login_time'] = datetime.fromisoformat(data['login_time'])
        data['last_activity'] = datetime.fromisoformat(data['last_activity'])
        data['device_type'] = DeviceType(data['device_type'])
        return cls(**data)


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        # 内存存储（生产环境应使用Redis）
        self._sessions: Dict[str, SessionInfo] = {}
        self._user_sessions: Dict[int, Set[str]] = {}
        self._session_timeout = timedelta(hours=24)  # 会话超时时间
        self._max_sessions_per_user = 5  # 每用户最大会话数
    
    def create_session(
        self,
        session_id: str,
        user_id: int,
        device_type: DeviceType,
        device_info: str,
        ip_address: str,
        user_agent: str
    ) -> SessionInfo:
        """
        创建新会话
        
        Args:
            session_id: 会话ID（通常是JWT token的一部分）
            user_id: 用户ID
            device_type: 设备类型
            device_info: 设备信息
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            会话信息
        """
        now = datetime.utcnow()
        
        session_info = SessionInfo(
            session_id=session_id,
            user_id=user_id,
            device_type=device_type,
            device_info=device_info,
            ip_address=ip_address,
            user_agent=user_agent,
            login_time=now,
            last_activity=now
        )
        
        # 检查用户会话数量限制
        self._enforce_session_limit(user_id)
        
        # 存储会话
        self._sessions[session_id] = session_info
        
        # 更新用户会话索引
        if user_id not in self._user_sessions:
            self._user_sessions[user_id] = set()
        self._user_sessions[user_id].add(session_id)
        
        logger.info(f"创建会话成功 user_id={user_id}, session_id={session_id}")
        return session_info
    
    def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        session = self._sessions.get(session_id)
        
        if session and self._is_session_expired(session):
            self.remove_session(session_id)
            return None
        
        return session
    
    def update_activity(self, session_id: str) -> bool:
        """更新会话活动时间"""
        session = self._sessions.get(session_id)
        
        if not session:
            return False
        
        if self._is_session_expired(session):
            self.remove_session(session_id)
            return False
        
        session.last_activity = datetime.utcnow()
        logger.debug(f"更新会话活动时间 session_id={session_id}")
        return True
    
    def remove_session(self, session_id: str) -> bool:
        """移除会话"""
        session = self._sessions.pop(session_id, None)
        
        if not session:
            return False
        
        # 从用户会话索引中移除
        user_sessions = self._user_sessions.get(session.user_id)
        if user_sessions:
            user_sessions.discard(session_id)
            if not user_sessions:
                del self._user_sessions[session.user_id]
        
        logger.info(f"移除会话成功 user_id={session.user_id}, session_id={session_id}")
        return True
    
    def get_user_sessions(self, user_id: int) -> List[SessionInfo]:
        """获取用户的所有会话"""
        session_ids = self._user_sessions.get(user_id, set())
        sessions = []
        
        for session_id in list(session_ids):  # 使用list避免迭代时修改
            session = self.get_session(session_id)
            if session:
                sessions.append(session)
        
        return sessions
    
    def remove_user_sessions(self, user_id: int, exclude_session_id: Optional[str] = None) -> int:
        """
        移除用户的所有会话
        
        Args:
            user_id: 用户ID
            exclude_session_id: 排除的会话ID（通常是当前会话）
            
        Returns:
            移除的会话数量
        """
        session_ids = self._user_sessions.get(user_id, set()).copy()
        removed_count = 0
        
        for session_id in session_ids:
            if session_id != exclude_session_id:
                if self.remove_session(session_id):
                    removed_count += 1
        
        logger.info(f"移除用户会话 user_id={user_id}, 移除数量={removed_count}")
        return removed_count
    
    def is_user_online(self, user_id: int) -> bool:
        """检查用户是否在线"""
        sessions = self.get_user_sessions(user_id)
        return len(sessions) > 0
    
    def get_online_users(self) -> List[int]:
        """获取所有在线用户ID"""
        return list(self._user_sessions.keys())
    
    def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        expired_sessions = []
        
        for session_id, session in self._sessions.items():
            if self._is_session_expired(session):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.remove_session(session_id)
        
        logger.info(f"清理过期会话完成，清理数量={len(expired_sessions)}")
        return len(expired_sessions)
    
    def get_session_stats(self) -> Dict:
        """获取会话统计信息"""
        return {
            "total_sessions": len(self._sessions),
            "online_users": len(self._user_sessions),
            "device_stats": self._get_device_stats(),
            "session_timeout_hours": self._session_timeout.total_seconds() / 3600
        }
    
    def _enforce_session_limit(self, user_id: int) -> None:
        """强制执行用户会话数量限制"""
        user_sessions = self.get_user_sessions(user_id)
        
        if len(user_sessions) >= self._max_sessions_per_user:
            # 移除最旧的会话
            oldest_session = min(user_sessions, key=lambda s: s.login_time)
            self.remove_session(oldest_session.session_id)
            logger.info(f"移除最旧会话以满足限制 user_id={user_id}")
    
    def _is_session_expired(self, session: SessionInfo) -> bool:
        """检查会话是否过期"""
        if not session.is_active:
            return True
        
        return datetime.utcnow() - session.last_activity > self._session_timeout
    
    def _get_device_stats(self) -> Dict[str, int]:
        """获取设备类型统计"""
        device_stats = {}
        
        for session in self._sessions.values():
            device_type = session.device_type.value
            device_stats[device_type] = device_stats.get(device_type, 0) + 1
        
        return device_stats


class SessionMiddleware:
    """会话中间件"""
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
    
    def extract_device_type(self, user_agent: str) -> DeviceType:
        """从User-Agent提取设备类型"""
        user_agent_lower = user_agent.lower()
        
        if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone']):
            return DeviceType.MOBILE
        elif 'tablet' in user_agent_lower or 'ipad' in user_agent_lower:
            return DeviceType.TABLET
        elif any(desktop in user_agent_lower for desktop in ['windows', 'macintosh', 'linux']):
            return DeviceType.DESKTOP
        elif any(web in user_agent_lower for web in ['chrome', 'firefox', 'safari', 'edge']):
            return DeviceType.WEB
        else:
            return DeviceType.UNKNOWN
    
    def extract_device_info(self, user_agent: str) -> str:
        """提取设备信息"""
        # 简化的设备信息提取
        if 'Chrome' in user_agent:
            return "Chrome Browser"
        elif 'Firefox' in user_agent:
            return "Firefox Browser"
        elif 'Safari' in user_agent:
            return "Safari Browser"
        elif 'Edge' in user_agent:
            return "Edge Browser"
        else:
            return "Unknown Browser"


# 全局会话管理器实例
session_manager = SessionManager()
session_middleware = SessionMiddleware(session_manager)

# 导出
__all__ = [
    "SessionManager",
    "SessionInfo",
    "SessionMiddleware",
    "DeviceType",
    "session_manager",
    "session_middleware"
]
