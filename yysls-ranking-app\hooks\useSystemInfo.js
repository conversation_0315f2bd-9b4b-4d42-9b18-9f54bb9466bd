import { ref } from 'vue'

/**
 * 系统信息和胶囊按钮信息的 hooks
 * @returns {Object} 返回系统信息相关的响应式数据和方法
 */
export function useSystemInfo() {
  // 系统信息和胶囊按钮信息
  const systemInfo = ref({})
  const menuButtonInfo = ref({})
  const navBarHeight = ref(0)
  const statusBarHeight = ref(0)

  /**
   * 获取系统信息和胶囊按钮信息
   */
  const getSystemInfo = () => {
    // 获取系统信息
    systemInfo.value = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.value.statusBarHeight || 0

    // 获取胶囊按钮信息（仅在微信小程序中有效）
    // #ifdef MP-WEIXIN
    try {
      menuButtonInfo.value = uni.getMenuButtonBoundingClientRect()
      // 计算导航栏高度：胶囊按钮高度 + (胶囊按钮top - 状态栏高度) * 2
      navBarHeight.value = menuButtonInfo.value.height + (menuButtonInfo.value.top - statusBarHeight.value) * 2
    } catch (e) {
      // 如果获取失败，使用默认值
      navBarHeight.value = 44
    }
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序使用默认导航栏高度
    navBarHeight.value = 44
    // #endif
  }

  return {
    systemInfo,
    menuButtonInfo,
    navBarHeight,
    statusBarHeight,
    getSystemInfo
  }
}