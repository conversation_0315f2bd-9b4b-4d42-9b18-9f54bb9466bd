#!/bin/bash

# 燕友圈榜单系统 - 生产环境部署修复脚本

set -e

echo "🚀 燕友圈榜单系统 - 生产环境部署修复"
echo "=================================="

# 检查 .env.prod 文件
echo "1. 检查环境配置文件..."
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod 文件不存在"
    echo "请先创建 .env.prod 文件，参考 .env.prod.example"
    exit 1
fi

echo "✅ .env.prod 文件存在"

# 检查必要的环境变量
echo "2. 检查必要的环境变量..."
required_vars=("MYSQL_ROOT_PASSWORD" "MYSQL_DATABASE" "MYSQL_USER" "MYSQL_PASSWORD")
missing_vars=()

for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env.prod; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ 缺少以下环境变量："
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "请在 .env.prod 文件中添加这些变量"
    exit 1
fi

echo "✅ 所有必要的环境变量都已设置"

# 显示当前环境变量（隐藏密码）
echo "3. 当前环境变量："
echo "   MYSQL_DATABASE=$(grep '^MYSQL_DATABASE=' .env.prod | cut -d'=' -f2)"
echo "   MYSQL_USER=$(grep '^MYSQL_USER=' .env.prod | cut -d'=' -f2)"
echo "   MYSQL_ROOT_PASSWORD=****(已隐藏)"
echo "   MYSQL_PASSWORD=****(已隐藏)"

# 停止现有容器
echo "4. 停止现有容器..."
docker-compose -f docker-compose.prod.yml down

# 清理旧的镜像（可选）
echo "5. 清理旧镜像..."
docker system prune -f

# 创建必要的目录
echo "6. 创建必要的目录..."
mkdir -p logs uploads nginx/conf.d ssl

# 检查和生成Nginx配置
echo "7. 检查Nginx配置..."
if [ ! -f "nginx/nginx.conf" ]; then
    echo "❌ nginx/nginx.conf 不存在，请检查配置文件"
    exit 1
fi

if [ ! -f "nginx/conf.d/yysls.conf" ] && [ ! -f "nginx/conf.d/yysls-dev.conf" ]; then
    echo "⚠️  缺少站点配置文件"
    echo "正在生成配置文件..."

    if [ -f "scripts/setup-nginx.sh" ]; then
        chmod +x scripts/setup-nginx.sh
        echo "请运行: ./scripts/setup-nginx.sh 生成配置文件"
        echo "或者手动复制模板文件并修改域名"
        exit 1
    else
        echo "❌ 配置生成脚本不存在"
        exit 1
    fi
fi

echo "✅ Nginx配置文件检查通过"

# 重新构建和启动
echo "8. 重新构建和启动服务..."
docker-compose -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo "8. 等待服务启动..."
sleep 10

# 检查服务状态
echo "9. 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 检查日志
echo "10. 检查应用日志..."
docker-compose -f docker-compose.prod.yml logs app --tail=20

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息："
echo "   - 前端地址: http://your-server-ip (HTTP)"
echo "   - 前端地址: https://your-domain.com (HTTPS)"
echo "   - API文档: http://your-server-ip/docs"
echo "   - 数据库端口: 3306"
echo "   - 应用端口: 8000 (内部访问)"
echo ""
echo "🔧 常用命令："
echo "   - 查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "   - 重启服务: docker-compose -f docker-compose.prod.yml restart"
echo "   - 停止服务: docker-compose -f docker-compose.prod.yml down"
echo ""
echo "⚠️  下一步："
echo "   1. 配置 Nginx 反向代理"
echo "   2. 设置 SSL 证书"
echo "   3. 初始化管理员用户"
