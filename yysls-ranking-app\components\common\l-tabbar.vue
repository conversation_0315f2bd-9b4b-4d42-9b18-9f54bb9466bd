<template>
	<fui-tabbar 
		:tabBar="tabBar" 
		:current="current"
		size='22' 
		fontWeight="600" 
		color="#9B9B9B" 
		selectedColor="#000000"
		background="#FFFFFF"
		:isBorder="false"
		:fixedHeight="true"
		@init="inttTabbar"
		@click="tabBarClick"
		>
	</fui-tabbar>
</template>

<script setup>
	import { ref } from 'vue';
	
	const tabBar =  [
		{
			text: "我的",
			url: 'pages/team/index',
			iconPath: "/static/tabbar/team.png",
			selectedIconPath: "/static/tabbar/teams.png"
		},
		{
			text: "信息",
			url: 'pages/team-up/index',
			iconPath: "/static/tabbar/team-up.png",
			selectedIconPath: "/static/tabbar/team-ups.png"
		},
		{
			text: "个人",
			url: 'pages/user/index',
			iconPath: "/static/tabbar/user.png",
			selectedIconPath: "/static/tabbar/users.png",
			dotIcon: ""
		}
	]
	
	// 当前索引
	const current = ref(0)
	// 初始化索引
	let currentPages = getCurrentPages()
	let page = currentPages[currentPages.length - 1]
	const nowPath = page.route;
	//获取页面路径
	tabBar.forEach((item,index)=>{
	    if(nowPath == item.url){
			current.value = index
	    }
	})
	
	const tabBarClick = async (tabbarItem) => {
		if (nowPath !== tabBar[tabbarItem.index].url) {
			uni.switchTab({
				url: '/' + tabBar[tabbarItem.index].url
			});
		}
	}
	
	// 初始化tabbar返回页面高度
	const emit = defineEmits(['inttTabbar'])
	const inttTabbar = (initData) => {
		emit("inttTabbar", initData)
	}
</script>