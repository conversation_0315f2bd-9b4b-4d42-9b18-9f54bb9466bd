"""
Pydantic模式统一导入
"""
from app.schemas.user import (
    UserBase, UserCreate, UserUpdate, UserResponse, UserLogin, UserRegister
)
from app.schemas.ranking import (
    RankingBase, RankingCreate, RankingUpdate, RankingResponse,
    RankingDetailBase, RankingDetailCreate, RankingDetailUpdate, RankingDetailResponse
)
from app.schemas.sponsor import (
    SponsorBase, SponsorCreate, SponsorUpdate, SponsorResponse
)
from app.schemas.common import (
    ResponseModel, PaginatedResponse, TokenResponse
)

# 导出所有schemas
__all__ = [
    # 用户相关
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", "UserLogin", "UserRegister",

    # 榜单相关
    "RankingBase", "RankingCreate", "RankingUpdate", "RankingResponse",
    "RankingDetailBase", "RankingDetailCreate", "RankingDetailUpdate", "RankingDetailResponse",

    # 赞助商相关
    "SponsorBase", "SponsorCreate", "SponsorUpdate", "SponsorResponse",

    # 通用响应
    "ResponseModel", "PaginatedResponse", "TokenResponse",
]
