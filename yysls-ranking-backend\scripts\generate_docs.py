#!/usr/bin/env python3
"""
燕友圈榜单系统文档生成器

一键生成所有技术文档，包括API文档和数据库SQL脚本
"""
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_script(script_path, description):
    """运行脚本并处理结果"""
    print(f"\n{'='*60}")
    print(f"📝 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=project_root)
        
        if result.returncode == 0:
            print(result.stdout)
            print(f"✅ {description} - 完成")
            return True
        else:
            print(f"❌ {description} - 失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行脚本时出错: {e}")
        return False


def create_docs_index():
    """创建文档索引页面"""
    
    index_content = """# 燕友圈榜单系统 - 技术文档索引

## 📚 文档概览

本目录包含燕友圈榜单系统的完整技术文档，为开发团队和运维人员提供详细的参考资料。

## 📋 文档列表

### 🔧 开发文档
- [API接口文档](API_DOCUMENTATION.md) - 完整的API接口说明
- [API参考文档](API_REFERENCE.md) - 自动生成的API参考
- [数据库SQL文档](DATABASE_SQL.md) - 数据库建表和初始化脚本
- [测试指南](../TESTING.md) - 测试框架和测试用例说明

### 🚀 部署文档
- [部署指南](../DEPLOYMENT.md) - 生产环境部署说明
- [项目状态](../PROJECT_STATUS.md) - 项目当前状态和进度
- [里程碑记录](../MILESTONES.md) - 项目重要里程碑

### 📊 项目管理
- [项目记忆](memory/project_memory.md) - 项目开发记录
- [开发日志](memory/development_log.md) - 详细开发日志

## 🔗 自动生成文档

### API文档
- [OpenAPI规范](openapi.json) - 标准OpenAPI 3.0规范文件
- [Postman集合](postman_collection.json) - API测试集合

### 数据库脚本
- [建表脚本](../sql/01_create_tables.sql) - 数据库表创建脚本
- [触发器脚本](../sql/02_create_triggers.sql) - 数据库触发器脚本
- [初始化数据](../sql/03_initial_data.sql) - 系统初始化数据
- [完整部署脚本](../sql/deploy_complete.sql) - 一键部署脚本

## 🛠️ 工具脚本

### 文档生成工具
- [API文档生成器](../scripts/generate_api_docs.py) - 从FastAPI应用生成API文档
- [SQL脚本生成器](../scripts/generate_sql_scripts.py) - 从SQLAlchemy模型生成SQL脚本
- [文档生成器](../scripts/generate_docs.py) - 一键生成所有文档

### 使用方法
```bash
# 生成API文档
python scripts/generate_api_docs.py

# 生成SQL脚本
python scripts/generate_sql_scripts.py

# 生成所有文档
python scripts/generate_docs.py
```

## 📖 快速开始

### 1. 查看API文档
- **在线文档**: 启动应用后访问 http://localhost:8000/docs
- **离线文档**: 查看 [API接口文档](API_DOCUMENTATION.md)
- **API测试**: 导入 [Postman集合](postman_collection.json)

### 2. 数据库部署
```bash
# 创建数据库
createdb yysls_ranking

# 执行部署脚本
psql -U postgres -d yysls_ranking -f sql/deploy_complete.sql
```

### 3. 应用部署
```bash
# 配置环境
cp .env.prod.example .env.prod
# 编辑 .env.prod 文件

# 部署应用
./deploy.sh
```

## 🔄 文档更新

### 自动更新
文档可以通过脚本自动生成和更新：

```bash
# 更新所有文档
python scripts/generate_docs.py
```

### 手动更新
- API文档会随着代码变更自动更新
- 数据库文档需要在模型变更后重新生成
- 项目文档需要手动维护

## 📞 技术支持

如果在使用文档过程中遇到问题：

1. 查看相关文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系开发团队

## 📝 文档贡献

欢迎为文档贡献内容：

1. 发现文档错误或不足
2. 提交Issue或Pull Request
3. 参与文档改进讨论

---

**文档状态**: 🟢 最新 | **最后更新**: 2024年当前日期 | **维护者**: 燕友圈工作室
"""
    
    # 保存索引文档
    docs_dir = project_root / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    with open(docs_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(index_content)
    
    print("✅ 文档索引已创建: docs/README.md")


def create_makefile():
    """创建Makefile用于文档生成"""
    
    makefile_content = """# 燕友圈榜单系统 - 文档生成Makefile

.PHONY: docs api-docs sql-docs clean help

# 默认目标
all: docs

# 生成所有文档
docs:
	@echo "🚀 生成所有技术文档..."
	python scripts/generate_docs.py

# 生成API文档
api-docs:
	@echo "📝 生成API文档..."
	python scripts/generate_api_docs.py

# 生成SQL脚本
sql-docs:
	@echo "🗄️ 生成数据库SQL脚本..."
	python scripts/generate_sql_scripts.py

# 清理生成的文档
clean:
	@echo "🧹 清理生成的文档..."
	rm -rf docs/openapi.json
	rm -rf docs/API_REFERENCE.md
	rm -rf docs/postman_collection.json
	rm -rf sql/

# 显示帮助信息
help:
	@echo "燕友圈榜单系统文档生成工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make docs      - 生成所有文档"
	@echo "  make api-docs  - 生成API文档"
	@echo "  make sql-docs  - 生成SQL脚本"
	@echo "  make clean     - 清理生成的文档"
	@echo "  make help      - 显示此帮助信息"
"""
    
    with open(project_root / "Makefile", "w", encoding="utf-8") as f:
        f.write(makefile_content)
    
    print("✅ Makefile已创建: Makefile")


def main():
    """主函数"""
    print("🚀 燕友圈榜单系统文档生成器")
    print("=" * 60)
    print("正在生成完整的技术文档...")
    
    success_count = 0
    total_count = 0
    
    # 生成API文档
    total_count += 1
    if run_script("scripts/generate_api_docs.py", "生成API文档"):
        success_count += 1
    
    # 生成SQL脚本
    total_count += 1
    if run_script("scripts/generate_sql_scripts.py", "生成数据库SQL脚本"):
        success_count += 1
    
    # 创建文档索引
    print(f"\n{'='*60}")
    print("📚 创建文档索引")
    print(f"{'='*60}")
    try:
        create_docs_index()
        create_makefile()
        success_count += 1
        total_count += 1
    except Exception as e:
        print(f"❌ 创建文档索引失败: {e}")
        total_count += 1
    
    # 输出结果
    print(f"\n{'='*60}")
    if success_count == total_count:
        print("🎉 所有文档生成完成！")
        print(f"✅ 成功: {success_count}/{total_count}")
        
        print("\n📁 生成的文档:")
        print("  📝 API文档:")
        print("    - docs/API_DOCUMENTATION.md")
        print("    - docs/API_REFERENCE.md")
        print("    - docs/openapi.json")
        print("    - docs/postman_collection.json")
        print("  🗄️ 数据库文档:")
        print("    - docs/DATABASE_SQL.md")
        print("    - sql/01_create_tables.sql")
        print("    - sql/02_create_triggers.sql")
        print("    - sql/03_initial_data.sql")
        print("    - sql/deploy_complete.sql")
        print("  📚 索引文档:")
        print("    - docs/README.md")
        print("    - Makefile")
        
        print("\n💡 使用说明:")
        print("  - 查看文档索引: docs/README.md")
        print("  - 在线API文档: http://localhost:8000/docs")
        print("  - 数据库部署: psql -f sql/deploy_complete.sql")
        print("  - 使用Makefile: make docs")
        
    else:
        print("💥 文档生成部分失败！")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        print("请检查错误信息并重试。")
        sys.exit(1)


if __name__ == "__main__":
    main()
