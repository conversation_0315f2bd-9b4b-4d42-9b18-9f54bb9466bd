#!/bin/bash

# 燕友圈榜单系统 - Docker ContainerConfig 错误修复脚本

set -e

echo "🔧 Docker ContainerConfig 错误修复工具"
echo "======================================"

# 1. 停止所有相关服务
echo "1. 停止所有相关服务..."
docker-compose -f docker-compose.prod.yml down -v 2>/dev/null || true
docker-compose -f docker-compose.prod-secure.yml down -v 2>/dev/null || true

echo "✅ 服务已停止"

# 2. 清理容器和镜像
echo ""
echo "2. 清理Docker资源..."

# 删除相关容器
echo "   删除相关容器..."
docker ps -a | grep yysls | awk '{print $1}' | xargs docker rm -f 2>/dev/null || true

# 删除相关镜像
echo "   删除相关镜像..."
docker images | grep yysls | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true

# 清理悬空资源
echo "   清理悬空资源..."
docker system prune -f

# 清理构建缓存
echo "   清理构建缓存..."
docker builder prune -f

echo "✅ Docker资源清理完成"

# 3. 检查和创建必要目录
echo ""
echo "3. 检查项目目录..."

# 创建必要目录
mkdir -p logs uploads ssl nginx/conf.d

# 检查权限
if [ -d "logs" ]; then
    chmod 755 logs
fi

if [ -d "uploads" ]; then
    chmod 755 uploads
fi

echo "✅ 项目目录检查完成"

# 4. 验证配置文件
echo ""
echo "4. 验证配置文件..."

# 检查 Dockerfile
if [ ! -f "Dockerfile" ]; then
    echo "❌ Dockerfile 不存在"
    exit 1
fi

# 检查 docker-compose 文件
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ docker-compose.prod.yml 不存在"
    exit 1
fi

# 检查环境配置
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod 不存在"
    echo "请先创建环境配置文件"
    exit 1
fi

echo "✅ 配置文件验证通过"

# 5. 检查 Nginx 配置
echo ""
echo "5. 检查 Nginx 配置..."

if [ ! -f "nginx/conf.d/yysls.conf" ] && [ ! -f "nginx/conf.d/yysls-dev.conf" ]; then
    echo "⚠️  缺少 Nginx 站点配置文件"
    
    if [ -f "nginx/conf.d/yysls.conf.template" ]; then
        echo "发现模板文件，正在生成配置..."
        cp nginx/conf.d/yysls.conf.template nginx/conf.d/yysls.conf
        
        # 使用默认域名
        sed -i 's/{{DOMAIN_NAME}}/localhost/g' nginx/conf.d/yysls.conf
        echo "✅ 已生成默认配置（域名: localhost）"
        echo "   如需修改域名，请运行: ./scripts/setup-nginx.sh"
    else
        echo "❌ 缺少 Nginx 配置模板"
        exit 1
    fi
fi

echo "✅ Nginx 配置检查完成"

# 6. 重新构建和启动
echo ""
echo "6. 重新构建和启动服务..."

# 选择配置文件
compose_file="docker-compose.prod.yml"
if [ -f "docker-compose.prod-secure.yml" ]; then
    echo "选择配置文件："
    echo "   1) 标准配置 (docker-compose.prod.yml)"
    echo "   2) 安全配置 (docker-compose.prod-secure.yml)"
    read -p "请选择 (1-2, 默认1): " config_choice
    
    if [ "$config_choice" = "2" ]; then
        compose_file="docker-compose.prod-secure.yml"
    fi
fi

echo "使用配置文件: $compose_file"

# 构建镜像
echo "正在构建镜像..."
docker-compose -f $compose_file build --no-cache

# 启动服务
echo "正在启动服务..."
docker-compose -f $compose_file up -d

echo "✅ 服务启动完成"

# 7. 验证服务状态
echo ""
echo "7. 验证服务状态..."
sleep 10

docker-compose -f $compose_file ps

# 检查应用健康状态
echo ""
echo "8. 健康检查..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过"
elif curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过（直接端口）"
else
    echo "⚠️  健康检查失败，查看日志："
    docker-compose -f $compose_file logs app --tail=20
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 服务信息："
echo "   - 配置文件: $compose_file"
echo "   - 访问地址: http://localhost"
echo "   - API文档: http://localhost/docs"
echo ""
echo "🔧 如果仍有问题："
echo "   1. 查看日志: docker-compose -f $compose_file logs -f"
echo "   2. 检查容器: docker-compose -f $compose_file ps"
echo "   3. 重新运行此脚本"
