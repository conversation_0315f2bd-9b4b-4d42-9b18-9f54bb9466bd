# 燕友圈榜单系统 - Excel模板使用说明

## 📋 模板文件

**文件名**: `榜单明细模板.xlsx`  
**文件大小**: 约7KB  
**生成时间**: 刚刚生成  

## 📊 模板结构

### 工作表1: 榜单明细模板
包含以下列：

| 列名 | 数据类型 | 是否必需 | 说明 | 示例 |
|------|----------|----------|------|------|
| 排名开始 | 整数 | ✅ 必需 | 排名区间开始值，必须大于0 | 1 |
| 排名结束 | 整数 | ✅ 必需 | 排名区间结束值，必须≥开始值 | 5 |
| 完成时间(MM:SS) | 时间格式 | ✅ 必需 | 格式为MM:SS | 05:30 |
| 参与人数 | 整数 | ✅ 必需 | 当前时间区间参与人数 | 5 |
| 队伍名称 | 文本 | ❌ 可选 | 队伍或团队的名称 | 燕友圈战队 |
| 队伍信息 | 文本 | ❌ 可选 | 队伍详细信息，逗号分隔 | 队伍A,队伍B,队伍C |

### 工作表2: 使用说明
详细的使用指南和注意事项

## 📝 示例数据

模板中已包含5行示例数据：

```
排名开始 | 排名结束 | 完成时间(MM:SS) | 参与人数 | 队伍名称     | 队伍信息
1       | 5       | 05:30          | 5       | 燕友圈战队   | 队伍A,队伍B,队伍C,队伍D,队伍E
6       | 10      | 06:15          | 5       | 竞速联盟     | 队伍F,队伍G,队伍H,队伍I,队伍J
11      | 15      | 07:00          | 5       | 速度之星     | 队伍K,队伍L,队伍M,队伍N,队伍O
16      | 20      | 07:45          | 5       | 闪电小队     | 队伍P,队伍Q,队伍R,队伍S,队伍T
21      | 25      | 08:30          | 5       | 极速团队     | 队伍U,队伍V,队伍W,队伍X,队伍Y
```

## ✅ 数据验证规则

模板已内置以下验证规则：

1. **排名开始/结束**: 必须为大于0的整数
2. **参与人数**: 必须为大于0的整数
3. **完成时间**: 必须为MM:SS格式
4. **排名逻辑**: 排名开始不能大于排名结束

## 🔧 使用步骤

### 1. 准备数据
- 打开 `榜单明细模板.xlsx` 文件
- 在"榜单明细模板"工作表中填写数据
- 可以修改示例数据或添加新行

### 2. 数据格式要求
- **时间格式**: 使用MM:SS格式，如 `05:30` 表示5分30秒
- **队伍信息**: 多个队伍用英文逗号分隔
- **数值字段**: 不能为空，必须为正整数
- **表头**: 请勿修改表头名称

### 3. 保存文件
- 保存为Excel格式(.xlsx或.xls)
- 确保文件大小不超过10MB

### 4. 上传到系统

#### 方法1: 通过API上传
```bash
# 1. 临时上传Excel文件
curl -X POST "https://your-api.com/api/v1/upload/excel/upload-temp" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@榜单明细.xlsx"

# 2. 创建榜单并导入数据
curl -X POST "https://your-api.com/api/v1/rankings" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "第1期5人竞速榜单",
    "period": 1,
    "ranking_type": "5_person",
    "start_time": "2024-01-15T09:00:00",
    "end_time": "2024-01-15T18:00:00",
    "team_size_limit": 5,
    "excel_file_path": "/path/to/temp/file.xlsx",
    "auto_import_details": true
  }'
```

#### 方法2: 通过Web界面
1. 登录燕友圈榜单管理系统
2. 进入"榜单管理"页面
3. 点击"创建榜单"或"编辑榜单"
4. 上传Excel文件
5. 勾选"自动导入Excel数据"
6. 保存榜单

## ⚠️ 注意事项

### 数据要求
- 排名区间不能重叠
- 时间必须为合理的竞速时间
- 参与人数要与榜单类型匹配

### 文件要求
- 仅支持.xlsx和.xls格式
- 文件大小不超过10MB
- 建议单次导入不超过1000条记录

### 系统限制
- 需要管理员权限才能导入数据
- 临时上传的文件24小时后自动清理
- 导入过程中如有错误会回滚所有更改

## 🚨 常见错误及解决方案

### 1. "排名开始不能大于排名结束"
**原因**: 排名区间设置错误  
**解决**: 检查排名开始和结束的数值

### 2. "完成时间格式错误，应为 MM:SS"
**原因**: 时间格式不正确  
**解决**: 使用MM:SS格式，如05:30

### 3. "参与人数必须为整数"
**原因**: 参与人数字段包含非数字字符  
**解决**: 确保参与人数为正整数

### 4. "Excel文件格式错误"
**原因**: 文件损坏或格式不支持  
**解决**: 重新保存为.xlsx格式

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看Excel文件中的"使用说明"工作表
2. 检查数据格式是否符合要求
3. 联系系统管理员获取帮助

## 🔄 重新生成模板

如果需要重新生成模板文件，可以运行：

```bash
python generate_excel_template.py
```

这将在当前目录生成新的 `榜单明细模板.xlsx` 文件。
