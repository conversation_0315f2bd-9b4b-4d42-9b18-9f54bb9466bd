#!/bin/bash

# MySQL ERROR: 1 修复脚本

echo "🔧 MySQL ERROR: 1 修复工具"
echo "========================="

# 1. 检查环境变量格式
echo "1. 检查环境变量..."
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod 文件不存在"
    exit 1
fi

echo "检查 MySQL 环境变量格式："
mysql_vars=$(grep "^MYSQL_" .env.prod)
if [ -z "$mysql_vars" ]; then
    echo "❌ 未找到 MySQL 环境变量"
    echo "需要的变量："
    echo "MYSQL_ROOT_PASSWORD=your_password"
    echo "MYSQL_DATABASE=yysls_ranking"
    echo "MYSQL_USER=yysls_user"
    echo "MYSQL_PASSWORD=your_password"
    exit 1
fi

echo "找到的 MySQL 变量："
echo "$mysql_vars" | sed 's/PASSWORD=.*/PASSWORD=***/'

# 2. 验证环境变量值
echo ""
echo "2. 验证环境变量值..."

# 检查是否有空值
if grep -q "MYSQL.*=$" .env.prod; then
    echo "❌ 发现空的 MySQL 环境变量："
    grep "MYSQL.*=$" .env.prod
    echo "请设置所有 MySQL 环境变量的值"
    exit 1
fi

# 检查密码复杂度
root_pass=$(grep "^MYSQL_ROOT_PASSWORD=" .env.prod | cut -d'=' -f2)
if [ ${#root_pass} -lt 6 ]; then
    echo "⚠️  MySQL root 密码可能过于简单"
fi

echo "✅ 环境变量格式检查通过"

# 3. 创建测试用的简单环境变量
echo ""
echo "3. 创建测试环境变量..."
cat > .env.test << 'EOF'
MYSQL_ROOT_PASSWORD=TestPass123!
MYSQL_DATABASE=test_db
MYSQL_USER=test_user
MYSQL_PASSWORD=TestUser123!
EOF

echo "✅ 测试环境变量已创建"

# 4. 使用测试环境变量启动
echo ""
echo "4. 使用测试环境变量启动 MySQL..."
docker run --rm -d \
    --name mysql-error1-test \
    --env-file .env.test \
    -e MYSQL_INITDB_SKIP_TZINFO=1 \
    mysql:8.0 \
    --default-authentication-plugin=mysql_native_password \
    --character-set-server=utf8mb4 \
    --collation-server=utf8mb4_unicode_ci

echo "等待 MySQL 启动..."
sleep 10

# 5. 检查测试结果
if docker ps | grep -q mysql-error1-test; then
    echo "✅ 使用测试环境变量启动成功！"
    echo "问题可能在于原始环境变量的值"
    
    # 等待完全启动
    echo "等待 MySQL 完全就绪..."
    for i in {1..20}; do
        if docker logs mysql-error1-test 2>&1 | grep -q "ready for connections"; then
            echo "✅ MySQL 完全就绪"
            break
        fi
        sleep 2
    done
    
    # 测试连接
    if docker exec mysql-error1-test mysqladmin ping -h localhost -u root -pTestPass123! 2>/dev/null; then
        echo "✅ 数据库连接测试成功"
        
        echo ""
        echo "🎉 MySQL 可以正常工作！"
        echo "问题在于原始 .env.prod 文件中的环境变量"
        echo ""
        echo "请检查以下内容："
        echo "1. 密码中是否包含特殊字符需要转义"
        echo "2. 变量值是否包含空格或换行符"
        echo "3. 文件编码是否为 UTF-8"
        echo "4. 是否有重复的变量定义"
        
    else
        echo "⚠️  容器启动但连接失败"
        docker logs mysql-error1-test --tail=10
    fi
    
else
    echo "❌ 即使使用测试环境变量也启动失败"
    echo "问题可能在于："
    echo "1. MySQL 镜像版本兼容性"
    echo "2. 系统资源限制"
    echo "3. Docker 配置问题"
    
    echo ""
    echo "测试容器日志："
    docker logs mysql-error1-test 2>&1 | tail -20
fi

# 6. 清理测试容器
docker stop mysql-error1-test 2>/dev/null || true
docker rm mysql-error1-test 2>/dev/null || true

# 7. 尝试不同的 MySQL 版本
echo ""
echo "6. 尝试 MySQL 5.7 版本..."
docker run --rm -d \
    --name mysql57-test \
    --env-file .env.test \
    mysql:5.7 \
    --character-set-server=utf8mb4 \
    --collation-server=utf8mb4_unicode_ci

sleep 10

if docker ps | grep -q mysql57-test; then
    echo "✅ MySQL 5.7 启动成功"
    echo "建议在 docker-compose.prod.yml 中使用 mysql:5.7"
else
    echo "❌ MySQL 5.7 也启动失败"
    docker logs mysql57-test 2>&1 | tail -10
fi

# 清理
docker stop mysql57-test 2>/dev/null || true
docker rm mysql57-test 2>/dev/null || true

# 8. 提供修复建议
echo ""
echo "🔧 修复建议："
echo "============"
echo ""
echo "如果测试环境变量能启动："
echo "1. 检查 .env.prod 中的特殊字符，特别是密码"
echo "2. 确保没有多余的空格或换行符"
echo "3. 尝试使用更简单的密码（只包含字母数字）"
echo ""
echo "如果需要使用 MySQL 5.7："
echo "1. 修改 docker-compose.prod.yml 中的镜像版本"
echo "   image: mysql:5.7"
echo ""
echo "立即尝试的修复："
echo "# 1. 备份原配置"
echo "cp .env.prod .env.prod.backup"
echo ""
echo "# 2. 使用简单密码"
echo "sed -i 's/MYSQL_ROOT_PASSWORD=.*/MYSQL_ROOT_PASSWORD=simple123/' .env.prod"
echo "sed -i 's/MYSQL_PASSWORD=.*/MYSQL_PASSWORD=simple123/' .env.prod"
echo ""
echo "# 3. 重新启动"
echo "docker-compose -f docker-compose.prod.yml up -d db"

# 清理测试文件
rm -f .env.test

echo ""
echo "✅ ERROR: 1 诊断完成"
