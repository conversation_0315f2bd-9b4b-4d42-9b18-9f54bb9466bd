#!/bin/bash

# 燕友圈榜单系统 - SSL证书生成脚本

set -e

echo "🔐 SSL证书生成工具"
echo "=================="

# 创建SSL目录
mkdir -p ssl

# 检查是否已有证书
if [ -f "ssl/cert.pem" ] && [ -f "ssl/key.pem" ]; then
    echo "⚠️  SSL证书已存在"
    echo "   证书文件: ssl/cert.pem"
    echo "   私钥文件: ssl/key.pem"
    echo ""
    read -p "是否要重新生成？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "👋 操作已取消"
        exit 0
    fi
fi

echo "1. 选择证书类型："
echo "   1) 自签名证书（开发/测试环境）"
echo "   2) Let's Encrypt证书（生产环境）"
echo "   3) 使用现有证书文件"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🔧 生成自签名证书..."
        
        # 获取域名
        read -p "请输入域名 (默认: localhost): " domain
        domain=${domain:-localhost}
        
        # 生成私钥
        openssl genrsa -out ssl/key.pem 2048

        # 生成证书
        openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=YYSLS/OU=IT/CN=$domain" \
            -extensions v3_req -config <(
                echo '[req]'
                echo 'distinguished_name = req'
                echo '[v3_req]'
                echo 'keyUsage = keyEncipherment, dataEncipherment'
                echo 'extendedKeyUsage = serverAuth'
                echo "subjectAltName = DNS:$domain,DNS:www.$domain,DNS:localhost,IP:127.0.0.1"
            )
        
        echo "✅ 自签名证书生成完成"
        echo "   域名: $domain"
        echo "   有效期: 365天"
        echo "   ⚠️  浏览器会显示不安全警告，这是正常的"
        ;;
        
    2)
        echo ""
        echo "🌐 配置 Let's Encrypt 证书..."
        
        # 检查certbot
        if ! command -v certbot &> /dev/null; then
            echo "❌ certbot 未安装"
            echo "   Ubuntu/Debian: sudo apt install certbot"
            echo "   CentOS/RHEL: sudo yum install certbot"
            exit 1
        fi
        
        read -p "请输入域名: " domain
        read -p "请输入邮箱: " email
        
        if [ -z "$domain" ] || [ -z "$email" ]; then
            echo "❌ 域名和邮箱不能为空"
            exit 1
        fi
        
        echo "正在申请证书..."
        certbot certonly --standalone -d $domain --email $email --agree-tos --non-interactive
        
        # 复制证书到项目目录
        cp /etc/letsencrypt/live/$domain/fullchain.pem ssl/cert.pem
        cp /etc/letsencrypt/live/$domain/privkey.pem ssl/key.pem
        
        echo "✅ Let's Encrypt 证书配置完成"
        echo "   域名: $domain"
        echo "   有效期: 90天"
        echo "   📝 请设置自动续期: 0 0,12 * * * certbot renew --quiet"
        ;;
        
    3)
        echo ""
        echo "📁 使用现有证书文件..."
        
        read -p "请输入证书文件路径: " cert_path
        read -p "请输入私钥文件路径: " key_path
        
        if [ ! -f "$cert_path" ] || [ ! -f "$key_path" ]; then
            echo "❌ 证书文件不存在"
            exit 1
        fi
        
        cp "$cert_path" ssl/cert.pem
        cp "$key_path" ssl/key.pem
        
        echo "✅ 证书文件复制完成"
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 设置权限
chmod 600 ssl/key.pem
chmod 644 ssl/cert.pem

echo ""
echo "🎉 SSL证书配置完成！"
echo ""
echo "📋 证书信息："
openssl x509 -in ssl/cert.pem -text -noout | grep -E "(Subject:|Not After)"

echo ""
echo "🚀 下一步："
echo "   1. 修改 nginx/conf.d/yysls.conf 中的域名"
echo "   2. 启动服务: docker-compose -f docker-compose.prod.yml up -d"
echo "   3. 测试HTTPS: https://your-domain.com"
