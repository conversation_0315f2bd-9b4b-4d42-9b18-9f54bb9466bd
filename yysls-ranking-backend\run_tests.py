#!/usr/bin/env python3
"""
测试运行脚本

提供便捷的测试执行命令
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd: list, description: str = ""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} - 成功完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} - 执行失败 (退出码: {e.returncode})")
        return False
    except FileNotFoundError:
        print(f"\n❌ 命令未找到: {cmd[0]}")
        print("请确保已安装pytest: pip install pytest pytest-asyncio pytest-cov")
        return False


def run_unit_tests():
    """运行单元测试"""
    cmd = [
        "pytest", 
        "tests/test_services/",
        "-v",
        "--tb=short",
        "-m", "unit"
    ]
    return run_command(cmd, "单元测试")


def run_integration_tests():
    """运行集成测试"""
    cmd = [
        "pytest", 
        "tests/test_api/",
        "-v", 
        "--tb=short",
        "-m", "integration"
    ]
    return run_command(cmd, "集成测试")


def run_auth_tests():
    """运行认证相关测试"""
    cmd = [
        "pytest", 
        "tests/",
        "-v",
        "--tb=short", 
        "-m", "auth"
    ]
    return run_command(cmd, "认证系统测试")


def run_all_tests():
    """运行所有测试"""
    cmd = [
        "pytest", 
        "tests/",
        "-v",
        "--tb=short"
    ]
    return run_command(cmd, "所有测试")


def run_coverage_tests():
    """运行测试并生成覆盖率报告"""
    cmd = [
        "pytest", 
        "tests/",
        "-v",
        "--tb=short",
        "--cov=app",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-fail-under=70"
    ]
    success = run_command(cmd, "测试覆盖率分析")
    
    if success:
        print(f"\n📊 覆盖率报告已生成:")
        print(f"   - HTML报告: htmlcov/index.html")
        print(f"   - 在浏览器中打开查看详细报告")
    
    return success


def run_fast_tests():
    """运行快速测试（排除慢速测试）"""
    cmd = [
        "pytest", 
        "tests/",
        "-v",
        "--tb=short",
        "-m", "not slow"
    ]
    return run_command(cmd, "快速测试")


def run_specific_test(test_path: str):
    """运行指定的测试文件或测试函数"""
    cmd = [
        "pytest", 
        test_path,
        "-v",
        "--tb=short"
    ]
    return run_command(cmd, f"指定测试: {test_path}")


def check_test_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查pytest是否安装
    try:
        result = subprocess.run(["pytest", "--version"], capture_output=True, text=True)
        print(f"✅ pytest版本: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ pytest未安装，请运行: pip install pytest pytest-asyncio pytest-cov")
        return False
    
    # 检查测试文件
    test_dir = Path("tests")
    if not test_dir.exists():
        print("❌ tests目录不存在")
        return False
    
    test_files = list(test_dir.rglob("test_*.py"))
    print(f"✅ 找到 {len(test_files)} 个测试文件")
    
    # 检查配置文件
    if Path("pytest.ini").exists():
        print("✅ pytest.ini配置文件存在")
    else:
        print("⚠️  pytest.ini配置文件不存在")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="燕友圈榜单系统测试运行器")
    parser.add_argument("command", nargs="?", default="all", 
                       choices=["all", "unit", "integration", "auth", "coverage", "fast", "check"],
                       help="要执行的测试类型")
    parser.add_argument("--file", "-f", help="运行指定的测试文件")
    parser.add_argument("--function", "-k", help="运行匹配的测试函数")
    
    args = parser.parse_args()
    
    print("🚀 燕友圈榜单系统测试运行器")
    print("=" * 60)
    
    # 检查测试环境
    if not check_test_environment():
        sys.exit(1)
    
    success = True
    
    if args.file:
        # 运行指定文件
        success = run_specific_test(args.file)
    elif args.function:
        # 运行匹配的测试函数
        cmd = ["pytest", "tests/", "-v", "--tb=short", "-k", args.function]
        success = run_command(cmd, f"匹配测试: {args.function}")
    else:
        # 根据命令执行相应测试
        if args.command == "unit":
            success = run_unit_tests()
        elif args.command == "integration":
            success = run_integration_tests()
        elif args.command == "auth":
            success = run_auth_tests()
        elif args.command == "coverage":
            success = run_coverage_tests()
        elif args.command == "fast":
            success = run_fast_tests()
        elif args.command == "check":
            print("✅ 测试环境检查完成")
        else:  # all
            success = run_all_tests()
    
    # 输出结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试执行完成！")
        print("\n💡 提示:")
        print("   - 运行覆盖率测试: python run_tests.py coverage")
        print("   - 运行单元测试: python run_tests.py unit")
        print("   - 运行集成测试: python run_tests.py integration")
        print("   - 运行指定文件: python run_tests.py --file tests/test_services/test_user_service.py")
    else:
        print("💥 测试执行失败！")
        print("\n🔧 故障排除:")
        print("   - 检查数据库连接")
        print("   - 确保所有依赖已安装")
        print("   - 查看详细错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
