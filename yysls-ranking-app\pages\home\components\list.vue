<template>
	<view class="ranking-page">
		<!-- 公共模块 -->
		<publicModule></publicModule>
		
		<!-- 背景图 -->
		<fui-background-image src="/static/home/<USER>"></fui-background-image>
		
		<!-- 顶部导航栏 -->
		<top-navbar>
			<template #content>
				<view style="color: #fff;font-weight: bold;font-size: 32rpx;">
					燕友圈
				</view>
			</template>
		</top-navbar>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- ==================== 播报信息区域 ==================== -->
			<view class="broadcast-container ink-card">
				<view class="broadcast-header">
					<view class="svg-icon">
						<sx-svg name="volume-2" :size="32" color="#D4AF37" />
					</view>
					<text class="broadcast-title">最新播报</text>
				</view>
				<view class="broadcast-content">
					<view class="broadcast-scroll-container">
						<text class="broadcast-text" :class="{ 'scrolling': isScrolling }">
							{{ currentMessage }}
						</text>
					</view>
				</view>
			</view>

			<!-- ==================== 榜单标题区域 ==================== -->
			<view class="ranking-header-container">
				<view class="floating-element">
					<view class="main-title-container">
						<text class="main-title">{{ rankingInfo.title }}</text>
					</view>
					<view class="period-container">
						<view class="svg-icon">
							<sx-svg name="sword" :size="48" color="#D4AF37" />
						</view>
						<text class="period-text">{{ rankingInfo.period }}</text>
						<view class="svg-icon">
							<sx-svg name="sword" :size="48" color="#D4AF37" />
						</view>
					</view>
				</view>
			</view>

			<!-- ==================== 榜单Tab和列表区域 ==================== -->
			<view class="ranking-section ink-card">
				<!-- Tab切换 -->
				<view class="ranking-tabs-container">
					<!-- 动态标题 -->
					<view class="ranking-title-container">
						<view class="svg-icon">
							<sx-svg name="trophy" :size="40" color="#D4AF37" />
						</view>
						<text class="ranking-title" :class="titleAnimationClass">{{ currentTitle }}</text>
					</view>

					<!-- Tab切换区域 -->
					<view class="tab-container">
						<view
							v-for="(tab, index) in tabOptions"
							:key="tab.key"
							class="tab-item"
							:class="{
								'active': currentTab === tab.key,
								'inactive': currentTab !== tab.key
							}"
							:data-tab="tab.key"
							@click="handleTabClick(tab.key)"
						>
							<text class="tab-text">{{ tab.name }}</text>
						</view>
					</view>
				</view>

				<!-- 榜单列表 -->
				<view class="ranking-list-container">
					<!-- 榜单表头 -->
					<view class="ranking-header">
						<view class="header-rank">排名</view>
						<view class="header-time">时间</view>
						<view class="header-count">人数</view>
					</view>

					<!-- 榜单内容 -->
					<view class="ranking-content" :class="contentAnimationClass">
						<z-paging
							ref="paging"
							v-model="dataList"
							@query="queryList"
							@refresherStatusChange="onRefresherStatusChange"
							:refresher-enabled="true"
							:refresher-threshold="80"
							:refresher-default-style="'none'"
							:refresher-background="'transparent'"
							:loading-more-enabled="true"
							:loading-more-default-as-loading="false"
							:auto-show-back-to-top="true"
							:back-to-top-bottom="200"
							:empty-view-style="{ backgroundColor: 'transparent' }"
							:safe-area-inset-bottom="false"
							:use-page-scroll="false"
							:fixed="false"
						>
							<!-- 自定义下拉刷新 -->
							<template #refresher>
								<view class="custom-refresher" :class="{
									'refreshing': refresherStatus === 'loading',
									'release-ready': refresherStatus === 'release-to-refresh',
									'complete': refresherStatus === 'complete'
								}">
									<view class="refresher-content">
										<view class="refresher-icon" :class="{
											'rotating': refresherStatus === 'loading',
											'scale-up': refresherStatus === 'release-to-refresh'
										}">
											<sx-svg
												:name="refresherStatus === 'complete' ? 'shield-check' : 'refresh-cw'"
												:size="32"
												:color="refresherStatus === 'complete' ? '#4CAF50' : '#D4AF37'"
											/>
										</view>
										<text class="refresher-text" :class="{
											'success-text': refresherStatus === 'complete'
										}">{{ refresherText }}</text>
									</view>
								</view>
							</template>

							<!-- 榜单项目 -->
							<template #default>
								<view
									v-for="(item, index) in dataList"
									:key="`${currentTab}-${item.rank}`"
									class="ranking-item"
									:class="[
										`rank-${item.rank}`
									]"
									@click="handleRankingItemClick(item)"
								>
									<!-- 排名 -->
									<view class="rank-number">
										<view class="crown-icon">
											<sx-svg
												v-if="item.rank <= 3"
												name="crown"
												:size="32"
												:color="getCrownColor(item.rank)"
											/>
										</view>
										<text class="rank-text">{{ item.rank }}</text>
									</view>

									<!-- 时间 -->
									<view class="rank-time">
										<text class="time-text">{{ item.time }}</text>
									</view>

									<!-- 人数 -->
									<view class="rank-count">
										<text class="count-text">{{ item.count }}</text>
									</view>

									<!-- 特效背景 -->
									<view v-if="item.rank <= 3" class="special-bg"></view>
								</view>
							</template>

							<!-- 自定义空数据 -->
							<template #empty>
								<view class="empty-container">
									<sx-svg name="trophy" :size="80" color="#666" />
									<text class="empty-text">暂无榜单数据</text>
									<text class="empty-desc">请稍后再试或下拉刷新</text>
								</view>
							</template>

							<!-- 自定义加载更多 -->
							<template #loadingMore>
								<view class="loading-more">
									<view class="loading-icon rotating">
										<sx-svg name="loader" :size="24" color="#D4AF37" />
									</view>
									<text class="loading-text">加载中...</text>
								</view>
							</template>
						</z-paging>
					</view>

					<!-- 查看完整榜单按钮 -->
					<view class="action-container">
						<view class="action-button" @click="handleViewMore">
							<sx-svg name="share-2" :size="32" color="#fff" />
							<text class="action-text">分享榜单</text>
						</view>
					</view>
				</view>
			</view>

			<!-- ==================== 榜单时间信息 ==================== -->
			<view class="time-info-container ink-card">
				<!-- 更新时间 -->
				<view class="time-item">
					<view class="time-label">
						<view class="svg-icon">
							<sx-svg name="clock" :size="32" color="#D4AF37"/>
						</view>
						<text class="label-text">更新时间</text>
					</view>
					<text class="time-value">{{ rankingInfo.updateTime }}</text>
				</view>

				<!-- 分割线 -->
				<view class="divider"></view>

				<!-- 统计周期 -->
				<view class="time-item">
					<view class="time-label">
						<view class="svg-icon">
							<sx-svg name="calendar" :size="32" color="#D4AF37"/>
						</view>
						
						<text class="label-text">统计周期</text>
					</view>
					<text class="time-value">{{ rankingInfo.statisticsPeriod }}</text>
				</view>
			</view>

			<!-- ==================== 赞助商列表 ==================== -->
			<view class="sponsor-container ink-card">
				<!-- 标题 -->
				<view class="sponsor-header">
					<view class="svg-icon">
						<sx-svg name="handshake" :size="40" color="#D4AF37" />
					</view>

					<text class="sponsor-title">合作伙伴</text>
				</view>

				<!-- 赞助商网格 -->
				<view class="sponsor-grid">
					<view
						v-for="(sponsor, index) in sponsorList"
						:key="sponsor.id || index"
						class="sponsor-item"
						@click="handleSponsorClick(sponsor)"
					>
						<!-- 图片 -->
						<view class="sponsor-image-container">
							<image
								v-if="sponsor.image"
								:src="sponsor.image"
								class="sponsor-image"
								mode="aspectFill"
								:lazy-load="true"
								@error="handleImageError"
							/>
							<view v-else class="sponsor-placeholder">
								<sx-svg name="image" :size="48" color="#666" />
							</view>
						</view>

						<!-- 名称 -->
						<text class="sponsor-name">{{ sponsor.name }}</text>

						<!-- 悬停效果背景 -->
						<view class="sponsor-bg"></view>
					</view>
				</view>
			</view>

			<!-- ==================== 广告招租区域 ==================== -->
			<view class="ad-banner-container" @click="handleAdClick">
				<!-- 背景装饰 -->
				<view class="ad-bg-decoration"></view>

				<!-- 主要内容 -->
				<view class="ad-content">
					<!-- 图标 -->
					<view class="ad-icon-container svg-icon">
						<sx-svg name="megaphone" :size="64" color="#D4AF37" />
					</view>

					<!-- 标题 -->
					<text class="ad-title">广告招租</text>

					<!-- 描述 -->
					<view class="ad-description">
						<text class="ad-desc-line">在江湖中展示您的品牌</text>
						<text class="ad-desc-line">让更多侠客了解您的产品</text>
					</view>

					<!-- 按钮 -->
					<view class="ad-button" @click.stop="handleAdButtonClick">
						<text class="ad-button-text">联系我们</text>
						<view class="ad-button-bg"></view>
					</view>
				</view>

				<!-- 装饰元素 -->
				<view class="ad-decorations">
					<view class="decoration-dot dot-1"></view>
					<view class="decoration-dot dot-2"></view>
					<view class="decoration-dot dot-3"></view>
					<view class="decoration-line line-1"></view>
					<view class="decoration-line line-2"></view>
				</view>
			</view>

			<!-- 底部间距 -->
			<view class="bottom-spacing"></view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from "vue"

// 隐藏tabbar
uni.hideTabBar()

// ==================== 组件状态管理 ====================

// 播报信息状态
const currentMessageIndex = ref(0)
const currentMessage = ref('')
const isScrolling = ref(false)
let scrollTimer = null
let switchTimer = null

// Tab切换状态
const titleAnimationClass = ref('')
const contentAnimationClass = ref('')

// 列表状态
const paging = ref(null)
const dataList = ref([])
const refresherTriggered = ref(false)
const refresherStatus = ref('default')

// 当前选中的Tab
const currentTab = ref('5-person')

// Tab选项
const tabOptions = ref([
	{ key: '5-person', name: '5人榜单' },
	{ key: '10-person', name: '10人榜单' }
])

// 播报消息
const broadcastMessages = ref([
	'🎉 恭喜"剑客无名"荣登本周武功榜首位！',
	'📢 新一期"江湖风云榜"即将发布',
	'⚔️ 武林大会报名火热进行中',
	'🏆 本周竞速赛奖励丰厚，快来参与！',
	'🌟 新手福利活动正在进行中',
	'⚡ 服务器维护完成，感谢您的耐心等待'
])

// ==================== 计算属性 ====================
// 当前标题
const currentTitle = computed(() => {
	const tab = tabOptions.value.find(t => t.key === currentTab.value)
	return tab ? tab.name : '竞速榜单'
})

// 下拉刷新文字
const refresherText = computed(() => {
	switch (refresherStatus.value) {
		case 'default':
			return '下拉刷新'
		case 'release-to-refresh':
			return '松手立即刷新'
		case 'loading':
			return '正在刷新...'
		case 'complete':
			return '刷新完成'
		default:
			return '下拉刷新'
	}
})

// ==================== 播报信息相关方法 ====================
// 初始化当前消息
const initMessage = () => {
	if (broadcastMessages.value.length > 0) {
		currentMessage.value = broadcastMessages.value[currentMessageIndex.value]
	}
}

// 开始滚动动画
const startScrolling = () => {
	isScrolling.value = true

	scrollTimer = setTimeout(() => {
		isScrolling.value = false
		if (broadcastMessages.value.length > 1) {
			switchToNext()
		}
	}, 15000)
}

// 切换到下一条消息
const switchToNext = () => {
	currentMessageIndex.value = (currentMessageIndex.value + 1) % broadcastMessages.value.length
	currentMessage.value = broadcastMessages.value[currentMessageIndex.value]

	switchTimer = setTimeout(() => {
		startScrolling()
	}, 1000)
}

// 清除定时器
const clearTimers = () => {
	if (scrollTimer) {
		clearTimeout(scrollTimer)
		scrollTimer = null
	}
	if (switchTimer) {
		clearTimeout(switchTimer)
		switchTimer = null
	}
}

// ==================== Tab切换相关方法 ====================
// 处理Tab点击
const handleTabClick = (tabKey) => {
	if (currentTab.value === tabKey) return

	// 添加标题切换动画
	triggerTitleAnimation()

	// 更新当前Tab
	currentTab.value = tabKey

	// 触发列表内容切换动画
	switchContent()
}

// 触发标题切换动画
const triggerTitleAnimation = () => {
	titleAnimationClass.value = 'title-fade-out'

	setTimeout(() => {
		titleAnimationClass.value = 'title-fade-in'

		setTimeout(() => {
			titleAnimationClass.value = ''
		}, 300)
	}, 150)
}

// 切换内容动画
const switchContent = () => {
	contentAnimationClass.value = 'fade-out'

	setTimeout(() => {
		// 重置分页数据
		if (paging.value) {
			paging.value.reload()
		}

		contentAnimationClass.value = 'fade-in'

		setTimeout(() => {
			contentAnimationClass.value = ''
		}, 300)
	}, 200)
}

// 榜单信息
const rankingInfo = reactive({
	title: '江湖风云榜',
	period: '第2024期',
	updateTime: (() => {
		const now = new Date()
		return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日 ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
	})(),
	statisticsPeriod: (() => {
		const now = new Date()
		const weekStart = new Date(now)
		weekStart.setDate(now.getDate() - now.getDay() + 1)
		const weekEnd = new Date(weekStart)
		weekEnd.setDate(weekStart.getDate() + 6)

		const formatDate = (date) => {
			return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`
		}

		return `${formatDate(weekStart)} - ${formatDate(weekEnd)}`
	})()
})

// 赞助商列表
const sponsorList = ref([
	{
		id: 1,
		name: '武林盟主',
		image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=120&h=80&fit=crop',
		url: ''
	},
	{
		id: 2,
		name: '江湖客栈',
		image: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=120&h=80&fit=crop',
		url: ''
	},
	{
		id: 3,
		name: '神兵阁',
		image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=120&h=80&fit=crop',
		url: ''
	},
	{
		id: 4,
		name: '丹药坊',
		image: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=120&h=80&fit=crop',
		url: ''
	}
])

// ==================== 榜单数据相关方法 ====================
// 模拟数据
const mockData = {
	'5-person': [
		{ rank: 1, time: '1分35秒', count: 5 },
		{ rank: 2, time: '1分48秒', count: 5 },
		{ rank: 3, time: '2分02秒', count: 5 },
		{ rank: 4, time: '2分15秒', count: 5 },
		{ rank: 5, time: '2分28秒', count: 5 },
		{ rank: 6, time: '2分41秒', count: 5 },
		{ rank: 7, time: '2分54秒', count: 5 },
		{ rank: 8, time: '3分07秒', count: 5 },
		{ rank: 9, time: '3分20秒', count: 5 },
		{ rank: 10, time: '3分33秒', count: 5 }
	],
	'10-person': [
		{ rank: 1, time: '3分12秒', count: 10 },
		{ rank: 2, time: '3分25秒', count: 10 },
		{ rank: 3, time: '3分38秒', count: 10 },
		{ rank: 4, time: '3分52秒', count: 10 },
		{ rank: 5, time: '4分06秒', count: 10 },
		{ rank: 6, time: '4分19秒', count: 10 },
		{ rank: 7, time: '4分32秒', count: 10 },
		{ rank: 8, time: '4分45秒', count: 10 },
		{ rank: 9, time: '4分58秒', count: 10 },
		{ rank: 10, time: '5分11秒', count: 10 }
	]
}

// 查询列表数据
const queryList = async (pageNo, pageSize) => {
	try {
		// 模拟网络请求延迟
		await new Promise(resolve => setTimeout(resolve, 800))

		const currentData = mockData[currentTab.value] || []
		const startIndex = (pageNo - 1) * pageSize
		const endIndex = startIndex + pageSize
		const pageData = currentData.slice(startIndex, endIndex)

		// 完成分页
		paging.value.complete(pageData)

	} catch (error) {
		console.error('查询榜单数据失败:', error)
		paging.value.complete(false)
	}
}

// 获取皇冠颜色
const getCrownColor = (rank) => {
	switch (rank) {
		case 1: return '#FFD700' // 金色
		case 2: return '#C0C0C0' // 银色
		case 3: return '#CD7F32' // 铜色
		default: return '#D4AF37'
	}
}

// ==================== 下拉刷新相关方法 ====================
// 处理下拉刷新状态变化
const onRefresherStatusChange = (status) => {
	refresherStatus.value = status
	refresherTriggered.value = status === 'loading'
}

// ==================== 事件处理函数 ====================
// 处理榜单项目点击
const handleRankingItemClick = (data) => {
	console.log('榜单项目点击:', data)

	// 显示详情或跳转
	uni.showToast({
		title: `第${data.rank}名: ${data.time}`,
		icon: 'none',
		duration: 2000
	})
}

// 处理查看更多
const handleViewMore = () => {
	console.log('查看更多')

	// 跳转到完整榜单页面
	uni.showToast({
		title: '跳转到完整榜单',
		icon: 'none',
		duration: 2000
	})
}



// 处理赞助商点击
const handleSponsorClick = (sponsor) => {
	console.log('赞助商点击:', sponsor)

	if (sponsor.url) {
		// 跳转到赞助商页面
		console.log('跳转到:', sponsor.url)
	} else {
		uni.showToast({
			title: sponsor.name,
			icon: 'none',
			duration: 1500
		})
	}
}

// 处理图片加载错误
const handleImageError = (e) => {
	console.warn('赞助商图片加载失败:', e)
}

// 处理广告点击
const handleAdClick = () => {
	console.log('广告区域点击')
}

// 处理广告按钮点击
const handleAdButtonClick = () => {
	console.log('联系我们按钮点击')

	// 显示联系方式或跳转
	uni.showModal({
		title: '联系我们',
		content: '请通过以下方式联系我们：\n微信：example\n邮箱：<EMAIL>',
		showCancel: false,
		confirmText: '知道了'
	})
}

// ==================== 初始化和生命周期 ====================

// 初始化播报信息
const initBroadcast = () => {
	initMessage()
	// 延迟开始第一次滚动
	setTimeout(() => {
		startScrolling()
	}, 1000)
}

// 暴露方法给父组件
defineExpose({
	refreshData: () => {
		if (paging.value) {
			paging.value.reload()
		}
	},
	switchTab: (tabKey) => {
		currentTab.value = tabKey
		switchContent()
	}
})

onMounted(() => {
	// 初始化播报信息
	initBroadcast()
})

// 组件卸载时清理
onUnmounted(() => {
	clearTimers()
})
</script>

<style scoped>
/* ==================== 页面基础样式 ==================== */
.ranking-page {
	min-height: 100vh;
	color: #F5F5DC;
	position: relative;
	overflow-x: hidden;
}

.main-content {
	position: relative;
	z-index: 1;
}

/* ==================== 播报信息样式 ==================== */
.broadcast-container {
	background: rgba(26, 26, 26, 0.9);
	border: 1rpx solid rgba(212, 175, 55, 0.2);
	border-radius: 24rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
	padding: 24rpx;
	margin: 24rpx;
	overflow: hidden;
	position: relative;
}

.broadcast-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	position: relative;
	z-index: 1;
}

.broadcast-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	letter-spacing: 1rpx;
}

.broadcast-content {
	position: relative;
	z-index: 1;
}

.broadcast-scroll-container {
	height: 48rpx;
	overflow: hidden;
	position: relative;
	background: rgba(74, 74, 74, 0.2);
	border-radius: 12rpx;
	padding: 0 24rpx;
	display: flex;
	align-items: center;
}

.broadcast-text {
	font-size: 26rpx;
	color: #F5F5DC;
	white-space: nowrap;
	line-height: 48rpx;
	transition: transform 0.3s ease;
	position: relative;
}

.broadcast-text.scrolling {
	animation: scroll-left 15s linear infinite;
}

@keyframes scroll-left {
	0% {
		transform: translateX(100%);
	}
	100% {
		transform: translateX(-100%);
	}
}

/* ==================== 榜单标题样式 ==================== */
.ranking-header-container {
	text-align: center;
	padding: 120rpx 48rpx 80rpx;
	position: relative;
	overflow: hidden;
}

.floating-element {
	opacity: 1;
	transform: translateY(0) scale(1);
	position: relative;
	z-index: 1;
	animation: float-glow 6s ease-in-out infinite;
}

.main-title-container {
	margin-bottom: 32rpx;
	position: relative;
}

.main-title {
	font-size: 72rpx;
	font-weight: 700;
	color: #D4AF37;
	text-shadow:
		6rpx 6rpx 12rpx rgba(0, 0, 0, 0.8),
		4rpx 4rpx 8rpx rgba(0, 0, 0, 0.9),
		0 0 32rpx rgba(212, 175, 55, 0.6);
	letter-spacing: 2rpx;
	line-height: 1.2;
	position: relative;
}

.period-container {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 24rpx;
	position: relative;
}

.period-text {
	font-size: 48rpx;
	font-weight: 700;
	color: #D4AF37;
	text-shadow:
		4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8),
		2rpx 2rpx 4rpx rgba(0, 0, 0, 0.9),
		0 0 24rpx rgba(212, 175, 55, 0.5);
	letter-spacing: 1rpx;
}

/* 悬浮发光动画 */
@keyframes float-glow {
	0%, 100% {
		transform: translateY(0) scale(1);
		filter: drop-shadow(0 0 20rpx rgba(212, 175, 55, 0.3));
	}
	50% {
		transform: translateY(-20rpx) scale(1.02);
		filter: drop-shadow(0 0 40rpx rgba(212, 175, 55, 0.5));
	}
}

/* ==================== Tab切换样式 ==================== */
.ranking-tabs-container {
	padding: 0 32rpx 40rpx;
}

.ranking-title-container {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	gap: 16rpx;
}

.ranking-title {
	font-size: 42rpx;
	font-weight: 600;
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	letter-spacing: 1rpx;
	transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.ranking-title.title-fade-out {
	opacity: 0;
	transform: translateY(-20rpx);
}

.ranking-title.title-fade-in {
	opacity: 1;
	transform: translateY(0);
}

.tab-container {
	display: flex;
	background: rgba(74, 74, 74, 0.3);
	border-radius: 16rpx;
	padding: 8rpx;
	border: 1rpx solid rgba(212, 175, 55, 0.1);
	backdrop-filter: blur(10rpx);
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.2),
		inset 0 1rpx 0 rgba(212, 175, 55, 0.1);
	position: relative;
	overflow: hidden;
}

.tab-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		135deg,
		rgba(212, 175, 55, 0.05) 0%,
		transparent 50%,
		rgba(212, 175, 55, 0.03) 100%
	);
	pointer-events: none;
}

.tab-item {
	flex: 1;
	padding: 20rpx 32rpx;
	text-align: center;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	font-weight: 500;
	position: relative;
	overflow: hidden;
	z-index: 1;
}

.tab-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(212, 175, 55, 0.1),
		transparent
	);
	transition: left 0.5s;
	z-index: -1;
}

.tab-item:active::before {
	left: 100%;
}

.tab-text {
	font-size: 32rpx;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
	z-index: 1;
}

.tab-item.inactive .tab-text {
	color: #9CA3AF;
}

.tab-item.active {
	background: rgba(212, 175, 55, 0.15);
	box-shadow:
		0 0 40rpx rgba(212, 175, 55, 0.2),
		inset 0 1rpx 0 rgba(212, 175, 55, 0.3);
	transform: scale(1.02);
}

.tab-item.active .tab-text {
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	font-weight: 600;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60%;
	height: 4rpx;
	background: linear-gradient(
		90deg,
		transparent,
		#D4AF37,
		transparent
	);
	border-radius: 2rpx;
	animation: glow-line 2s ease-in-out infinite alternate;
}

/* 发光线条动画 */
@keyframes glow-line {
	0% {
		box-shadow: 0 0 10rpx rgba(212, 175, 55, 0.5);
	}
	100% {
		box-shadow: 0 0 30rpx rgba(212, 175, 55, 0.8);
	}
}

/* ==================== 榜单列表样式 ==================== */
.ranking-list-container {
	position: relative;
	z-index: 1;
}

.ranking-header {
	display: flex;
	background: rgba(212, 175, 55, 0.1);
	border: 1rpx solid rgba(212, 175, 55, 0.3);
	border-radius: 16rpx;
	padding: 24rpx 32rpx;
	margin-bottom: 32rpx;
	font-weight: 600;
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	position: relative;
	z-index: 1;
}

.header-rank {
	width: 160rpx;
	text-align: center;
	font-size: 28rpx;
}

.header-time {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
}

.header-count {
	width: 160rpx;
	text-align: center;
	font-size: 28rpx;
}

.ranking-content {
	opacity: 1;
	transform: translateY(0);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
	z-index: 1;
	height: 800rpx;
}

.ranking-content.fade-out {
	opacity: 0;
	transform: translateY(40rpx);
}

.ranking-content.fade-in {
	opacity: 1;
	transform: translateY(0rpx);
}

/* 自定义下拉刷新样式 */
.custom-refresher {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	opacity: 0.8;
}

.custom-refresher.refreshing {
	opacity: 1;
	transform: scale(1.05);
}

.custom-refresher.release-ready {
	opacity: 1;
	transform: scale(1.02);
}

.custom-refresher.complete {
	opacity: 1;
	animation: complete-bounce 0.6s ease-out;
}

.refresher-content {
	display: flex;
	align-items: center;
	gap: 16rpx;
	transition: all 0.3s ease;
}

.refresher-icon {
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: flex;
	align-items: center;
	justify-content: center;
}

.refresher-icon.rotating {
	animation: rotate-smooth 1.2s linear infinite;
}

.refresher-icon.scale-up {
	transform: scale(1.2);
	animation: pulse-glow 0.8s ease-in-out infinite alternate;
}

.refresher-text {
	font-size: 26rpx;
	color: #D4AF37;
	font-weight: 500;
	transition: all 0.3s ease;
	text-shadow: 0 0 8rpx rgba(212, 175, 55, 0.3);
}

.refresher-text.success-text {
	color: #4CAF50;
	text-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
}

/* 空数据样式 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #9CA3AF;
	margin-bottom: 12rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #6B7280;
}

/* 加载更多样式 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	padding: 40rpx;
}

.loading-icon.rotating {
	animation: rotate 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #D4AF37;
}

/* 操作按钮 */
.action-container {
	text-align: center;
	margin-top: 40rpx;
	position: relative;
	z-index: 1;
}

.action-button {
	margin: 0 auto;
	width: 160rpx;
	padding: 24rpx 48rpx;
	background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
	color: #1A1A1A;
	font-weight: 600;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
	display: flex;
}

.action-button:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.4);
}

.action-text {
	margin-top: -4rpx;
	font-size: 28rpx;
	margin-left: 12rpx;
	color: #fff;
}

/* 旋转动画 */
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 平滑旋转动画 */
@keyframes rotate-smooth {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 脉冲发光动画 */
@keyframes pulse-glow {
	0% {
		transform: scale(1.2);
		filter: drop-shadow(0 0 8rpx rgba(212, 175, 55, 0.4));
	}
	100% {
		transform: scale(1.3);
		filter: drop-shadow(0 0 16rpx rgba(212, 175, 55, 0.8));
	}
}

/* 完成弹跳动画 */
@keyframes complete-bounce {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}

/* ==================== 榜单项目样式 ==================== */
.ranking-item {
	display: flex;
	align-items: center;
	background: rgba(74, 74, 74, 0.3);
	border: 1rpx solid rgba(212, 175, 55, 0.1);
	border-radius: 16rpx;
	padding: 24rpx 32rpx;
	margin-bottom: 16rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	backdrop-filter: blur(10rpx);
}

.ranking-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		135deg,
		rgba(212, 175, 55, 0.02) 0%,
		transparent 50%,
		rgba(212, 175, 55, 0.01) 100%
	);
	pointer-events: none;
}

.ranking-item:active {
	transform: scale(0.98);
}


.rank-number {
	width: 160rpx;
	text-align: center;
	font-weight: bold;
	color: #FFFFFF;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
}

.crown-icon {
	transform: rotate(-18deg);
	filter: drop-shadow(0 0 8rpx rgba(212, 175, 55, 0.4));
}

.rank-text {
	font-size: 36rpx;
	font-weight: 700;
}

.rank-time {
	flex: 1;
	text-align: center;
}

.time-text {
	font-size: 32rpx;
	color: #F5F5DC;
	font-weight: 500;
}

.rank-count {
	width: 160rpx;
	text-align: center;
}

.count-text {
	font-size: 32rpx;
	color: #D4AF37;
	font-weight: 600;
	text-shadow: 0 0 8rpx rgba(212, 175, 55, 0.3);
}

.special-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.1;
	pointer-events: none;
	border-radius: 16rpx;
}

/* 第1名特殊样式 */
.ranking-item.rank-1 {
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.35) 0%, rgba(255, 215, 0, 0.15) 100%);
	border: 3rpx solid rgba(255, 215, 0, 0.8);
}

.ranking-item.rank-1 .rank-text {
	color: #FFD700;
	text-shadow:
		0 0 40rpx rgba(255, 215, 0, 1),
		0 0 80rpx rgba(255, 215, 0, 0.6),
		4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8);
	font-size: 40rpx;
	font-weight: 900;
}


/* 第2名特殊样式 */
.ranking-item.rank-2 {
	background: linear-gradient(135deg, rgba(192, 192, 192, 0.3) 0%, rgba(192, 192, 192, 0.12) 100%);
	border: 3rpx solid rgba(192, 192, 192, 0.7);
}

.ranking-item.rank-2 .rank-text {
	color: #C0C0C0;
	text-shadow:
		0 0 30rpx rgba(192, 192, 192, 0.9),
		0 0 60rpx rgba(192, 192, 192, 0.5),
		4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8);
	font-size: 38rpx;
	font-weight: 800;
}

/* 第3名特殊样式 */
.ranking-item.rank-3 {
	background: linear-gradient(135deg, rgba(205, 127, 50, 0.3) 0%, rgba(205, 127, 50, 0.12) 100%);
	border: 3rpx solid rgba(205, 127, 50, 0.7);
}

.ranking-item.rank-3 .rank-text {
	color: #CD7F32;
	text-shadow:
		0 0 30rpx rgba(205, 127, 50, 0.9),
		0 0 60rpx rgba(205, 127, 50, 0.5),
		4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8);
	font-size: 38rpx;
	font-weight: 800;
}



/* ==================== 时间信息样式 ==================== */
.time-info-container {
	background: rgba(26, 26, 26, 0.9);
	border: 1rpx solid rgba(212, 175, 55, 0.2);
	border-radius: 24rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
	padding: 32rpx;
	margin: 24rpx;
	overflow: hidden;
	position: relative;
	opacity: 1;
	transform: translateY(0);
}

.time-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 0;
	position: relative;
	z-index: 1;
}

.time-label {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.label-text {
	font-size: 28rpx;
	color: #D4AF37;
	font-weight: 500;
	text-shadow: 0 0 8rpx rgba(212, 175, 55, 0.3);
}

.time-value {
	font-size: 26rpx;
	color: #F5F5DC;
	font-weight: 400;
	text-align: right;
	max-width: 60%;
	word-break: break-all;
}

.divider {
	height: 1rpx;
	background: linear-gradient(
		90deg,
		transparent 0%,
		rgba(212, 175, 55, 0.3) 20%,
		rgba(212, 175, 55, 0.5) 50%,
		rgba(212, 175, 55, 0.3) 80%,
		transparent 100%
	);
	margin: 16rpx 0;
	position: relative;
	z-index: 1;
}

/* ==================== 赞助商网格样式 ==================== */
.sponsor-container {
	background: rgba(26, 26, 26, 0.9);
	border: 1rpx solid rgba(212, 175, 55, 0.2);
	border-radius: 24rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
	padding: 32rpx;
	margin: 24rpx;
	overflow: hidden;
	position: relative;
	opacity: 1;
	transform: translateY(0);
}

.sponsor-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 32rpx;
	position: relative;
	z-index: 1;
}

.sponsor-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	letter-spacing: 1rpx;
}

.sponsor-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
	position: relative;
	z-index: 1;
}

.sponsor-item {
	background: rgba(245, 245, 220, 0.1);
	border: 1rpx solid rgba(245, 245, 220, 0.2);
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	cursor: pointer;
}

.sponsor-image-container {
	width: 100%;
	height: 80rpx;
	border-radius: 8rpx;
	overflow: hidden;
	margin-bottom: 16rpx;
	background: rgba(74, 74, 74, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
}

.sponsor-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.sponsor-placeholder {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(102, 102, 102, 0.2);
	border-radius: 8rpx;
}

.sponsor-name {
	font-size: 24rpx;
	color: #F5F5DC;
	font-weight: 400;
	line-height: 1.4;
	position: relative;
	z-index: 1;
}

.sponsor-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(
		circle at center,
		rgba(212, 175, 55, 0.1) 0%,
		transparent 70%
	);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

/* ==================== 广告招租样式 ==================== */
.ad-banner-container {
	background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.05) 100%);
	border: 4rpx dashed rgba(212, 175, 55, 0.3);
	border-radius: 24rpx;
	padding: 48rpx 32rpx;
	margin: 24rpx;
	text-align: center;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	opacity: 1;
	transform: translateY(0) scale(1);
	transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.ad-bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		radial-gradient(circle at 20% 30%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
		radial-gradient(circle at 80% 70%, rgba(212, 175, 55, 0.03) 0%, transparent 50%);
	pointer-events: none;
}

.ad-content {
	position: relative;
	z-index: 1;
}

.ad-icon-container {
	margin-bottom: 24rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.ad-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #D4AF37;
	text-shadow: 0 0 16rpx rgba(212, 175, 55, 0.3);
	letter-spacing: 1rpx;
	margin-bottom: 16rpx;
	display: block;
}

.ad-description {
	margin-bottom: 32rpx;
}

.ad-desc-line {
	font-size: 26rpx;
	color: #F5F5DC;
	line-height: 1.6;
	display: block;
	margin-bottom: 8rpx;
	opacity: 0.9;
}

.ad-button {
	display: inline-block;
	padding: 24rpx 48rpx;
	background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
	color: #1A1A1A;
	font-weight: 600;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
	position: relative;
	overflow: hidden;
	cursor: pointer;
}

.ad-button-text {
	font-size: 28rpx;
	position: relative;
	z-index: 1;
}

.ad-button-bg {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.6s ease;
}

.ad-button:hover .ad-button-bg {
	left: 100%;
}

/* 装饰元素 */
.ad-decorations {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: 0;
}

.decoration-dot {
	position: absolute;
	width: 8rpx;
	height: 8rpx;
	background: rgba(212, 175, 55, 1);
	border-radius: 50%;
	animation: dot-float 4s ease-in-out infinite;
}

.decoration-dot.dot-1 {
	top: 20%;
	left: 15%;
	animation-delay: 0s;
}

.decoration-dot.dot-2 {
	top: 70%;
	right: 20%;
	animation-delay: 1.5s;
}

.decoration-dot.dot-3 {
	bottom: 30%;
	left: 25%;
	animation-delay: 3s;
}

.decoration-line {
	position: absolute;
	background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 1), transparent);
	height: 1rpx;
	animation: line-expand 6s ease-in-out infinite;
}

.decoration-line.line-1 {
	top: 25%;
	left: 10%;
	right: 60%;
	animation-delay: 0.5s;
}

.decoration-line.line-2 {
	bottom: 35%;
	left: 50%;
	right: 15%;
	animation-delay: 3.5s;
}

/* 动画定义 */
@keyframes dot-float {
	0%, 100% {
		transform: translateY(0) scale(1);
		opacity: 0.4;
	}
	50% {
		transform: translateY(-20rpx) scale(1.2);
		opacity: 0.8;
	}
}

@keyframes line-expand {
	0%, 100% {
		transform: scaleX(0);
		opacity: 0;
	}
	50% {
		transform: scaleX(1);
		opacity: 0.8;
	}
}

/* ==================== 通用样式 ==================== */
.ranking-section {
	background: rgba(26, 26, 26, 0.9);
	border: 1rpx solid rgba(212, 175, 55, 0.2);
	border-radius: 24rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
	padding: 32rpx;
	margin: 24rpx;
	overflow: hidden;
	position: relative;
}

.bottom-spacing {
	height: 160rpx;
}

/* 通用卡片样式 */
.ink-card {
	background: rgba(26, 26, 26, 0.9);
	border: 1rpx solid rgba(212, 175, 55, 0.2);
	border-radius: 24rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
	position: relative;
	overflow: hidden;
}

.svg-icon {
	filter:
		brightness(0) saturate(100%)
		invert(84%) sepia(29%) saturate(1567%)
		hue-rotate(15deg) brightness(98%) contrast(89%)
		drop-shadow(0 0 8rpx rgba(212, 175, 55, 0.4));
}
</style>