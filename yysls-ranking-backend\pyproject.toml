[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "yysls-ranking-backend"
version = "1.0.0"
description = "燕友圈榜单系统后端服务"
authors = [
    {name = "燕友圈工作室", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    # Web框架
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",

    # 数据库
    "sqlalchemy==2.0.23",
    "alembic==1.12.1",
    "aiomysql==0.2.0",
    "PyMySQL==1.1.0",
    "cryptography==41.0.7",

    # 认证与安全
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.6",

    # 微信登录
    "requests==2.31.0",
    "pydantic==2.11.7",
    "pydantic-settings==2.10.1",
    "email-validator==2.1.0",

    # 配置管理
    "python-dotenv==1.0.0",

    # 日期时间处理
    "python-dateutil==2.8.2",

    # Excel文件处理
    "openpyxl==3.1.2",
    "xlsxwriter==3.1.9",
]

[project.optional-dependencies]
dev = [
    # 测试
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "httpx==0.25.2",

    # 开发工具
    "black==23.11.0",
    "isort==5.12.0",
    "flake8==6.1.0",
]
docs = [
    # 文档
    "mkdocs==1.5.3",
    "mkdocs-material==9.4.8",
]

[project.scripts]
yysls-ranking = "app.main:app"

[tool.setuptools]
packages = ["app", "app.api", "app.api.v1", "app.api.v1.endpoints", "app.core", "app.crud", "app.models", "app.schemas", "app.services", "app.utils"]
package-dir = {"" = "."}

[tool.setuptools.package-data]
app = ["**/*.py"]

[tool.setuptools.dynamic]
version = {attr = "app.__version__"}

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
