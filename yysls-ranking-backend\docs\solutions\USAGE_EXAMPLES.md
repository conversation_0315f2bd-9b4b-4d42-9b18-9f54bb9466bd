# 技术解决方案使用示例

## 📋 概述

本文档提供技术解决方案的实际使用示例，帮助开发者快速上手和应用这些解决方案。

## 🔧 数据库会话管理修复示例

### 场景1: 新发现的会话问题

当你遇到类似错误时：
```
NameError: name 'AsyncSession' is not defined
```

**步骤1: 快速诊断**
```bash
cd yysls-ranking-backend
python scripts/verify_session_consistency.py
```

**输出示例**:
```
🔍 数据库会话一致性检查报告
==================================================

📊 统计信息:
  问题文件数: 2
  问题总数: 5

📋 问题类型分布:
  import_mismatch: 2个
  type_annotation_mismatch: 2个
  async_call_sync_method: 1个

📁 详细问题列表:

📄 app/api/v1/endpoints/users.py:
  ❌ AsyncSession导入但项目使用同步get_db (第9行)
     💡 建议: 改为: from sqlalchemy.orm import Session
  ❌ AsyncSession类型注解但使用get_db (第40行)
     💡 建议: 改为: db: Session = Depends(get_db)
```

**步骤2: 自动修复**
```bash
# 预览修复内容
python scripts/verify_session_consistency.py --fix

# 或使用快速修复脚本
bash scripts/quick_fix_session.sh --dry-run  # 预览
bash scripts/quick_fix_session.sh            # 执行修复
```

**步骤3: 验证修复**
```bash
# 再次检查
python scripts/verify_session_consistency.py

# 测试应用启动
python -c "from app.main import create_application; create_application()"
```

### 场景2: 手动修复复杂问题

对于自动修复无法处理的复杂情况：

**问题代码**:
```python
# app/api/v1/endpoints/custom.py
from sqlalchemy.ext.asyncio import AsyncSession

@router.post("/complex-operation")
async def complex_operation(
    data: ComplexData,
    db: AsyncSession = Depends(get_db)  # 类型不匹配
):
    # 混合使用异步和同步调用
    user = await user_service.get(db, data.user_id)  # 错误：异步调用同步方法
    result = await some_async_operation(user)
    updated_user = await user_service.update(db, user, result)  # 错误：异步调用同步方法
    return ResponseModel(data=updated_user)
```

**修复步骤**:

1. **修复导入**:
```python
# 修复前
from sqlalchemy.ext.asyncio import AsyncSession

# 修复后
from sqlalchemy.orm import Session
```

2. **修复类型注解**:
```python
# 修复前
db: AsyncSession = Depends(get_db)

# 修复后
db: Session = Depends(get_db)
```

3. **修复服务调用**:
```python
# 修复前
user = await user_service.get(db, data.user_id)
updated_user = await user_service.update(db, user, result)

# 修复后
user = user_service.get(db, data.user_id)
updated_user = user_service.update(db, user, result)
```

**完整修复后的代码**:
```python
# app/api/v1/endpoints/custom.py
from sqlalchemy.orm import Session

@router.post("/complex-operation")
async def complex_operation(
    data: ComplexData,
    db: Session = Depends(get_db)  # 类型匹配
):
    # 正确的同步调用
    user = user_service.get(db, data.user_id)  # 同步调用
    result = await some_async_operation(user)  # 保留真正的异步操作
    updated_user = user_service.update(db, user, result)  # 同步调用
    return ResponseModel(data=updated_user)
```

### 场景3: 批量修复多个文件

当项目中有多个文件需要修复时：

**使用自动化脚本**:
```bash
# 1. 创建备份
cp -r app/api app/api.backup

# 2. 批量修复
bash scripts/quick_fix_session.sh

# 3. 检查修复结果
python scripts/verify_session_consistency.py

# 4. 如果需要回滚
bash backups/session_fix_*/rollback.sh
```

**手动批量修复**:
```bash
# 查找所有需要修复的文件
grep -r "AsyncSession" app/api/ --include="*.py"

# 批量替换导入语句
find app/api -name "*.py" -exec sed -i 's/from sqlalchemy\.ext\.asyncio import AsyncSession/from sqlalchemy.orm import Session/g' {} \;

# 批量替换类型注解
find app/api -name "*.py" -exec sed -i 's/db: AsyncSession = Depends(get_db)/db: Session = Depends(get_db)/g' {} \;
```

## 🧪 测试验证示例

### 单元测试验证
```python
# tests/test_session_fix.py
import pytest
from fastapi.testclient import TestClient
from app.main import create_application

def test_api_endpoints_after_session_fix():
    """测试会话修复后API端点是否正常工作"""
    app = create_application()
    client = TestClient(app)
    
    # 测试健康检查
    response = client.get("/")
    assert response.status_code == 200
    
    # 测试API文档
    response = client.get("/docs")
    assert response.status_code == 200
    
    # 测试用户相关端点（需要认证的跳过或mock）
    response = client.get("/api/v1/users/me")
    assert response.status_code in [200, 401]  # 401是因为未认证，正常
```

### 集成测试验证
```python
# tests/test_database_session_integration.py
import pytest
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.user_service import UserService

def test_user_service_with_session():
    """测试用户服务与数据库会话的集成"""
    # 获取数据库会话
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # 测试服务调用
        user_service = UserService()
        users = user_service.get_multi(db, skip=0, limit=10)
        
        # 验证返回类型
        assert isinstance(users, list)
        
    finally:
        # 确保会话正确关闭
        db.close()
```

## 🚨 故障排除示例

### 问题1: 修复后仍有导入错误

**错误信息**:
```
ImportError: cannot import name 'AsyncSession' from 'sqlalchemy.orm'
```

**解决方案**:
```bash
# 检查是否有遗漏的导入
grep -r "AsyncSession" app/ --include="*.py"

# 手动修复遗漏的文件
# 将 AsyncSession 改为 Session
# 将相关导入改为 from sqlalchemy.orm import Session
```

### 问题2: 运行时会话类型错误

**错误信息**:
```
AttributeError: 'AsyncSession' object has no attribute 'execute'
```

**解决方案**:
```python
# 检查依赖注入配置
# 确保使用正确的get_db函数

# 错误的配置
db: Session = Depends(get_async_db)  # 类型不匹配

# 正确的配置
db: Session = Depends(get_db)  # 类型匹配
```

### 问题3: 测试失败

**错误信息**:
```
TypeError: object Session can't be used in 'await' expression
```

**解决方案**:
```python
# 检查测试代码中的异步调用
# 修复前
user = await user_service.get(db, user_id)

# 修复后
user = user_service.get(db, user_id)
```

## 📚 最佳实践总结

### 1. 预防措施
- 在项目开始时确定使用同步还是异步数据库会话
- 建立代码审查流程，检查会话使用一致性
- 定期运行验证脚本检查项目状态

### 2. 修复流程
1. 使用诊断工具识别问题
2. 优先使用自动修复工具
3. 手动处理复杂情况
4. 运行测试验证修复效果
5. 更新文档和最佳实践

### 3. 维护建议
- 定期更新解决方案文档
- 收集新的问题模式并添加到解决方案中
- 改进自动化工具的检测和修复能力

---

**维护者**: 开发团队  
**最后更新**: 2024-12-19  
**版本**: v1.0
