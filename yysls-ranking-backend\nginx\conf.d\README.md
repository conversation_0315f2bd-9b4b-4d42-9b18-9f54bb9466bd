# Nginx 配置文件管理

## 📁 文件说明

```
nginx/conf.d/
├── README.md                    # 本说明文件（可提交）
├── yysls.conf.template         # 生产环境配置模板（可提交）
├── yysls-dev.conf.example      # 开发环境配置示例（可提交）
├── yysls.conf                  # 实际配置文件（不提交）
└── yysls-dev.conf              # 开发配置文件（不提交）
```

## 🔒 安全策略

### ✅ 可以提交到仓库
- `*.template` - 配置模板文件
- `*.example` - 示例配置文件
- `README.md` - 说明文档

### ❌ 不应提交到仓库
- `*.conf` - 实际配置文件（包含真实域名等敏感信息）

## 🚀 使用方法

### 1. 自动生成配置
```bash
# 运行配置生成脚本
./scripts/setup-nginx.sh
```

### 2. 手动生成配置
```bash
# 复制模板文件
cp nginx/conf.d/yysls.conf.template nginx/conf.d/yysls.conf

# 修改域名
sed -i 's/{{DOMAIN_NAME}}/your-actual-domain.com/g' nginx/conf.d/yysls.conf
```

### 3. 开发环境配置
```bash
# 使用开发环境配置
cp nginx/conf.d/yysls-dev.conf.example nginx/conf.d/yysls-dev.conf
# 删除生产配置避免冲突
rm -f nginx/conf.d/yysls.conf
```

## 🔧 配置管理

### 环境切换
```bash
# 切换到生产环境
./scripts/nginx-config.sh switch

# 更新域名
./scripts/nginx-config.sh domain

# 重载配置
./scripts/nginx-config.sh reload
```

### 配置验证
```bash
# 检查配置语法
docker run --rm -v $(pwd)/nginx:/etc/nginx:ro nginx:alpine nginx -t

# 或使用管理脚本
./scripts/nginx-config.sh check
```

## 📋 配置模板说明

### 生产环境模板 (`yysls.conf.template`)
- ✅ HTTPS 强制重定向
- ✅ 严格的限流配置
- ✅ 安全头设置
- ✅ 文档访问限制
- ✅ SSL 优化配置

### 开发环境示例 (`yysls-dev.conf.example`)
- ✅ HTTP only（无SSL）
- ✅ 宽松的限流配置
- ✅ CORS 支持
- ✅ 开放文档访问
- ✅ 调试友好配置

## 🛡️ 安全注意事项

1. **域名保护**：实际配置文件包含真实域名，不应暴露
2. **路径安全**：避免暴露内部路径结构
3. **访问控制**：生产环境限制敏感端点访问
4. **SSL配置**：确保使用安全的SSL配置
5. **限流保护**：根据实际需求调整限流参数

## 🔄 部署流程

1. **生成配置**：`./scripts/setup-nginx.sh`
2. **配置SSL**：`./scripts/generate-ssl.sh`
3. **启动服务**：`docker-compose -f docker-compose.prod.yml up -d`
4. **验证访问**：测试 HTTP/HTTPS 访问
5. **监控日志**：`docker logs yysls-ranking-nginx -f`

## 📞 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   # 重新生成配置
   ./scripts/setup-nginx.sh
   ```

2. **域名解析问题**
   ```bash
   # 检查域名配置
   grep server_name nginx/conf.d/yysls.conf
   ```

3. **SSL证书问题**
   ```bash
   # 检查证书文件
   ls -la ssl/
   # 重新生成证书
   ./scripts/generate-ssl.sh
   ```

4. **权限问题**
   ```bash
   # 修复权限
   chmod 644 nginx/conf.d/yysls.conf
   ```

## 📚 相关文档

- [Nginx 官方文档](https://nginx.org/en/docs/)
- [Docker Compose 网络](https://docs.docker.com/compose/networking/)
- [SSL 最佳实践](https://wiki.mozilla.org/Security/Server_Side_TLS)
