<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燕友圈 - 播报内容管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(180deg, #1A1A1A 0%, #2A2A2A 50%, #1A1A1A 100%);
            color: #F5F5DC;
            min-height: 100vh;
            position: relative;
        }
        
        .gold-text {
            color: #D4AF37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .ink-card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 返回按钮样式 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(26, 26, 26, 0.9);
            border-color: rgba(212, 175, 55, 0.5);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        
        /* 主内容区域 */
        .main-content {
            padding: 60px 20px 140px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 表单标签样式 */
        .form-label {
            color: #D4AF37;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }
        
        /* 文本域样式 */
        .form-textarea {
            background: rgba(74, 74, 74, 0.3);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            padding: 20px;
            color: #F5F5DC;
            width: 100%;
            font-size: 16px;
            line-height: 1.6;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 300px;
            font-family: 'Noto Serif SC', serif;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: rgba(212, 175, 55, 0.5);
            background: rgba(74, 74, 74, 0.5);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
        }
        
        .form-textarea::placeholder {
            color: rgba(245, 245, 220, 0.5);
            line-height: 1.6;
        }
        
        /* 字符计数器 */
        .char-counter {
            text-align: right;
            margin-top: 8px;
            font-size: 14px;
            color: rgba(245, 245, 220, 0.6);
        }
        
        .char-counter.warning {
            color: #F59E0B;
        }
        
        .char-counter.error {
            color: #EF4444;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            border: 1px solid rgba(212, 175, 55, 0.5);
            color: #1A1A1A;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #E6C547 0%, #C9A52F 100%);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            transform: translateY(-2px);
        }
        
        .btn-primary:disabled {
            background: rgba(74, 74, 74, 0.5);
            color: rgba(245, 245, 220, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 预览区域 */
        .preview-area {
            background: rgba(74, 74, 74, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
        }
        
        .preview-title {
            color: #D4AF37;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .preview-content {
            color: #F5F5DC;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .preview-empty {
            color: rgba(245, 245, 220, 0.5);
            font-style: italic;
        }
        
        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 浮动标签栏间距 */
        .floating-tabbar-iframe {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 9999;
            pointer-events: none;
        }
        
        .floating-tabbar-iframe iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            pointer-events: auto;
        }
        
        /* 响应式设计 */
        @media (max-width: 640px) {
            .main-content {
                padding: 60px 16px 140px 16px;
            }
            
            .form-textarea {
                min-height: 250px;
                padding: 16px;
            }
            
            .preview-area {
                padding: 16px;
            }
        }
    </style>
</head>
<body class="overflow-x-hidden">
    <!-- 返回按钮 -->
    <div class="back-btn" onclick="goBack()">
        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" alt="返回"
             class="w-6 h-6" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 播报内容管理表单 -->
        <div class="ink-card p-6 scroll-animate">
            <div class="text-center mb-8">
                <img src="https://unpkg.com/lucide-static@latest/icons/megaphone.svg" alt="播报管理"
                     class="w-12 h-12 mx-auto mb-4" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                <h1 class="text-2xl font-bold gold-text mb-2">播报内容管理</h1>
                <p class="text-gray-400 text-sm">编辑和发布江湖播报内容</p>
            </div>
            
            <form id="announcementForm" class="space-y-6">
                <!-- 播报内容输入 -->
                <div>
                    <label class="form-label" for="content">播报内容 *</label>
                    <textarea id="content" name="content" class="form-textarea" 
                              placeholder="请输入播报内容...&#10;&#10;例如：&#10;各位江湖侠客，燕友圈最新排行榜已发布！&#10;本期榜单统计时间：2024年1月1日 - 2024年1月31日&#10;恭喜各位上榜的武林高手！&#10;&#10;详情请查看排行榜页面。"
                              maxlength="1000"
                              oninput="updatePreview(); updateCharCounter();"
                              required></textarea>
                    <div class="char-counter" id="charCounter">0 / 1000</div>
                </div>
                
                <!-- 实时预览 -->
                <div class="preview-area">
                    <div class="preview-title">
                        <img src="https://unpkg.com/lucide-static@latest/icons/eye.svg" alt="预览" 
                             class="w-4 h-4 mr-2" style="filter: brightness(0) saturate(100%) invert(84%) sepia(29%) saturate(1567%) hue-rotate(15deg) brightness(98%) contrast(89%);">
                        实时预览
                    </div>
                    <div class="preview-content" id="previewContent">
                        <span class="preview-empty">播报内容预览将在此显示...</span>
                    </div>
                </div>
                
                <!-- 发布按钮 -->
                <div class="pt-4">
                    <button type="submit" class="btn-primary" id="publishBtn">
                        <span id="publishText">发布播报</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 浮动标签栏组件 -->
    <div class="floating-tabbar-iframe">
        <iframe src="components/floating-tabbar.html"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                id="tabbarIframe"
                style="background: transparent; position: fixed; bottom: 0; left: 0; right: 0; height: 120px; border: none; z-index: 9999;">
        </iframe>
    </div>

    <script>
        // 播报内容管理页面控制器
        class AnnouncementManagement {
            constructor() {
                this.isPublishing = false;
                this.initPage();
            }

            initPage() {
                // 初始化滚动动画
                this.initScrollAnimations();
                
                // 初始化标签栏
                this.initTabbar();
                
                // 初始化表单验证
                this.initFormValidation();
                
                // 初始化字符计数器
                this.updateCharCounter();
            }

            // 滚动动画初始化
            initScrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, observerOptions);

                // 观察所有需要动画的元素
                document.querySelectorAll('.scroll-animate').forEach(el => {
                    observer.observe(el);
                });
            }

            // 初始化表单验证
            initFormValidation() {
                const form = document.getElementById('announcementForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handlePublish();
                });
            }

            // 更新字符计数器
            updateCharCounter() {
                const content = document.getElementById('content').value;
                const counter = document.getElementById('charCounter');
                const length = content.length;
                const maxLength = 1000;

                counter.textContent = `${length} / ${maxLength}`;

                // 根据字符数量设置样式
                counter.classList.remove('warning', 'error');
                if (length > maxLength * 0.9) {
                    counter.classList.add('error');
                } else if (length > maxLength * 0.8) {
                    counter.classList.add('warning');
                }
            }

            // 更新实时预览
            updatePreview() {
                const content = document.getElementById('content').value;
                const previewContent = document.getElementById('previewContent');

                if (content.trim()) {
                    previewContent.innerHTML = content.replace(/\n/g, '<br>');
                    previewContent.classList.remove('preview-empty');
                } else {
                    previewContent.innerHTML = '<span class="preview-empty">播报内容预览将在此显示...</span>';
                }
            }

            // 处理发布
            async handlePublish() {
                if (this.isPublishing) return;

                const content = document.getElementById('content').value.trim();

                if (!content) {
                    this.showToast('请输入播报内容', 'error');
                    return;
                }

                if (content.length > 1000) {
                    this.showToast('播报内容不能超过1000个字符', 'error');
                    return;
                }

                this.isPublishing = true;
                this.updatePublishButton(true);

                try {
                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    console.log('发布播报内容:', content);

                    this.showToast('播报内容发布成功！', 'success');

                    // 清空表单
                    setTimeout(() => {
                        this.resetForm();
                    }, 1000);

                } catch (error) {
                    console.error('发布播报失败:', error);
                    this.showToast('发布失败，请稍后重试', 'error');
                } finally {
                    this.isPublishing = false;
                    this.updatePublishButton(false);
                }
            }

            // 更新发布按钮状态
            updatePublishButton(isLoading) {
                const publishBtn = document.getElementById('publishBtn');
                const publishText = document.getElementById('publishText');

                if (isLoading) {
                    publishBtn.disabled = true;
                    publishText.textContent = '发布中...';
                } else {
                    publishBtn.disabled = false;
                    publishText.textContent = '发布播报';
                }
            }

            // 重置表单
            resetForm() {
                document.getElementById('announcementForm').reset();
                this.updateCharCounter();
                this.updatePreview();
            }

            // 显示提示消息
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300`;

                switch(type) {
                    case 'success':
                        toast.style.background = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                        break;
                    case 'error':
                        toast.style.background = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                        break;
                    default:
                        toast.style.background = 'linear-gradient(135deg, #6B7280 0%, #4B5563 100%)';
                }

                toast.textContent = message;
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translate(-50%, 0)';
                }, 100);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translate(-50%, -20px)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // 初始化标签栏
            initTabbar() {
                const tabbarIframe = document.getElementById('tabbarIframe');
                tabbarIframe.addEventListener('load', () => {
                    setTimeout(() => {
                        tabbarIframe.contentWindow.postMessage({
                            type: 'setActiveTab',
                            tab: 'user'
                        }, '*');
                    }, 100);
                });
            }
        }

        // 全局函数
        function updatePreview() {
            announcementManagement.updatePreview();
        }

        function updateCharCounter() {
            announcementManagement.updateCharCounter();
        }

        function goBack() {
            window.history.back();
        }

        // 全局变量
        let announcementManagement;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            announcementManagement = new AnnouncementManagement();
        });

        // 监听来自浮动标签栏的消息
        window.addEventListener('message', (event) => {
            if (event.origin !== window.location.origin && event.origin !== 'null') {
                return;
            }

            if (event.data && event.data.type === 'tabchange') {
                switch(event.data.tab) {
                    case 'home':
                        window.location.href = 'home.html';
                        break;
                    case 'user':
                        window.location.href = 'user.html';
                        break;
                }
            }
        });
    </script>
</body>
</html>
