"""
认证API集成测试

测试认证相关的API端点
"""
import pytest
from httpx import AsyncClient
from unittest.mock import patch

from app.models.user import User


@pytest.mark.integration
@pytest.mark.auth
class TestAuthAPI:
    """认证API测试类"""
    
    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient, test_user: User):
        """测试登录成功"""
        login_data = {
            "username": test_user.username,
            "password": "testpassword123"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "登录成功"
        assert "data" in data
        
        # 验证token数据
        token_data = data["data"]
        assert "access_token" in token_data
        assert token_data["token_type"] == "bearer"
        assert "expires_in" in token_data
        assert "user" in token_data
        
        # 验证用户信息
        user_info = token_data["user"]
        assert user_info["id"] == test_user.id
        assert user_info["username"] == test_user.username
        assert user_info["email"] == test_user.email
    
    @pytest.mark.asyncio
    async def test_login_wrong_password(self, async_client: AsyncClient, test_user: User):
        """测试登录失败（密码错误）"""
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        # 验证响应
        assert response.status_code == 401
        data = response.json()
        assert "用户名或密码错误" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_nonexistent_user(self, async_client: AsyncClient):
        """测试登录失败（用户不存在）"""
        login_data = {
            "username": "nonexistent",
            "password": "password"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        # 验证响应
        assert response.status_code == 401
        data = response.json()
        assert "用户名或密码错误" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_inactive_user(self, async_client: AsyncClient, test_user: User, db_session):
        """测试登录失败（用户已禁用）"""
        # 禁用用户
        test_user.is_active = False
        await db_session.commit()
        
        login_data = {
            "username": test_user.username,
            "password": "testpassword123"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        # 验证响应
        assert response.status_code == 401
        data = response.json()
        assert "用户账户已被禁用" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_get_current_user(self, async_client: AsyncClient, auth_headers: dict, test_user: User):
        """测试获取当前用户信息"""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "获取用户信息成功"
        
        # 验证用户信息
        user_info = data["data"]
        assert user_info["id"] == test_user.id
        assert user_info["username"] == test_user.username
        assert user_info["email"] == test_user.email
    
    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, async_client: AsyncClient):
        """测试获取当前用户信息（未认证）"""
        response = await async_client.get("/api/v1/auth/me")
        
        # 验证响应
        assert response.status_code == 403  # FastAPI HTTPBearer 返回403
    
    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self, async_client: AsyncClient):
        """测试获取当前用户信息（无效token）"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        # 验证响应
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_refresh_token(self, async_client: AsyncClient, auth_headers: dict):
        """测试刷新token"""
        response = await async_client.post("/api/v1/auth/refresh", headers=auth_headers)
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "令牌刷新成功"
        
        # 验证新token数据
        token_data = data["data"]
        assert "access_token" in token_data
        assert token_data["token_type"] == "bearer"
        assert "expires_in" in token_data
        assert "user" in token_data
    
    @pytest.mark.asyncio
    async def test_logout(self, async_client: AsyncClient, auth_headers: dict):
        """测试用户登出"""
        response = await async_client.post("/api/v1/auth/logout", headers=auth_headers)
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "登出成功"
    
    @pytest.mark.asyncio
    @pytest.mark.wechat
    async def test_wechat_login_success(self, async_client: AsyncClient):
        """测试微信登录成功"""
        with patch('app.utils.wechat.wechat_api.login_with_code') as mock_wechat:
            # 模拟微信API响应
            mock_wechat.return_value = {
                "access_token": "mock_access_token",
                "openid": "mock_openid_123",
                "nickname": "微信用户",
                "headimgurl": "https://example.com/avatar.jpg",
                "sex": 1,
                "country": "中国",
                "province": "北京",
                "city": "北京"
            }
            
            wechat_data = {
                "code": "mock_wechat_code"
            }
            
            response = await async_client.post("/api/v1/auth/wechat-login", json=wechat_data)
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            assert data["code"] == 200
            assert data["message"] == "微信登录成功"
            
            # 验证token数据
            token_data = data["data"]
            assert "access_token" in token_data
            assert "user" in token_data
            
            # 验证用户信息
            user_info = token_data["user"]
            assert user_info["wechat_openid"] == "mock_openid_123"
            assert user_info["nickname"] == "微信用户"
    
    @pytest.mark.asyncio
    @pytest.mark.wechat
    async def test_wechat_login_missing_code(self, async_client: AsyncClient):
        """测试微信登录失败（缺少授权码）"""
        wechat_data = {}
        
        response = await async_client.post("/api/v1/auth/wechat-login", json=wechat_data)
        
        # 验证响应
        assert response.status_code == 400
        data = response.json()
        assert "缺少微信授权码" in data["detail"]
    
    @pytest.mark.asyncio
    @pytest.mark.wechat
    async def test_wechat_login_api_error(self, async_client: AsyncClient):
        """测试微信登录失败（微信API错误）"""
        with patch('app.utils.wechat.wechat_api.login_with_code') as mock_wechat:
            # 模拟微信API错误
            from app.utils.wechat import WeChatAPIError
            mock_wechat.side_effect = WeChatAPIError("微信API调用失败")
            
            wechat_data = {
                "code": "invalid_code"
            }
            
            response = await async_client.post("/api/v1/auth/wechat-login", json=wechat_data)
            
            # 验证响应
            assert response.status_code == 400
            data = response.json()
            assert "微信登录失败" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_validation_error(self, async_client: AsyncClient):
        """测试登录参数验证错误"""
        # 缺少必需字段
        login_data = {
            "username": "testuser"
            # 缺少password字段
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        # 验证响应
        assert response.status_code == 422  # Pydantic验证错误
        data = response.json()
        assert "detail" in data
        
        # 验证错误详情
        errors = data["detail"]
        assert any(error["loc"] == ["password"] for error in errors)
    
    @pytest.mark.asyncio
    async def test_auth_flow_complete(self, async_client: AsyncClient, test_user: User):
        """测试完整的认证流程"""
        # 1. 登录
        login_data = {
            "username": test_user.username,
            "password": "testpassword123"
        }
        
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        token_data = login_response.json()["data"]
        access_token = token_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # 2. 获取用户信息
        user_response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert user_response.status_code == 200
        
        # 3. 刷新token
        refresh_response = await async_client.post("/api/v1/auth/refresh", headers=headers)
        assert refresh_response.status_code == 200
        
        # 4. 登出
        logout_response = await async_client.post("/api/v1/auth/logout", headers=headers)
        assert logout_response.status_code == 200
