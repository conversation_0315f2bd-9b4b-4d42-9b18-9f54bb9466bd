# Excel文件上传和解析功能API文档

## 📋 功能概述

燕友圈榜单系统新增Excel文件上传和解析功能，支持通过Excel文件批量导入榜单明细数据，提高数据录入效率。

### 主要功能
- **Excel模板下载**：提供标准格式的Excel模板文件
- **文件验证**：验证上传的Excel文件格式和内容
- **数据解析**：解析Excel文件中的榜单明细数据
- **批量导入**：在创建或更新榜单时自动导入Excel数据
- **错误处理**：详细的数据验证和错误提示

## 🔧 API接口列表

### 1. 下载Excel模板

**接口地址**：`GET /api/v1/upload/excel/template`

**请求参数**：
- `ranking_type` (query, optional): 榜单类型，默认为 `5_person`
  - `5_person`: 5人榜单
  - `10_person`: 10人榜单

**响应**：Excel文件流

**示例**：
```bash
curl -X GET "https://api.example.com/api/v1/upload/excel/template?ranking_type=5_person" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o "榜单明细模板.xlsx"
```

### 2. 验证Excel文件

**接口地址**：`POST /api/v1/upload/excel/validate`

**请求参数**：
- `file` (form-data): Excel文件 (.xlsx 或 .xls)

**响应示例**：
```json
{
  "code": 200,
  "message": "Excel文件验证通过",
  "data": {
    "filename": "榜单明细.xlsx",
    "size": 15360,
    "size_mb": 0.01,
    "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "is_valid": true
  }
}
```

### 3. 解析Excel文件

**接口地址**：`POST /api/v1/upload/excel/parse`

**请求参数**：
- `file` (form-data): 榜单明细Excel文件

**响应示例**：
```json
{
  "code": 200,
  "message": "Excel文件解析成功，共解析3条记录",
  "data": {
    "filename": "榜单明细.xlsx",
    "total_records": 3,
    "ranking_details": [
      {
        "rank_start": 1,
        "rank_end": 5,
        "completion_time": "05:30:00",
        "completion_seconds": 330,
        "participant_count": 5,
        "team_info": "队伍A,队伍B,队伍C,队伍D,队伍E"
      },
      {
        "rank_start": 6,
        "rank_end": 10,
        "completion_time": "06:15:00",
        "completion_seconds": 375,
        "participant_count": 5,
        "team_info": "队伍F,队伍G,队伍H,队伍I,队伍J"
      }
    ],
    "parsed_at": "2024-01-15T10:30:00"
  }
}
```

### 4. 临时上传Excel文件

**接口地址**：`POST /api/v1/upload/excel/upload-temp`

**请求参数**：
- `file` (form-data): Excel文件

**响应示例**：
```json
{
  "code": 200,
  "message": "Excel文件上传成功",
  "data": {
    "original_filename": "榜单明细.xlsx",
    "temp_filename": "abc123_456.xlsx",
    "temp_filepath": "/app/uploads/temp/excel/abc123_456.xlsx",
    "file_size": 15360,
    "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "uploaded_by": 1,
    "uploaded_at": "2024-01-15T10:30:00",
    "expires_at": "2024-01-15T23:59:59"
  }
}
```

### 5. 删除临时Excel文件

**接口地址**：`DELETE /api/v1/upload/excel/temp/{temp_filename}`

**路径参数**：
- `temp_filename`: 临时文件名

**响应示例**：
```json
{
  "code": 200,
  "message": "临时文件删除成功",
  "data": null
}
```

### 6. 清理过期临时文件

**接口地址**：`GET /api/v1/upload/excel/temp/cleanup`

**响应示例**：
```json
{
  "code": 200,
  "message": "清理完成，删除2个过期文件",
  "data": {
    "deleted_count": 2
  }
}
```

## 🎯 榜单管理集成

### 创建榜单并导入Excel数据

**接口地址**：`POST /api/v1/rankings`

**增强的请求参数**：
```json
{
  "name": "第1期5人竞速榜单",
  "period": 1,
  "ranking_type": "5_person",
  "start_time": "2024-01-15T09:00:00",
  "end_time": "2024-01-15T18:00:00",
  "team_size_limit": 5,
  "excel_file_path": "/app/uploads/temp/excel/abc123_456.xlsx",
  "auto_import_details": true
}
```

**新增字段说明**：
- `excel_file_path`: Excel文件临时路径（通过临时上传接口获得）
- `auto_import_details`: 是否自动导入Excel中的榜单明细数据

### 更新榜单并导入Excel数据

**接口地址**：`PUT /api/v1/rankings/{ranking_id}`

**增强的请求参数**：
```json
{
  "name": "更新后的榜单名称",
  "excel_file_path": "/app/uploads/temp/excel/def456_789.xlsx",
  "auto_import_details": true,
  "replace_existing_details": true
}
```

**新增字段说明**：
- `excel_file_path`: Excel文件临时路径
- `auto_import_details`: 是否自动导入Excel中的榜单明细数据
- `replace_existing_details`: 是否替换现有的榜单明细数据

## 📊 Excel模板格式

### 必需列
| 列名 | 数据类型 | 是否必需 | 说明 |
|------|----------|----------|------|
| 排名开始 | 整数 | 是 | 排名区间开始值，必须大于0 |
| 排名结束 | 整数 | 是 | 排名区间结束值，必须大于等于开始值 |
| 完成时间(MM:SS) | 时间格式 | 是 | 格式为MM:SS，如05:30表示5分30秒 |
| 参与人数 | 整数 | 是 | 当前时间区间的参与人数，必须大于0 |
| 队伍信息 | 文本 | 否 | 队伍信息，多个队伍用逗号分隔 |

### 数据验证规则
1. **排名范围**：排名开始不能大于排名结束
2. **时间格式**：支持MM:SS格式的字符串或Excel时间对象
3. **数据类型**：数值列必须为整数，不能为空
4. **文件格式**：仅支持.xlsx和.xls格式
5. **文件大小**：最大10MB

### 示例数据
```
排名开始 | 排名结束 | 完成时间(MM:SS) | 参与人数 | 队伍信息
1       | 5       | 05:30          | 5       | 队伍A,队伍B,队伍C,队伍D,队伍E
6       | 10      | 06:15          | 5       | 队伍F,队伍G,队伍H,队伍I,队伍J
11      | 15      | 07:00          | 5       | 队伍K,队伍L,队伍M,队伍N,队伍O
```

## 🔄 完整使用流程

### 1. 下载模板
```bash
curl -X GET "https://api.example.com/api/v1/upload/excel/template?ranking_type=5_person" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o "榜单明细模板.xlsx"
```

### 2. 填写数据并上传
```bash
curl -X POST "https://api.example.com/api/v1/upload/excel/upload-temp" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@榜单明细.xlsx"
```

### 3. 创建榜单并导入数据
```bash
curl -X POST "https://api.example.com/api/v1/rankings" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "第1期5人竞速榜单",
    "period": 1,
    "ranking_type": "5_person",
    "start_time": "2024-01-15T09:00:00",
    "end_time": "2024-01-15T18:00:00",
    "team_size_limit": 5,
    "excel_file_path": "/app/uploads/temp/excel/abc123_456.xlsx",
    "auto_import_details": true
  }'
```

## ⚠️ 错误处理

### 常见错误码
- `400`: 文件格式错误、数据验证失败
- `401`: 未授权访问
- `404`: 文件不存在
- `413`: 文件过大
- `422`: 请求参数错误
- `500`: 服务器内部错误

### 错误示例
```json
{
  "detail": "Excel数据解析错误:\n第2行 排名开始 必须为整数\n第3行 完成时间格式错误，应为 MM:SS\n第4行 排名开始不能大于排名结束"
}
```

## 🔒 安全考虑

1. **文件类型验证**：仅允许.xlsx和.xls格式
2. **文件大小限制**：最大10MB
3. **临时文件管理**：临时文件24小时后自动清理
4. **权限控制**：需要有效的认证token
5. **文件隔离**：临时文件按用户ID隔离存储

## 📝 注意事项

1. **临时文件有效期**：上传的临时文件在当天结束时过期
2. **数据覆盖**：使用`replace_existing_details=true`会删除现有的榜单明细
3. **事务处理**：导入过程中如果出错，会回滚所有更改
4. **性能考虑**：建议单次导入数据不超过1000条记录
5. **编码格式**：Excel文件应使用UTF-8编码以避免中文乱码
