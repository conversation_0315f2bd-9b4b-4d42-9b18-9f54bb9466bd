# 燕友圈榜单系统 - API文档说明

## 📚 文档概览

本目录包含燕友圈榜单系统后端API的完整文档和相关工具。

### 文档文件

| 文件 | 描述 | 用途 |
|------|------|------|
| `API_COMPLETE_DOCUMENTATION.md` | 完整API文档 | 开发者参考手册 |
| `openapi.json` | OpenAPI规范文件 | 自动生成的API规范 |
| `postman_collection.json` | Postman集合 | API测试工具 |
| `database_design.md` | 数据库设计文档 | 数据库结构参考 |
| `DATABASE_SQL.md` | 数据库SQL脚本 | 建表和初始化脚本 |

### 技术解决方案

| 目录/文件 | 描述 | 用途 |
|-----------|------|------|
| `solutions/` | 技术问题解决方案库 | 问题诊断和修复指南 |
| `solutions/database_session_management_fix.md` | 数据库会话管理修复 | AsyncSession问题解决 |

### 工具脚本

| 脚本 | 描述 | 用途 |
|------|------|------|
| `../scripts/generate_api_docs.py` | API文档生成器 | 自动生成文档 |
| `../scripts/test_api_endpoints.py` | API端点测试器 | 验证API可用性 |
| `../scripts/generate_postman_collection.py` | Postman集合生成器 | 生成测试集合 |

## 🚀 快速开始

### 1. 查看API文档

直接阅读 `API_COMPLETE_DOCUMENTATION.md` 文件，包含：
- 所有API端点的详细说明
- 请求参数和响应格式
- 认证方式和错误处理
- 完整的示例代码

### 2. 使用Postman测试API

1. **生成Postman集合**：
   ```bash
   cd yysls-ranking-backend
   python scripts/generate_postman_collection.py
   ```

2. **导入到Postman**：
   - 打开Postman
   - 点击 "Import"
   - 选择生成的 `docs/postman_collection.json` 文件
   - 导入完成后即可使用

3. **配置环境变量**：
   - `base_url`: API基础地址（默认：http://localhost:8000）
   - `access_token`: 访问令牌（登录后自动设置）

### 3. 测试API端点

运行API端点测试器验证服务是否正常：

```bash
cd yysls-ranking-backend
python scripts/test_api_endpoints.py
```

可选参数：
```bash
# 指定不同的API地址
python scripts/test_api_endpoints.py --base-url http://your-api-server.com
```

### 4. 生成最新文档

当API发生变化时，重新生成文档：

```bash
cd yysls-ranking-backend
python scripts/generate_api_docs.py
```

## 📖 API文档结构

### 认证模块
- 用户登录（用户名密码）
- 微信登录
- 获取当前用户信息
- 刷新Token
- 用户登出

### 榜单管理
- 获取榜单列表（支持分页和筛选）
- 创建榜单
- 获取榜单详情
- 更新榜单
- 删除榜单

### 用户管理
- 获取用户列表
- 创建用户
- 获取用户详情
- 更新用户信息

### 赞助商管理
- 获取赞助商列表
- 创建赞助商
- 获取赞助商详情
- 更新赞助商信息

### 系统健康检查
- 根路径检查
- 健康状态检查

## 🔐 认证说明

### Bearer Token认证

大部分API需要在请求头中包含Bearer Token：

```http
Authorization: Bearer <your_access_token>
```

### 获取Token

1. **用户名密码登录**：
   ```http
   POST /api/v1/auth/login
   Content-Type: application/json
   
   {
     "username": "your_username",
     "password": "your_password"
   }
   ```

2. **微信登录**：
   ```http
   POST /api/v1/auth/wechat-login
   Content-Type: application/json
   
   {
     "code": "wechat_auth_code"
   }
   ```

### Token管理

- **有效期**：30分钟（可配置）
- **刷新**：使用 `POST /api/v1/auth/refresh` 刷新Token
- **登出**：使用 `POST /api/v1/auth/logout` 使Token失效

## 📊 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "获取数据成功",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

## ❌ 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权 | 检查Token是否有效 |
| 403 | 权限不足 | 确认用户权限 |
| 404 | 资源不存在 | 检查请求路径和资源ID |
| 422 | 参数验证失败 | 检查参数类型和格式 |
| 500 | 服务器错误 | 联系系统管理员 |

## 🛠️ 开发工具

### 1. API文档生成

自动从FastAPI应用生成OpenAPI文档：

```bash
python scripts/generate_api_docs.py
```

生成文件：
- `docs/openapi.json` - OpenAPI规范
- `docs/API_DOCUMENTATION.md` - Markdown文档

### 2. API测试

验证所有API端点是否可访问：

```bash
python scripts/test_api_endpoints.py
```

测试内容：
- 健康检查端点
- 认证端点
- 业务功能端点
- OpenAPI文档端点

### 3. Postman集合

生成用于API测试的Postman集合：

```bash
python scripts/generate_postman_collection.py
```

特性：
- 自动设置认证Token
- 预配置请求参数
- 环境变量支持

## 📝 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 完整的API文档
- Postman集合支持
- 自动化测试工具

## 🤝 贡献指南

1. API变更时，请更新相应的文档
2. 运行测试确保API正常工作
3. 重新生成Postman集合
4. 更新版本号和更新日志

## 📞 联系方式

如有问题或建议，请联系开发团队。
