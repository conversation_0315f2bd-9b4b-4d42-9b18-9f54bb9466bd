你是一位资深的前端工程师、UI/UX设计师和产品经理，需要为微信小程序《燕友圈》APP创建完整的高保真原型设计。请基于已有的设计风格 `doc/设计风格.json` 原型图输入到 `prototype/user.html`，按照以下具体步骤完成原型设计：

目前只需要设计一个我的页面：
首页设计需求：使用 D:\A_outsource\yysls-ranking-app\static\home\homeBg.webp 图作为整个首页背景长图
首页模块需求: 用户信息（区分管理员与普通用户,标签区分）-> 功能列表（所有用户，包含关于我们）-> 管理员功能列表(管理员可见)->退出按钮


原型输出规则：
1、用户体验分析：先分析这个 App 的主要功能和用户需求，确定核心交互逻辑。
2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。
3、高保真 UI 设计：作为 UI 设计师，设计贴近真实 iOS/Android 设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。
4、HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成所有原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。拆分代码文件，保持结构清晰：
5、每个界面应作为独立的 HTML 文件存放，例如 home.html、profile.html、settings.html 等。
- 真实感增强：
- 界面尺寸应模拟 iPhone 15 Pro，并让界面圆角化，使其更像真实的手机界面。
- 使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）。
- 添加顶部状态栏（模拟 iOS 状态栏），并包含 App 导航栏（类似 iOS 底部 Tab Bar）。
6.图标使用：Lucide Static CDN 方式引入，如`https://unpkg.com/lucide-static@latest/icons/XXX.svg`，且输出md文档 包含图标颜色，链接，使用模块。且添加下载脚本
7.设计风格:要有古典武侠水墨风格,基于已有的设计风格 `doc/设计风格.json` 设计与参考 `prototype/home.html`

/* 给盒子添加透视效果 */
 {
  backdrop-filter: none !important;
  background: rgba(26, 26, 26, 0.9) !important;
}