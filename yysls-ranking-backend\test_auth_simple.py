#!/usr/bin/env python3
"""
简单的认证测试
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.user_service import UserService

def test_auth():
    """测试认证"""
    db = SessionLocal()
    user_service = UserService()
    
    try:
        print("测试认证功能...")
        
        # 测试认证
        user = user_service.authenticate(db, "admin", "123456")
        
        if user:
            print(f"✅ 认证成功！")
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   角色: {user.role}")
        else:
            print("❌ 认证失败")
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_auth()
