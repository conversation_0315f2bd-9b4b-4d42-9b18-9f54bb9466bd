<template>
	<view class="page">
		<lui-loading v-if="commonStore.loadingShow"></lui-loading>
		<slot />
	</view>
</template>

<script setup>  
	import {
		ref,
		watch
	} from 'vue'
	import {
		useCommonStore
	} from '@/stores/common.js';
	const commonStore = useCommonStore()
	console.log(commonStore.loadingShow,'commonStore')
	// const props = defineProps({
	// 	value: {
	// 		type: Boolean,
	// 		default: false
	// 	}
	// })
	// const show = ref(props.value);
	// watch(() => props.value, (val) => {
	// 	show.value = val;
	// });
</script>

<style>
</style>